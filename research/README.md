# Introduction
The Research Directory is designed to support research members by providing a dedicated repository for their work. This repository serves the following objectives:

- It serves as a storage space for plotting, visualization, and logging metric functions that are not required for production serving.
- The majority of the code in this directory will be in `Jupyter format`, enabling easy sharing of model workings, current model status, and research progress.
- The research folder will have a `longer branch lifespan` compared to engineering features, as research activities typically require more time. It will not follow the same principle to the default branch lifespan (Usually 2 weeks span).
- `The code convention and code style will be disregarded` in this specific folder to facilitate rapid proof of concept (PoC) and idea experimentation.