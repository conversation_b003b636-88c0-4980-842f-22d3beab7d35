import tempfile

from fastapi import UploadFile
from openai import AsyncOpenAI
from src.core import ResumeData, parse_match_score, parse_resume

from .config import settings
from .schemas import MatchScorePayload

# --- Configuration ---
openai_client = AsyncOpenAI(
    api_key=settings.openai_api_key,
    base_url=settings.openai_base_url,
)


async def get_resume_data(resume_file: UploadFile) -> ResumeData:
    """
    Calls the LLM backend and streams its response using the OpenAI library.
    """
    with tempfile.NamedTemporaryFile(delete=True, suffix=".tmp") as tmp:
        # Read file content as bytes
        contents = await resume_file.read()
        tmp.write(contents)
        tmp_path = tmp.name

        result = await parse_resume(tmp_path, openai_client)
    return result


async def get_match_score(match_score_payload: MatchScorePayload):
    """
    Calls the LLM backend and streams its response using the OpenAI library.
    """
    resume_data = match_score_payload.resume_data
    jd_data = match_score_payload.job_description
    weight_reference = match_score_payload.weight_reference
    return await parse_match_score(
        resume_data, jd_data, openai_client, weight_reference
    )
