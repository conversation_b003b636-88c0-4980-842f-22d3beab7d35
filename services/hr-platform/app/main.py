import secrets
from typing import Annotated

import uvicorn
from fastapi import Depends, FastAP<PERSON>, File, HTTPException, Request, UploadFile, status
from fastapi.security import H<PERSON><PERSON><PERSON>asic, HTTPBasicCredentials
from fastapi.middleware.cors import CORSMiddleware

from .config import settings
from .core import get_match_score, get_resume_data
from .schemas import MatchScorePayload, MatchScoreResponse

app = FastAPI()
security = HTTPBasic()
origins = [
    "http://localhost",
    "http://127.0.0.1",
]


app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_current_username(
    credentials: Annotated[HTTPBasicCredentials, Depends(security)],
):
    current_username_bytes = credentials.username.encode("utf8")
    correct_username_bytes = settings.admin_username.encode("utf8")
    is_correct_username = secrets.compare_digest(
        current_username_bytes, correct_username_bytes
    )
    current_password_bytes = credentials.password.encode("utf8")
    correct_password_bytes = settings.admin_password.encode("utf8")
    is_correct_password = secrets.compare_digest(
        current_password_bytes, correct_password_bytes
    )
    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


@app.get("/")
async def get_page(request: Request):
    return {"status": "ok"}


@app.post("/resume/parse")
async def parse_resume_endpoint(
    username: Annotated[str, Depends(get_current_username)],
    file: UploadFile = File(...),
):
    resume_data = await get_resume_data(file)
    return resume_data


@app.post("/analyst/match-score")
async def parse_match_score_endpoint(
    username: Annotated[str, Depends(get_current_username)], payload: MatchScorePayload
):
    attribute_scores, total_score = await get_match_score(payload)
    return MatchScoreResponse(score=total_score, attribute_scores=attribute_scores)


if __name__ == "__main__":
    # This is for running directly, e.g., python -m app.main
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
