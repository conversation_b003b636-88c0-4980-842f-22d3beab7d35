import base64
import os
from datetime import datetime
from typing import List, Optional

import pymupdf
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator


class ResumeExperience(BaseModel):
    company: str = Field(..., description="Name of the company.")
    position: str = Field(..., description="Job title or position.")
    start_date: Optional[str] = Field(
        ..., description="Start date of employment, preferably YYYY-MM."
    )
    end_date: Optional[str] = Field(
        ...,
        description="End date of employment, preferably YYYY-MM. Use 'Present' if current.",
    )
    description: Optional[str] = Field(
        ...,
        description="Summary of the position or role if available. Maximum 4-5 sentences and each sentence should be no more than 10 words.",
    )

    @field_validator("end_date")
    def convert_end_date(cls, end_date):
        if not end_date:
            return None
        if end_date.lower() in ["n/a", "not provided", "not found"]:
            return None

        return (
            end_date
            if end_date.lower() not in ["present", "current", "now"]
            else datetime.now().strftime("%Y-%m")
        )

    @field_validator("start_date")
    def convert_start_date(cls, start_date):
        if not start_date:
            return None
        if start_date.lower() in ["n/a", "not provided", "not found"]:
            return None
        return (
            start_date
            if start_date.lower() not in ["present", "current", "now"]
            else datetime.now().strftime("%Y-%m")
        )


class ResumeEducation(BaseModel):
    institution: str = Field(..., description="Name of the educational institution.")
    degree: Optional[str] = Field(
        ..., description="Degree obtained (e.g., Bachelor of Science)."
    )
    start_date: Optional[str] = Field(
        ..., description="Start date of education, preferably YYYY-MM."
    )
    end_date: Optional[str] = Field(
        ...,
        description="End date of education or graduation date, preferably YYYY-MM. Use 'Present' if current.",
    )
    major: str = Field(..., description="Major field of study.")
    gpa: Optional[float] = Field(..., description="GPA of the student, if available.")
    ielts: Optional[float] = Field(
        ..., description="IELTS score of the student, if available."
    )
    sat_score: Optional[float] = Field(
        ..., description="SAT score of the student, if available."
    )

    @field_validator("end_date")
    def convert_end_date(cls, end_date):
        if not end_date:
            return None
        if end_date.lower() in ["n/a", "not provided", "not found"]:
            return None
        return (
            end_date
            if end_date.lower() not in ["present", "current", "now"]
            else datetime.now().strftime("%Y-%m")
        )

    @field_validator("start_date")
    def convert_start_date(cls, start_date):
        if not start_date:
            return None
        if start_date.lower() in ["n/a", "not provided", "not found"]:
            return None
        return start_date

    @field_validator("degree")
    def convert_degree(cls, degree):
        if not degree:
            return None
        return (
            degree
            if degree.lower() not in ["n/a", "not found", "not provided"]
            else None
        )


class ResumeCertification(BaseModel):
    institution: str = Field(..., description="Name of the educational institution.")
    certificate: str = Field(
        ...,
        description="Certificate or Course name obtained (e.g.,  Performance Media).",
    )
    start_date: Optional[str] = Field(
        ..., description="Start date of education, preferably YYYY-MM."
    )
    end_date: Optional[str] = Field(
        ...,
        description="End date of education or graduation date, preferably YYYY-MM. Use 'Present' if current.",
    )

    @field_validator("end_date")
    def convert_end_date(cls, end_date):
        if not end_date:
            return None
        if end_date.lower() in ["n/a", "not provided", "not found"]:
            return None
        return (
            end_date
            if end_date.lower() not in ["present", "current", "now"]
            else datetime.now().strftime("%Y-%m")
        )

    @field_validator("start_date")
    def convert_start_date(cls, start_date):
        if not start_date:
            return None
        if start_date.lower() in ["n/a", "not provided", "not found"]:
            return None
        return start_date


class ResumeData(BaseModel):
    name: str = Field(..., description="Full name of the candidate.")
    email: str = Field(..., description="Email address of the candidate.")
    phone: str = Field(..., description="Phone number of the candidate.")
    address: Optional[str] = Field(
        ..., description="Full address of the candidate. If not found, use 'N/A'."
    )
    dob: Optional[str] = Field(
        ...,
        description="Date of birth of the candidate, preferably DD-MM-YYYY. If not found, use 'N/A'.",
    )
    cccd: Optional[str] = Field(
        ...,
        description="identifier number of the candidate, could be CCCD or CMND number. If not found, use 'N/A'.",
    )
    experiences: List[ResumeExperience] = Field(
        ..., description="List of professional experiences."
    )
    educations: List[ResumeEducation] = Field(
        ..., description="List of educational qualifications."
    )
    certifications: List[ResumeCertification] = Field(
        ..., description="List of certifications."
    )
    soft_skills: List[str] = Field(..., description="List of soft skills.")
    technical_skills: List[str] = Field(
        ...,
        description="List of technical skills with respective years of experience. For example: 1 year of experience in Python.",
    )

    @field_validator("address")
    def convert_address(cls, address):
        return (
            address
            if address.lower() not in ["n/a", "not found", "not provided"]
            else None
        )

    @field_validator("dob")
    def convert_dob(cls, dob):
        if not dob:
            return None
        return dob if dob.lower() not in ["n/a", "not found", "not provided"] else None

    @field_validator("cccd")
    def convert_cccd(cls, cccd):
        if not cccd:
            return None
        return (
            cccd if cccd.lower() not in ["n/a", "not found", "not provided"] else None
        )


class JobDescription(BaseModel):
    title: str = Field(..., description="Job title or position.")
    description: str = Field(
        ...,
        description="Job description or role description.",
    )
    location: str = Field(..., description="Job location.")
    position_level: str = Field(
        ...,
        description="Job position level, such as Junior, Mid, Senior.",
    )
    industry: str = Field(..., description="Job industry.")


class MatchScore(BaseModel):
    degree_score: int = Field(
        ...,
        description="Score if the candidate match with the requirement or not. Range from 0 to 100.",
    )
    experience_score: int = Field(
        ...,
        description="Score if the candidate match with the requirement or not. Range from 0 to 100.",
    )
    certification_score: int = Field(
        ...,
        description="Score if the candidate match with the requirement or not. Range from 0 to 100.",
    )
    soft_skill_score: int = Field(
        ...,
        description="Score if the candidate match with the requirement or not. Range from 0 to 100.",
    )
    technical_skill_score: int = Field(
        ...,
        description="Score if the candidate match with the requirement or not. Range from 0 to 100.",
    )


class MatchScoreWeight(BaseModel):
    degree_score: float = 0.2
    experience_score: float = 0.3
    certification_score: float = 0.2
    soft_skill_score: float = 0.05
    technical_skill_score: float = 0.25


def convert_pdf_to_img(doc):
    max_page = 5
    base64_images = []
    for idx, page in enumerate(doc):
        if idx >= max_page:
            break
        pix = page.get_pixmap(dpi=150)
        img_byte_arr = pix.tobytes(output="png")
        base64_image = base64.b64encode(img_byte_arr).decode("utf-8")
        base64_images.append(f"data:image/png;base64,{base64_image}")
    return base64_images


async def parse_resume(
    resume_file: str, openai_client: AsyncOpenAI
) -> Optional[ResumeData]:
    doc = pymupdf.open(resume_file)
    max_page = 5
    resume_text = ""
    for idx, page in enumerate(doc):
        if idx >= max_page:
            break
        text = page.get_text()
        resume_text += "\n" + text

    if len(resume_text.split()) < 50:
        resume_images_base64 = convert_pdf_to_img(doc)
        if not resume_images_base64:
            return None

        user_msg = [
            {
                "type": "text",
                "text": "Parse the resume information from the following image(s):",
            }
        ]
        for img_b64 in resume_images_base64:
            user_msg.append(
                {
                    "type": "image_url",
                    "image_url": {
                        "url": img_b64,
                    },
                }
            )
    else:
        user_msg = f"Parse the following resume:\n\n{resume_text}"

    completion = await openai_client.beta.chat.completions.parse(
        model=os.environ["model_name"],
        messages=[
            {
                "role": "system",
                "content": """You are an expert resume parser. Your task is to extract information from the resume text and structure it according to the provided schema.
                Ensure all date formats are consistent if possible (e.g., YYYY-MM or Month YYYY). If a specific piece of information for a non-list field (like address or dob) is not found, use 'N/A'.
                For experiences section, please merge the section that have the same both **company and position**, concatenate the start and end date.
                For lists like experiences or educations, if none are found, return an empty list.
                NOTE: please keep the information as it is in the resume. Do not add any extra information.""",
            },
            {
                "role": "user",
                "content": user_msg,
            },
        ],
        timeout=180,
        response_format=ResumeData,
        temperature=0.2,
    )
    result: ResumeData = completion.choices[0].message.parsed
    if result and result.experiences:
        result.experiences = sorted(
            result.experiences,
            key=lambda x: x.end_date if x.end_date is not None else datetime.min,
            reverse=True,
        )
    if result and result.educations:
        result.educations = sorted(
            result.educations,
            key=lambda x: x.end_date if x.end_date is not None else datetime.min,
            reverse=True,
        )
    return result


async def parse_match_score(
    resume_data: ResumeData,
    jd_data: JobDescription,
    openai_client: AsyncOpenAI,
    weight_reference: Optional[MatchScoreWeight] = None,
) -> tuple[MatchScore, int]:
    if not weight_reference:
        weight_reference = MatchScoreWeight()
    content = "Here is the resume information:\n\n"
    for field in resume_data.model_dump():
        content += f"{field}: {getattr(resume_data, field)}\n"
    content += "\n\nHere is the job description:\n\n"
    for field in jd_data.model_dump():
        content += f"{field}: {getattr(jd_data, field)}\n"

    completion = await openai_client.beta.chat.completions.parse(
        model=os.environ["model_name"],
        messages=[
            {
                "role": "system",
                "content": """You are an expert HR AI. Your task is to rank the score of how well the candidate match with the job description.
                    ## Scoring Guide:
                    It's ok to say candidate does not match the requirement.
                    Degree Section: Prioritize major than degree level. Candidate with degrees more directly relevant to the required degree should receive higher score, even if their degree level is lower.
                    Experience Section: Candidate with more relevant experience field get higher score.
                    Technical Skills Section: Candidate with more relevant technical skills get higher score.
                    Certificates Section: Candidate with required certificates get higher score. Candidate without required certificates get no score. Candidate with related certificates to the position get medium score.
                    Soft Skills Section: Prioritize foreign language and leadership skills. Candidate with more relevant soft skills get higher score.
                    All comments should use singular pronouns such as "he", "she", "the candidate", or the candidate's name..""",
            },
            {
                "role": "user",
                "content": content,
            },
        ],
        timeout=180,
        response_format=MatchScore,
        temperature=0.2,
    )
    result: MatchScore = completion.choices[0].message.parsed
    final_score = 0
    for section, weight in weight_reference.model_dump().items():
        if hasattr(result, section):
            final_score += getattr(result, section) * weight
    final_score = final_score
    return result, final_score
