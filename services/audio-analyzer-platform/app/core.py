# import tempfile
# from fastapi import UploadFile

# from src.core import parse_invoice, InvoiceData



# async def get_invoice_data(invoice_file: UploadFile) -> InvoiceData:
#     """
#     Calls the LLM backend and streams its response using the OpenAI library.
#     """
#     with tempfile.NamedTemporaryFile(delete=True, suffix=".tmp") as tmp:
#         # Read file content as bytes
#         contents = await invoice_file.read()
#         tmp.write(contents)
#         tmp_path = tmp.name

#         result = await parse_invoice(tmp_path)
#     return result

