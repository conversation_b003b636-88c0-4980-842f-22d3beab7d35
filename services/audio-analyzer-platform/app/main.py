import os
import sys
import json
import uvicorn
import shutil
import librosa
import soundfile as sf
from numpy import ndarray
from io import BytesIO
from dependency_injector.wiring import Provide, inject
from typing import Dict, Annotated, List
from fastapi import FastAPI, Request, Depends, UploadFile, File, Form

from src.module.application_container import ApplicationContainer
from src.utils.util import create_injector

# from src.controller import audio_analyzer_controller
from src.controller.requests.analyze_request import AnalyzeRequest
from src.controller import audio_analyzer_controller
from src.controller.audio_analyzer_controller import get_audio_analyzer_service

from src.service.audio_analyzer_service import AudioAnalyzerService


app = FastAPI(title="Audio Analyzer API")

injector = create_injector(
    ApplicationContainer(),
    [
        sys.modules[__name__],
        audio_analyzer_controller,
        uvicorn
    ]
)
audio_analyzer_service = get_audio_analyzer_service()



@app.post("/analyze_audio")
def analyze_audio(
    customer_phone_numbers: Annotated[str, Form(...)],
    source: Annotated[str, Form(...)],
    audio_files : List[UploadFile] = File(...)
):
    _audio_files = {}
    for file in audio_files:
        _audio_files[file.filename] = _parse_audio_file(file)

    analyze_request = AnalyzeRequest(
        audio_urls=[],
        source=source,
        num_speakers=2,  # Default value, can be adjusted
        max_speakers=3,  # Default value, can be adjusted
        audio_files=_audio_files,
        customer_phone_numbers=json.loads(customer_phone_numbers),
        verbose=False  # Default value, can be adjusted
    )

    analyze_response = audio_analyzer_service.analyze(analyze_request)

    return {
        audio_url: analyze_response.to_dict()
        for audio_url, analyze_response in analyze_response.items()
    }


def _parse_audio_file(audio_file: UploadFile) -> ndarray:
    audio_bytes = audio_file.file.read()  # Đọc nội dung file dưới dạng bytes
    audio_stream = BytesIO(audio_bytes)

    try:
        audio, sr = sf.read(audio_stream)  # data là ndarray
    except RuntimeError as e:
        raise ValueError(f"Could not read file: {e}")

    return audio


if __name__ == "__main__":

    # This is for running directly, e.g., python -m app.main
    uvicorn.run("app.main:app", host="0.0.0.0", port=31661, reload=True)
