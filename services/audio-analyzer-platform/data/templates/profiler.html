<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="refresh" content="15">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PYTHON PROFILER</title>

    <!-- Breakpoint Viewport -->
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0' name='viewport'/>

    <!-- CSS Files -->
    <style>
        table {
            border-collapse: collapse;
        }

        table td,
        table th {
            border: 1px solid black;
        }

        table thead tr:first-child th {
            border-top: 0;
            border-right: 0;
            border-left: 0;
        }

        .btn {
            height: 40px;
            line-height: 40px;
            padding: 0 10px;
            font-size: 14px;
            margin: 0;
            border: none;
        }

        .btn:focus {
            outline: none;
        }

        .inner {
            display: flex;
        }

        .inner > button {
            font-size: 14px;
            border: none;
            cursor: pointer;
        }

        .inner > button:focus {
            outline: none;
        }

        .btn-group {
            display: block;
        }

        .btn.active {
            background: #30333a;
            color: #fff;
        }

        #container > div {
            width: 100% !important;
            min-width: 100%;
            text-align: center !important;
        }

    </style>
</head>

<body>
<table style="width:100%">
    <thead>
    <tr style="background-color: paleturquoise">
        <th>No</th>
        <th>Name</th>
        <th>Total Req</th>
        <th>Pending Req</th>
        <th>Failure Req</th>
        <th>Total Exec Time
            <br>(millis)
        </th>
        <th>Last Exec Time
            <br>(millis)
        </th>
        <th>Highest Exec Time
            <br>(millis)
        </th>

        <th>Request Rate
            <br>(req/sec)
        </th>
        <th>Avg Time/Request
            <br>(millis/req)
        </th>

        <th>Total Memory Used
            <br>(MB)
        </th>
        <th>Last Memory Used
            <br>(MB)
        </th>
        <th>Highest Memory Used
            <br>(MB)
        </th>
        <th>Lowest Memory Used
            <br>(MB)
        </th>
    </tr>
    </thead>
    <tbody style="text-align: right">
    {% for report in reports %}
        <tr>
            <td style="text-align: center">{{ loop.index }}</td>
            <td style="text-align: left">
                <div class="inner">
                    {{ report.func_name }}
                </div>
            </td>
            <td>{{ report.total_hits.get_value() }}</td>
            <td>{{ report.current_pending_hits.get_value() }}</td>
            <td>{{ report.current_error_hits.get_value() }}</td>
            <td>{{ report.total_duration_as_ms() }}</td>
            <td>{{ report.last_duration_as_ms() }}</td>
            <td>{{ report.highest_duration_as_ms() }}</td>
            <td>{{ report.get_request_rate() }}</td>
            <td>{{ report.get_avg_time_per_request() }}</td>
            <td>{{ report.total_memory_as_mb() }}</td>
            <td>{{ report.last_memory_as_mb() }}</td>
            <td>{{ report.highest_memory_as_mb() }}</td>
            <td>{{ report.lowest_memory_as_mb() }}</td>
        </tr>
    {% endfor %}
    <tr>
        <td style="text-align: center"><b>Up Time</b></td>
        <td style="text-align: left" colspan="9">
            {{ uptime }}
        </td>
    </tr>
    <tr>
        <td style="text-align: center"><b>Start At</b></td>
        <td style="text-align: left" colspan="9">
            {{ start_at }}
        </td>
    </tr>
    </tbody>
</table>

</body>
</html>