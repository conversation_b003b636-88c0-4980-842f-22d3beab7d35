import io
import json
import time
import random
import string
from typing import Dict

import yaml
import requests
import pandas as pd
import streamlit as st
from yaml.loader import SafeLoader
import streamlit_authenticator as stauth

base_url = "http://127.0.0.1:5000"
base_url = "https://bcb3-57-155-66-184.ngrok-free.app"


def analyze_audio(
    audio_files: Dict[str, io.BytesIO], customer_phone_numbers: str, source
):
    url = f"{base_url}/analyze_audio"

    payload = {
        "source": "vinpearl",
        "customer_phone_numbers": json.dumps(
            {name: customer_phone_numbers for name in audio_files.keys()}
        ),
    }
    files = [
        # ('audio_files',('50004020250314101827.mp3',open('/home/<USER>/Working/sources/VinAI/vypq_workspace/[Vinpearl]_[STT]_Report_210525/data/audio/vinpearl/50004020250314101827.mp3','rb'),'audio/mpeg')),
        ("audio_files", (name, audio_file, "audio/mpeg"))
        for name, audio_file in audio_files.items()
    ]
    headers = {}

    response = requests.request(
        "POST", url, headers=headers, data=payload, files=files, verify=False
    )

    return response.json()


def move_file_in_list(file_list, current_index, direction):
    """Moves a file up (-1) or down (+1) in the list."""
    new_index = current_index + direction
    if 0 <= new_index < len(file_list):
        file_list[current_index], file_list[new_index] = (
            file_list[new_index],
            file_list[current_index],
        )


def remove_file_from_list(file_list, index_to_remove):
    """Removes a file from the list by index."""
    if 0 <= index_to_remove < len(file_list):
        file_list.pop(index_to_remove)


def simulate_ai_processing(ordered_files, progress_bar_updater):
    """
    Simulates sending files to an AI server and getting a Salesforce ID.
    Updates a progress bar.
    """

    num_files = len(ordered_files)
    audio_files = {}
    for i, audio_file in enumerate(ordered_files):
        st.write(
            f"Processing {audio_file.name}..."
        )  # Optional: give more granular feedback

        # Read bytes and wrap in io.BufferedReader
        audio_bytes = audio_file.read()
        audio_file.seek(0)
        buffered_reader = io.BufferedReader(io.BytesIO(audio_bytes))
        audio_files[audio_file.name] = buffered_reader

        time.sleep(random.uniform(0.5, 1.0))  # Simulate work for each file
        progress_bar_updater.progress(
            (i + 1) / num_files, text=f"Processing {audio_file.name}..."
        )

    time.sleep(1)
    progress_bar_updater.progress(1.0, text="Generating Salesforce Record...")
    time.sleep(0.5)

    analyze_response = analyze_audio(
        customer_phone_numbers=st.session_state.outbound_number,
        source="vinpearl",
        audio_files=audio_files,
    )

    responses = {}
    for audio_name, result in analyze_response.items():
        next_actions = result.get("case", {}).get("next_task", {})
        responses[audio_name] = {
            "case_id": "SFDC_"
            + "".join(random.choices(string.ascii_uppercase + string.digits, k=15)),
            "transcription": [
                {"role": turn["speaker"], "text": turn["text"]}
                for turn in result["transcriptions"]
            ],
            "customer_info": {
                "name": result.get("call_summary", {})
                .get("customer_info", {})
                .get("Tên khách hàng", ""),
                "phone": result.get("call_summary", {})
                .get("customer_info", {})
                .get("Số điện thoại", ""),
                "email": result.get("call_summary", {})
                .get("customer_info", {})
                .get("Email", ""),
            },
            "call_summary": result.get("call_summary", {}).get("summary", ""),
            "next_actions": {
                "category_group": next_actions.get("group_category", ""),
                "category": next_actions.get("category", ""),
                "pic": next_actions.get("pic", ""),
                "description": next_actions.get("description", ""),
            },
            "suggestion": result.get("recommended_task", {}).get("Đề xuất", ""),
            "analyze_status": result.get("analyze_status", {}).get("status", ""),
        }

    return responses


# --- Streamlit App ---

with open(".streamlit/config.yaml") as file:
    st_config = yaml.load(file, Loader=SafeLoader)


st.set_page_config(layout="wide", page_title="Call Center AI Analytics")

authenticator = stauth.Authenticate(
    st_config["credentials"],
    st_config["cookie"]["name"],
    st_config["cookie"]["key"],
    st_config["cookie"]["expiry_days"],
)
try:
    authenticator.login()
except Exception as e:
    st.error(e)

if st.session_state.get("authentication_status"):
    st.title("🎙️ Call Center Uploader for AI Processing")
    st.markdown(
        """
    <style>
        .st-emotion-cache-janbn0 {
            flex-direction: row-reverse;
            text-align: right;
        }
    </style>
    """,
        unsafe_allow_html=True,
    )

    if "ordered_audio_files" not in st.session_state:
        st.session_state.ordered_audio_files = []
    if "last_uploaded_file_names" not in st.session_state:
        st.session_state.last_uploaded_file_names = set()
    if "agent_id" not in st.session_state:
        st.session_state.agent_id = None
    if "outbound_number" not in st.session_state:
        st.session_state.outbound_number = None
    if "upload_key" not in st.session_state:
        st.session_state.upload_key = "file_uploader_1"  # key for uploader

    # Function to clear uploader
    def clear_upload():
        st.session_state.upload_key = (
            f"file_uploader_{st.session_state.upload_key[-1] + '1'}"  # force new key
        )
        st.session_state.last_uploaded_file_names = set()
        st.session_state.ordered_audio_files = []

    st.markdown(
        "Upload audio files that form a single conversation. You can reorder them and preview before submitting."
    )

    # --- 0. Input Form ---
    with st.form(key="call_details"):
        # Agent ID
        # Customer Number
        agent_id = st.text_input("Agent ID", placeholder="e.g., 13123456789")
        outbound_number = st.text_input(
            "Customer Number", placeholder="e.g., +84505552323"
        )
        submit_button = st.form_submit_button("Submit Call Details")

        if submit_button:
            st.session_state.agent_id = agent_id
            st.session_state.outbound_number = outbound_number
            st.success("Call details submitted!")

    # --- 1. File Uploader ---
    uploaded_files_from_dialog = st.file_uploader(
        "Select audio files (e.g., part1.mp3, part2.mp3)",
        type=["mp3", "wav", "m4a", "ogg", "flac"],
        accept_multiple_files=True,
        key=st.session_state.upload_key,
    )

    if uploaded_files_from_dialog:
        current_upload_names = {f.name for f in uploaded_files_from_dialog}
        if current_upload_names != st.session_state.last_uploaded_file_names:
            st.session_state.ordered_audio_files = list(uploaded_files_from_dialog)
            st.session_state.last_uploaded_file_names = current_upload_names
            st.rerun()
    elif not st.session_state.ordered_audio_files:
        st.session_state.last_uploaded_file_names = set()

    # --- 2. Display, Reorganize, and Play Uploaded Files ---
    if st.session_state.ordered_audio_files:
        st.subheader("Conversation Order & Preview:")
        st.markdown(
            "Ensure files are in the correct chronological order. You can listen to them here."
        )

        for i, audio_file in enumerate(st.session_state.ordered_audio_files):
            st.markdown(
                f"**{i + 1}. {audio_file.name}** ({audio_file.type}, {audio_file.size / 1024:.1f} KB)"
            )

            # Display audio player
            # Streamlit's st.audio can take the UploadedFile object directly
            st.audio(audio_file, format=audio_file.type)

            # Controls for reordering and removing
            control_cols = st.columns([0.1, 0.1, 0.1, 0.7])  # Up, Down, Remove, Spacer
            with control_cols[0]:
                if st.button(
                    "⬆️",
                    key=f"up_{i}_{audio_file.name}",
                    help="Move Up",
                    disabled=(i == 0),
                ):
                    move_file_in_list(st.session_state.ordered_audio_files, i, -1)
                    st.rerun()
            with control_cols[1]:
                if st.button(
                    "⬇️",
                    key=f"down_{i}_{audio_file.name}",
                    help="Move Down",
                    disabled=(i == len(st.session_state.ordered_audio_files) - 1),
                ):
                    move_file_in_list(st.session_state.ordered_audio_files, i, 1)
                    st.rerun()
            # with control_cols[2]:
            #     if st.button("🗑️", key=f"remove_{i}_{audio_file.name}", help="Remove File"):
            #         remove_file_from_list(st.session_state.ordered_audio_files, i)
            #         st.session_state.last_uploaded_file_names = {f.name for f in st.session_state.ordered_audio_files}
            #         st.rerun()
            st.markdown("---")  # Separator for each file item
    else:
        st.info("Please upload one or more audio files to begin.")
        if st.session_state.ordered_audio_files:
            st.subheader("AI Analysis Results (per Audio File)")
            audio_names = [f.name for f in st.session_state.ordered_audio_files]
            if "selected_audio_tab" not in st.session_state:
                st.session_state.selected_audio_tab = (
                    audio_names[0] if audio_names else None
                )

            tabs = st.tabs(audio_names)
            for idx, audio_name in enumerate(audio_names):
                with tabs[idx]:
                    salesforce_id_result = st.session_state.get(
                        "salesforce_id_result", {}
                    )
                    audio_result = salesforce_id_result.get(audio_name, {})

                    if not audio_result:
                        st.info("No result for this audio yet.")
                        continue

                    data = {
                        "Key": [
                            "Case ID",
                            "Customer Name",
                            "Customer Phone",
                            "Customer Email",
                            "Call Summary",
                            "Next Actions Category Group",
                            "Next Actions Category",
                            "Next Actions PIC",
                            "Next Actions Description",
                            "Suggestion",
                            "Analyze Status",
                        ],
                        "Value": [
                            audio_result.get("case_id", ""),
                            audio_result.get("customer_info", {}).get("name", ""),
                            audio_result.get("customer_info", {}).get("phone", ""),
                            audio_result.get("customer_info", {}).get("email", ""),
                            audio_result.get("call_summary", ""),
                            audio_result.get("next_actions", {}).get(
                                "category_group", ""
                            ),
                            audio_result.get("next_actions", {}).get("category", ""),
                            audio_result.get("next_actions", {}).get("pic", ""),
                            audio_result.get("next_actions", {}).get("description", ""),
                            audio_result.get("suggestion", ""),
                            audio_result.get("analyze_status", ""),
                        ],
                    }
                    df = pd.DataFrame(data)
                    st.success(
                        "✅ AI Processing Complete! Displaying Salesforce Record Details in a table."
                    )
                    st.table(df)

                    st.subheader("Call Transcription")
                    for utterance in salesforce_id_result["transcription"]:
                        role = utterance["role"]
                        text = utterance["text"]
                        if role == "Customer":
                            with st.chat_message(name="Customer", avatar="👤"):
                                st.markdown(text)
                        elif role == "Agent":
                            with st.chat_message(
                                name="Agent", avatar=":material/support_agent:"
                            ):
                                st.markdown(text)
    # --- 3. Submit to AI & Progress Bar & Result ---
    if st.session_state.ordered_audio_files:
        st.markdown("---")  # Separator before the submit button
        if st.button("🚀 Process Conversation with AI", type="primary"):
            if not st.session_state.agent_id or not st.session_state.outbound_number:
                st.error(
                    "Please enter inbound and outbound call numbers before submitting."
                )
                st.stop()
            st.info("Submitting to AI server... Please wait.")
            progress_bar = st.progress(0, text="Initializing...")

            salesforce_id_results = simulate_ai_processing(
                st.session_state.ordered_audio_files, progress_bar
            )

            # Create a tab for each audio transcription
            tabs = st.tabs(
                [f"Audio {audio_name}" for audio_name in salesforce_id_results]
            )

            for tab, audio_name, result in zip(
                tabs,
                list(salesforce_id_results.keys()),
                list(salesforce_id_results.values()),
            ):
                with tab:
                    progress_bar.empty()
                    data = {
                        "Key": [
                            "Case ID",
                            "Customer Name",
                            "Customer Phone",
                            "Customer Email",
                            "Call Summary",
                            "Next Actions Category Group",
                            "Next Actions Category",
                            "Next Actions PIC",
                            "Next Actions Description",
                            "Suggestion",
                            "Analyze Status",
                        ],
                        "Value": [
                            result["case_id"],
                            result["customer_info"]["name"],
                            result["customer_info"]["phone"],
                            result["customer_info"]["email"],
                            result["call_summary"],
                            result["next_actions"]["category_group"],
                            result["next_actions"]["category"],
                            result["next_actions"]["pic"],
                            result["next_actions"]["description"],
                            result["suggestion"],
                            result["analyze_status"],
                        ],
                    }

                    df = pd.DataFrame(data)
                    st.success(
                        "✅ AI Processing Complete! Displaying Salesforce Record Details in a table."
                    )
                    st.table(df)

                    st.subheader("Call Transcription")
                    st.markdown(
                        """
                        <style>
                        .chat-container {
                            display: flex;
                            margin: 10px 0;
                        }
                        .chat-avatar {
                            width: 36px;
                            height: 36px;
                            border-radius: 50%;
                            object-fit: cover;
                        }
                        .chat-bubble {
                            padding: 10px 15px;
                            margin: 4px 10px;
                            border-radius: 18px;
                            max-width: 70%;
                            font-size: 15px;
                            line-height: 1.4;
                            word-wrap: break-word;
                        }

                        /* Agent bubble (left) - like Messenger Blue */
                        .agent-row {
                            flex-direction: row;
                            justify-content: flex-start;
                        }
                        .agent-bubble {
                            background-color: #0084FF;
                            color: white;
                        }

                        /* Customer bubble (right) - Light gray */
                        .customer-row {
                            flex-direction: row-reverse;
                            justify-content: flex-end;
                        }
                        .customer-bubble {
                            background-color: #E4E6EB;
                            color: black;
                        }
                        </style>
                        """,
                        unsafe_allow_html=True,
                    )

                    st.markdown(f"### Transcription for Audio {audio_name}")
                    for utterance in result["transcription"]:
                        role = utterance.get("role", "")
                        text = utterance.get("text", "")
                        if role == "Customer":
                            avatar = (
                                "https://cdn-icons-png.flaticon.com/512/847/847969.png"
                            )
                            st.markdown(
                                f"""
                                <div class="chat-container customer-row">
                                    <img src="{avatar}" class="chat-avatar">
                                    <div class="chat-bubble customer-bubble">{text}</div>
                                </div>
                                """,
                                unsafe_allow_html=True,
                            )
                        elif role == "Agent":
                            avatar = "https://png.pngtree.com/png-vector/20240901/ourmid/pngtree-3d-cartoon-businessman-wearing-headset-clipart-illustration-png-image_13710409.png"
                            st.markdown(
                                f"""
                                <div class="chat-container agent-row">
                                    <img src="{avatar}" class="chat-avatar">
                                    <div class="chat-bubble agent-bubble">{text}</div>
                                </div>
                                """,
                                unsafe_allow_html=True,
                            )
            # Optionally clear files
            # st.session_state.ordered_audio_files = []
            # st.session_state.last_uploaded_file_names = set()
            # st.rerun()

    else:
        st.markdown("---")

    if st.button("🔄 Reset Conversation", on_click=clear_upload):
        st.session_state.ordered_audio_files = []
        st.session_state.last_uploaded_file_names = set()
        st.session_state.agent_id = None
        st.session_state.outbound_number = None
        st.cache_data.clear()
        st.cache_resource.clear()
        st.rerun()
    st.caption("Simple Call Center AI Analytics by VinIT-AI")
elif st.session_state.get("authentication_status") is False:
    st.error("Username/password is incorrect")
elif st.session_state.get("authentication_status") is None:
    st.warning("Please enter your username and password")
