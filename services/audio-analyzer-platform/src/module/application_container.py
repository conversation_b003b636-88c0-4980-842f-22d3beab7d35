import os
from dependency_injector import providers, containers

from src.processor.task_classifier import TaskClassifier, TaskClassifierImpl
from src.processor.audio_transcriber import AudioTranscriber, AudioTranscriberImpl
from src.processor.audio_diarization import AudioDiarization, AudioDiarizationImpl
from src.processor.audio_preprocessing import AudioPreprocessing, AudioPreprocessingImpl
from src.processor.transcription_refiner import TranscriptionRefiner, TranscriptionRefinerImpl
from src.processor.transcription_analyzer import TranscriptionAnalyzer, TranscriptionAnalyzerImpl


from src.service.audio_analyzer_service import AudioAnalyzerService, AudioAnalyzerServiceImpl
from src.service.audio_transcriber_service import AudioTranscriberService, AudioTranscriberServiceImpl
from src.service.transcription_analyzer_service import TranscriptionAnalyzerService, TranscriptionAnalyzerServiceImpl

from src.service.common.hardware_monitor import HardwareMonitor
from src.service.common.synchronization import Synchronization, SynchronizationImpl

from src.client.llm_client import OpenAIClient
from src.client.sfmc_client import Sfmc<PERSON>lient

from src.domain.llm_config import LLMConfig
from src.domain.transcriber_model_config import TranscriberModelConfig


class ApplicationContainer(containers.DeclarativeContainer):
    config = providers.Configuration()

    synchronization = providers.AbstractSingleton(Synchronization)
    synchronization.override(
        providers.Singleton(
            SynchronizationImpl,
            n_simultaneously_tasks=config.server.n_similarity_requests
        )
    )

    hardware_monitor = providers.Singleton(
        HardwareMonitor,
        20
    )


    # llm_client = providers.Singleton(
    #     AzureOpenAIClient,
    #     api_version=os.environ['AZURE_OPENAI_API_VERSION'],
    #     azure_endpoint=os.environ['OPENAI_ENDPOINT'],
    #     api_key=os.environ['OPENAI_API_KEY']
    # )

    llm_client = providers.Singleton(
        OpenAIClient,
        api_version=os.environ['AZURE_OPENAI_API_VERSION'],
        endpoint=os.environ['OPENAI_ENDPOINT'],
        api_key=os.environ['OPENAI_API_KEY']
    )


    # llm_client = providers.Singleton(
    #     AzureOpenAIClient,
    #     api_version=config.llm_client.api_version,
    #     azure_endpoint=config.llm_client.azure_endpoint,
    #     api_key=config.llm_client.api_key,
    # )

    sfmc_client = providers.Singleton(
        SfmcClient,
        endpoint_url=config.salesforce_client.endpoint_url,
        api_key=os.environ['SF_KEY']
    )

    llm_config = providers.Singleton(
        LLMConfig,
        model_name=config.llm_config.model_name,
        max_tokens=config.llm_config.max_tokens,
        temperature=config.llm_config.temperature,
        sampling=config.llm_config.sampling,
    )

    describe_llm_config = providers.Singleton(
        LLMConfig,
        model_name=config.describe_llm_config.model_name,
        max_tokens=config.describe_llm_config.max_tokens,
        temperature=config.describe_llm_config.temperature,
        sampling=config.describe_llm_config.sampling,
    )

    # Audio processing components
    audio_preprocessing = providers.AbstractSingleton(AudioPreprocessing)
    audio_preprocessing.override(
        providers.Singleton(
            AudioPreprocessingImpl,
            model_name=config.audio_preprocessing.model_name,
            demucs_model_root=config.audio_preprocessing.demucs_model_root
        )
    )

    audio_diarization = providers.AbstractSingleton(AudioDiarization)
    audio_diarization.override(
        providers.Singleton(
            AudioDiarizationImpl,
            model_path=config.audio_diarization.model_path,
            hf_token=config.audio_diarization.hf_token,
            is_using_gpu=config.audio_diarization.is_using_gpu
        )
    )

    audio_transcriber = providers.AbstractSingleton(AudioTranscriber)
    audio_transcriber.override(
        providers.Singleton(
            AudioTranscriberImpl,
            model_name=config.audio_transcriber.whisper.model_name,
            local_model_path=config.audio_transcriber.whisper.model_path,
            audio_diarization=audio_diarization,
            is_using_gpu=config.audio_transcriber.whisper.is_using_gpu,
            min_turn_duration_in_ms=config.audio_transcriber.min_turn_duration_in_ms,
            ignoring_sentences=config.audio_transcriber.whisper.ignoring_sentences
        )
    )

    transcriber_model_config = providers.Singleton(
        TranscriberModelConfig,
        model_path=config.audio_transcriber.chunk_former.model_path,
        chunk_size=config.audio_transcriber.chunk_former.chunk_size,
        left_context_size=config.audio_transcriber.chunk_former.left_context_size,
        right_context_size=config.audio_transcriber.chunk_former.right_context_size,
        total_batch_duration=config.audio_transcriber.chunk_former.total_batch_duration,
        is_using_gpu=config.audio_transcriber.chunk_former.is_using_gpu
    )


    # Transcription refiner
    transcription_refiner = providers.AbstractSingleton(TranscriptionRefiner)
    transcription_refiner.override(
        providers.Singleton(
            TranscriptionRefinerImpl,
            llm_client=llm_client,
            refiner_prompts=config.transcription_refiner.refiner_prompt,
            system_prompt=config.transcription_refiner.system_prompt,
            merger_prompt=config.transcription_refiner.merger_prompt,
            transcriber_prompt=config.transcription_refiner.transcriber_prompt,
            possible_missing_words=config.transcription_refiner.possible_missing_words,
            llm_config=llm_config,
            describe_llm_config=describe_llm_config,
        )
    )

    # Transcription analyzer
    transcription_analyzer = providers.AbstractSingleton(TranscriptionAnalyzer)
    transcription_analyzer.override(
        providers.Singleton(
            TranscriptionAnalyzerImpl,
            llm_client=llm_client,
            llm_config=llm_config,
            analyzer_prompt=config.transcription_analyzer_prompts.analyzer_prompt,
            summary_merger_prompt=config.transcription_analyzer_prompts.summarizer_prompt,
            evaluator_prompt=config.transcription_analyzer_prompts.evaluator_prompt,
        )
    )

    # Task classifier
    task_classifier = providers.AbstractSingleton(TaskClassifier)
    task_classifier.override(
        providers.Singleton(
            TaskClassifierImpl,
            llm_client=llm_client,
            llm_config=llm_config,
            task_description=config.task_classifier.task_description,
            classifier_prompt=config.task_classifier.classifier_prompt
        )
    )


    # Audio transcriber service
    audio_transcriber_service = providers.AbstractSingleton(AudioTranscriberService)
    audio_transcriber_service.override(
        providers.Singleton(
            AudioTranscriberServiceImpl,
            audio_transcriber=audio_transcriber,
            audio_preprocessing=audio_preprocessing,
            transcription_refiner=transcription_refiner,
        )
    )

    # Transcription analyzer service
    transcription_analyzer_service = providers.AbstractSingleton(TranscriptionAnalyzerService)
    transcription_analyzer_service.override(
        providers.Singleton(
            TranscriptionAnalyzerServiceImpl,
            transcription_analyzer=transcription_analyzer,
            task_classifier=task_classifier,
        )
    )


    # Audio analyzer service
    audio_analyzer_service = providers.AbstractSingleton(AudioAnalyzerService)
    audio_analyzer_service.override(
        providers.Singleton(
            AudioAnalyzerServiceImpl,
            audio_transcriber_service=audio_transcriber_service,
            transcription_analyzer_service=transcription_analyzer_service,
            sfmc_client=sfmc_client,
            synchronization=synchronization,
            hardware_monitor=hardware_monitor,
            n_processors=config.audio_analyzer.n_processors
        )
    )

