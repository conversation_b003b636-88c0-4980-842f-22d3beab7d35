from typing import List

from src.domain.call_summary import CallSummary
from src.domain.transcription import Transcription
from src.domain.analyze_status import Analyze<PERSON>tatus


from src.processor.task_classifier import TaskClassifier
from src.processor.transcription_analyzer import TranscriptionAnalyzer

from src.controller.requests.summary_request import SummaryRequest
from src.controller.requests.summary_response import SummaryResponse
from src.controller.requests.classify_request import ClassifyRequest
from src.controller.requests.classify_response import ClassifyResponse


class TranscriptionAnalyzerService:
    def summary(self, summary_request: SummaryRequest) -> SummaryResponse:
        pass

    def classify_case(self, classify_request: ClassifyRequest) -> ClassifyResponse:
        pass

    def evaluate(self, transcriptions: List[Transcription], source: str) -> AnalyzeStatus:
        pass

    def combine_summaries(self, summaries: List[CallSummary], source: str) -> CallSummary:
        pass

class TranscriptionAnalyzerServiceImpl(TranscriptionAnalyzerService):
    def __init__(
            self,
            transcription_analyzer: TranscriptionA<PERSON>yzer,
            task_classifier: TaskClassifier,
    ):
        self.transcription_analyzer: TranscriptionAnalyzer = transcription_analyzer
        self.task_classifier: TaskClassifier = task_classifier

    def summary(self, summary_request: SummaryRequest) -> SummaryResponse:
        summarized_call = self.transcription_analyzer.analyze(
            transcriptions=summary_request.transcriptions,
            source=summary_request.source
        )
        return SummaryResponse(
            call_summary=summarized_call
        )

    def classify_case(self, classify_request: ClassifyRequest) -> ClassifyResponse:
        tasks = self.task_classifier.classify(
            call_summary=classify_request.call_summary.summary,
            source=classify_request.source
        )
        return ClassifyResponse(tasks=tasks)

    def evaluate(self, transcriptions: List[Transcription], source: str) -> AnalyzeStatus:
        evaluation = self.transcription_analyzer.evaluate(
            transcriptions=transcriptions,
            source=source
        )
        return evaluation
    
    def combine_summaries(self, summaries: List[CallSummary], source: str) -> CallSummary:
        return self.transcription_analyzer.combine_summaries(
            summaries=summaries,
            source=source
        )