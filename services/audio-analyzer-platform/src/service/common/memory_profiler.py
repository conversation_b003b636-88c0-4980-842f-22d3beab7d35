import os
import time
import psutil
from beautifultable import BeautifulTable

from py_profiler.measure_service import MeasureValue, AccumulativeMeasureService


class MemoryMeasureValue(MeasureValue):
    def __init__(self, func_name: str):
        super().__init__(func_name)
        self.last_memory_mb =  -1
        self.lowest_memory_mb =  -1
        self.highest_memory_mb =  -1
        self.total_memory_mb = -1

    def total_memory_as_mb(self):
        return int(self.total_memory_mb)

    def last_memory_as_mb(self):
        return int(self.last_memory_mb)

    def highest_memory_as_mb(self):
        return int(self.highest_memory_mb)

    def lowest_memory_as_mb(self):
        return int(self.lowest_memory_mb)

    def stop(
            self,
            duration_ns: int,
            is_error: bool = False,
            used_memory_mb: int = -1,
            total_memory_mb: int = -1,
    ):
        if is_error:
            self.current_error_hits.increment()

        self.current_pending_hits.decrement()

        self.total_duration_ns.set(duration_ns)
        with self._lock:
            self.last_duration_ns = duration_ns
            self.last_memory_mb = used_memory_mb
            self.total_memory_mb = total_memory_mb
            if self.highest_duration_ns == -1 or self.highest_duration_ns < duration_ns:
                self.highest_duration_ns = duration_ns
            if self.lowest_duration_ns == -1 or self.lowest_duration_ns > duration_ns:
                self.lowest_duration_ns = duration_ns
            if self.highest_memory_mb == -1 or self.highest_memory_mb < used_memory_mb:
                self.highest_memory_mb = used_memory_mb
            if self.lowest_memory_mb == -1 or self.lowest_memory_mb > used_memory_mb:
                self.lowest_memory_mb = used_memory_mb


class AccumulativeMemoryMeasureService(AccumulativeMeasureService):
    def __init__(self, profile_template_path: str):
        super().__init__()
        self._template = self.load_jinja2_template(profile_template_path)
        print(f'AccumulativeMemoryMeasureService called {__name__}')

    def get_measure_value(self, func_name: str) -> MemoryMeasureValue:
        with self._lock:
            if func_name not in self._measure_map:
                self._measure_map[func_name] = MemoryMeasureValue(func_name)
        return self._measure_map.get(func_name)


    def stop_measure(
            self,
            func_name: str,
            duration_ns: int,
            is_error: bool = False,
            used_memory_mb: int=-1,
            total_memory_mb: int=-1,
    ) -> None:
        self.get_measure_value(func_name).stop(duration_ns, is_error, used_memory_mb, total_memory_mb)


    def as_table(self):
        table = BeautifulTable()
        table.columns.header = [
            "No",
            "Name",
            "Total Req",
            "Pending Req",
            "Error Req",
            "Total Exec Time",
            "Last Exec Time",
            "Highest Exec Time",
            "Request Rate (req/sec)",
            "Avg Time/Request (millis/request)",
            "Total Memory Used",
            "Last Memory Used",
            "Highest Memory used",
        ]
        table.columns.alignment['Name'] = BeautifulTable.ALIGN_LEFT

        table.columns.width = 12
        table.columns.width["No"] = 4
        table.columns.width["Total Req"] = 8
        table.columns.width["Pending Req"] = 10
        table.columns.width["Name"] = 32

        for i, report in enumerate(self.get_reports()):
            table.rows.append([
                i + 1,
                report.func_name,
                report.total_hits.get_value(),
                report.current_pending_hits.get_value(),
                report.current_error_hits.get_value(),
                report.total_duration_as_ms(),
                report.last_duration_as_ms(),
                report.highest_duration_as_ms(),
                report.get_request_rate(),
                report.get_avg_time_per_request(),
                report.total_memory_as_mb(),
                report.last_memory_as_mb(),
                report.highest_memory_as_mb()
            ])
        return str(table)
    def load_jinja2_template(self, path: str):
        # import pkgutil
        # data = pkgutil.get_data(__name__, path).decode("utf-8")

        from jinja2 import Template
        with open(path, 'r') as file:  # r to open file in READ mode
            html_as_string = file.read()
            return Template(html_as_string)


memory_profiling_service = AccumulativeMemoryMeasureService("data/templates/profiler.html")

def m_profiler(name=None):
    def memory_usage():
        return psutil.Process(os.getpid()).memory_info().rss / 1024 ** 2

    def decorator(func):
        def wrapper(*args, **kwargs):
            if name is None:
                try:
                    function_name = func.__func__.__qualname__
                except:
                    function_name = func.__qualname__
            else:
                function_name = name

            is_error = False
            begin_time = time.time() * 1000_000_000
            begin_memory = memory_usage()
            memory_profiling_service.start_measure(function_name)
            try:
                return func(*args, **kwargs)
            except Exception as error:
                is_error = True
                raise error
            finally:
                memory_profiling_service.stop_measure(
                    func_name=function_name,
                    duration_ns=int(time.time() * 1000_000_000 - begin_time),
                    used_memory_mb=memory_usage() - begin_memory,
                    total_memory_mb=begin_memory,
                    is_error=is_error
                )

        return wrapper

    return decorator
