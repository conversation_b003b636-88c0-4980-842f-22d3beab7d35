import logging
from threading import Semaphore


class Synchronization:
    def acquire(self) -> bool:
        pass

    def release(self) -> bool:
        pass


class SynchronizationImpl(Synchronization):
    def __init__(self, n_simultaneously_tasks: int = 3):
        logging.info(f"n_simultaneously_tasks {n_simultaneously_tasks}")
        self.synchronizer = Semaphore(n_simultaneously_tasks)
        self.n_simultaneously_tasks = n_simultaneously_tasks

    def acquire(self) -> bool:
        try:
            self.synchronizer.acquire()
            return True
        except Exception as e:
            return False

    def release(self) -> bool:
        try:
            self.synchronizer.release()
            return True
        except Exception as e:
            return False
