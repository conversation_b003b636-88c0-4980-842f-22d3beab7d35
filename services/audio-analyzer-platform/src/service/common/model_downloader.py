import os
import logging
from typing import List
from pathlib import Path
from py_profiler import profiler

from py_common.domain.s3_config import S3Config
from py_common.model_downloader import ModelDownloader
from py_common.utils.utils import delete_file_or_folder

"""
@Author: anhlt92
"""


class ModelDownloaderImpl(ModelDownloader):
    def __init__(self, model_paths: list, s3_config: S3Config):
        super().__init__(model_paths, s3_config)

    @profiler(name=f"{__qualname__}.sync_models")
    def sync_models(self) -> bool:
        for remote_path, local_path in self.model_path_dict.items():
            try:
                model_name = Path(remote_path).name
                if not os.path.exists(os.path.join(local_path, model_name)):
                    logging.info(f"Preparing to download model from: {remote_path}")

                    is_truncated = self._download_s3_folder(remote_path, local_path)
                    if is_truncated is False:
                        logging.info(f"Download object: {remote_path}.")
                        self._download_objects(
                            [remote_path],
                            local_path,
                            remote_path.rsplit('/', 1)[0]
                        )
                else:
                    logging.info(f"Model: {remote_path} is downloaded.")
            except Exception as e:
                logging.error(f"Can't sync model: {remote_path}", exc_info=e)
                delete_file_or_folder(local_path)
                raise e
        return True

    @profiler(name=f"{__qualname__}.download_s3_folder")
    def _download_s3_folder(self, s3_folder: str, local_dir=None) -> bool:
        if s3_folder[-1] != "/":
            s3_folder = s3_folder + "/"
        max_keys = 100
        response = self.s3_client.list_objects_v2(
            Bucket=self.s3_config.bucket,
            Prefix=s3_folder,
            MaxKeys=max_keys
        )

        is_truncated = response.get('IsTruncated', False)
        contents: list = response.get('Contents', [])
        continuation_token = response.get('NextContinuationToken', '')

        self._download_object_from_contents(contents, local_dir, s3_folder)

        while (len(contents) > 0 and is_truncated):
            response = self.s3_client.list_objects_v2(
                Bucket=self.s3_config.bucket,
                Prefix=s3_folder,
                MaxKeys=max_keys,
                ContinuationToken=continuation_token
            )
            is_truncated = response.get('IsTruncated', False)
            contents: list = response.get('Contents', [])
            continuation_token = response.get('NextContinuationToken', '')

            self._download_object_from_contents(contents, local_dir, s3_folder)

        return is_truncated

    def _download_objects(self, object_keys: List[str], local_dir: str, s3_folder: str):
        for key in object_keys:
            target = key if local_dir is None else os.path.join(local_dir, os.path.relpath(key, s3_folder))
            if not os.path.exists(os.path.dirname(target)):
                os.makedirs(os.path.dirname(target))
            if key[-1] == '/':
                logging.info(key)
                continue
            logging.info(f"Downloading {key}...")
            self.bucket.download_file(key, target)
            logging.info(f"Downloaded {key} to {target}")
