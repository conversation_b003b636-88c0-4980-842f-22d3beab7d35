import cv2
import logging
import requests
import numpy as np
from io import BytesIO
import concurrent.futures
from numpy import ndarray
from PIL import Image, ImageOps
from py_profiler import profiler
from pillow_heif import register_heif_opener
from typing import List, Dict, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor
from py_common.utils.utils import encode_url, is_ignore_encoded_url, free_memory

from src.service.common.memory_profiler import m_profiler

register_heif_opener()

import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

"""
@Author: anhlt92
"""


class ImageDownloader:
    MlsEntityType = "mls_raw"

    def mget_internal_urls(self, urls: List[str]) -> Dict[str, str]:
        pass

    def mget_internal_urls_by_entity_ids(self, entity_type: str, entity_ids: List[str]) -> Dict[str, List[str]]:
        """
        :param entity_type:  See ImageDownloader.MlsEntityType
        :param entity_ids:
        :return:
        """
        pass

    def download_images(self, urls: List[str]) -> Dict[str, ndarray]:
        pass

    @staticmethod
    def read_from_local(path: str) -> Tuple[str, ndarray]:

        image = Image.open(path)
        image = ImageOps.exif_transpose(image)
        image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        return path, image

    @staticmethod
    def read_from_url(url: str) -> Tuple[str, Optional[ndarray]]:
        image = None
        response = None
        try:
            url_encoded = url if is_ignore_encoded_url(url) else encode_url(url)
            response = requests.get(url_encoded, cookies={'ssid_admin': 'abcd'}, verify=False)

            image = Image.open(BytesIO(response.content))
            image = ImageOps.exif_transpose(image)
            image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        except Exception as e:
            logging.info(f"Unable to read image from {url}", exc_info=e)
        finally:
            del response

        return url, image

    @staticmethod
    def fallback_to_given_urls(internal_url_map: Dict[str, str], given_urls: List[str]) -> Dict[str, str]:
        """
            Fallback to the given url if couldn't get the internal urls, there are might be 2 reasons
            + 1: the given urls is the internal url already => It's safe to fall back to the given one.
            + 2: There is something wrong in the service =>  It's safe to fall back to the given one cause our flow will be independent
        :param internal_url_map:
        :param given_urls:
        :return:  Dict[str, str]
        """
        return {
            url: internal_url_map.get(url, url)
            for url in given_urls
        }


class DefaultImageDownloader(ImageDownloader):
    def __init__(self, base_url: str, sk: str, num_workers: int) -> None:
        self.base_url: str = base_url
        self.sk: str = sk
        self.executor: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=num_workers)

    @profiler()
    def mget_internal_urls(self, urls: List[str]) -> Dict[str, str]:
        response = None
        internal_url_map= {}
        try:
            if urls is not None and len(urls) > 0:
                response = requests.post(
                    url=f"{self.base_url}/internal/download-photo/_/internal-url",
                    json={
                        "urls": urls
                    },
                    params={
                        'sk': self.sk
                    },
                    verify=False,
                )

                if not response.ok:
                    raise Exception(f'Response error: {response.status_code}')

                internal_url_map: Dict[str, str] = response.json() if response is not None else {}
                return ImageDownloader.fallback_to_given_urls(internal_url_map, urls)
            else:
                return {}
        except Exception as e:
            logging.error(msg=f"Failed to mget_internal_urls({urls})", exc_info=e)
            raise e
        finally:
            del response
            internal_url_map.clear()
            del internal_url_map

    def mget_internal_urls_by_entity_ids(self, entity_type: str, entity_ids: List[str]) -> Dict[str, List[str]]:

        try:
            if entity_ids is not None and len(entity_ids) > 0:
                response = requests.post(
                    url=f"{self.base_url}/internal/download-photo/{entity_type}/multi-pre-sign-url",
                    json={
                        "entity_ids": entity_ids
                    },
                    params={
                        'sk': self.sk
                    },
                    verify=False,
                )

                if not response.ok:
                    raise Exception(f'Response error: {response.status_code}')
                internal_urls_map: Dict[str, List[str]] = response.json() if response is not None else {}
                return internal_urls_map
            else:
                return {}
        except Exception as e:
            logging.error(msg=f"Failed to mget_internal_urls_by_entity_ids({entity_type}, {entity_ids})", exc_info=e)
            raise e

    @m_profiler(name=f"{__qualname__}.download_images")
    def download_images(self, urls: List[str]) -> Dict[str, ndarray]:
        futures = []
        try:
            image_data: Dict[str, ndarray] = {}
            futures = [self._download(url) for url in urls]
            for i, task in enumerate(concurrent.futures.as_completed(futures)):
                url = urls[i]
                try:
                    is_cancelled = task.cancelled()
                    if not is_cancelled:
                        url, image = task.result()
                        if image is not None:
                            image_data[url] = image
                except Exception as e:
                    logging.error(f"Couldn't download image from {url}", exc_info=e)
            return image_data
        finally:
            free_memory(futures)

    def _download(self, path_or_url: str):
        if path_or_url.startswith("http"):
            return self.executor.submit(ImageDownloader.read_from_url, url=path_or_url)
        else:
            return self.executor.submit(ImageDownloader.read_from_local, path=path_or_url)
