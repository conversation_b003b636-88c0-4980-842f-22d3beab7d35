from src.utils.util import get_gpu_device_info, get_cpu_device_info, get_disk_info, get_device


class HardwareMonitor:
    def __init__(self, n_execute_times: int = 10):
        self.n_execute_times: int = n_execute_times
        self.gpu_device_infos = []
        self.cpu_device_infos = []
        self.disk_infos = []
        self.device = get_device(using_gpu=True)

    def log(self):
        self.cpu_device_infos.append(get_cpu_device_info())
        self.cpu_device_infos = self.cpu_device_infos[-self.n_execute_times:]
        if self.device == "cuda":
            self.gpu_device_infos.append(get_gpu_device_info())
            self.gpu_device_infos = self.gpu_device_infos[-self.n_execute_times:]
        self.disk_infos.append(get_disk_info())
        self.disk_infos = self.disk_infos[-self.n_execute_times:]

    # def __exit__(self, exc_type, exc_value, exc_tb):
    #     self.synchronization.release()

    def get_all_infos(self):
        cpu_info = [get_cpu_device_info()]
        if self.device == "cuda":
            gpu_info = [get_gpu_device_info()]
        else:
            gpu_info = []
        disk_info = [get_disk_info()]
        return {
            "cpu_device_infos": self.cpu_device_infos + cpu_info,
            "gpu_device_infos": self.gpu_device_infos + gpu_info,
            "disk_infos": self.disk_infos + disk_info,
        }
