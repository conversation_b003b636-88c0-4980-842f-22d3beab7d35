from src.service.common.synchronization import Synchronization


class synchronization_context_manager:

    def __init__(
            self,
            synchronization: Synchronization
    ):
        self.synchronization: Synchronization = synchronization

    def __enter__(self):
        self.synchronization.acquire()
        return self

    def __exit__(self, exc_type, exc_value, exc_tb):
        self.synchronization.release()
