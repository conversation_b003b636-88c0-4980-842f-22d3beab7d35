from numpy import n<PERSON><PERSON>
from typing import Dict, List

from concurrent.futures.thread import <PERSON>hr<PERSON><PERSON><PERSON>Executor

from src.client.sfmc_client import SfmcClient

from src.controller.requests.analyze_request import AnalyzeRequest
from src.controller.requests.analyze_response import AnalyzeResponse
from src.controller.requests.classify_request import ClassifyRequest
from src.controller.requests.summary_request import SummaryRequest
from src.controller.requests.transcribe_request import TranscribeRequest

from src.domain.case import Case
from src.domain.enum import Enum
from src.domain.call_summary import CallSummary

from src.service.common.hardware_monitor import HardwareMonitor
from src.service.common.synchronization import Synchronization
from src.service.audio_transcriber_service import AudioTranscriberService
from src.service.transcription_analyzer_service import TranscriptionAnalyzerService
from src.service.common.synchronization_context_manager import synchronization_context_manager

from src.utils.util import get_most_common_language


class AudioAnalyzerService:
    def analyze(self, analyze_request: AnalyzeRequest) -> Dict[str, AnalyzeResponse]:
        pass


class AudioAnalyzerServiceImpl(AudioAnalyzerService):
    def __init__(
        self,
        audio_transcriber_service: AudioTranscriberService,
        transcription_analyzer_service: TranscriptionAnalyzerService,
        synchronization: Synchronization,
        hardware_monitor: HardwareMonitor,
        n_processors: int,
        sfmc_client: SfmcClient,
    ):
        self.audio_transcriber_service: AudioTranscriberService = (
            audio_transcriber_service
        )
        self.transcription_analyzer_service: TranscriptionAnalyzerService = (
            transcription_analyzer_service
        )
        self.sfmc_client: SfmcClient = sfmc_client

        self.synchronization: Synchronization = synchronization
        self.hardware_monitor: HardwareMonitor = hardware_monitor
        self.n_processors: int = n_processors
        self.executor = ThreadPoolExecutor(max_workers=n_processors)

    def analyze(self, analyze_request: AnalyzeRequest) -> Dict[str, AnalyzeResponse]:
        tasks = {}
        with synchronization_context_manager(self.synchronization) as sync_ctx:
            audio_urls = []
            if (
                analyze_request.audio_files is None
                or len(analyze_request.audio_files) == 0
            ):
                audio_urls = analyze_request.audio_urls
            else:
                audio_urls = list(analyze_request.audio_files.keys())

            # for audio_url in audio_urls:
            analyze_responses = self._analyze(
                audio_urls=audio_urls,
                source=analyze_request.source,
                num_speakers=analyze_request.num_speakers,
                max_speakers=analyze_request.max_speakers,
                verbose=analyze_request.verbose,
                audio_files=analyze_request.audio_files,
            )

            analyzed_audio = {}
            call_logs = {}
            for audio_url, result in analyze_responses.items():
                result.customer_phone_number = (
                    analyze_request.customer_phone_numbers.get(audio_url, None)
                )
                analyzed_audio[audio_url] = result
                # self.sfmc_client.push(result)

                from src.domain.call_log import CallLog

                call_logs[audio_url] = CallLog(
                    call_summary=result.call_summary,
                    task=result.case.task_type,
                    next_task=result.case.next_task,
                    transcriptions=result.transcriptions,
                    customer_requests=result.call_summary.customer_requests,
                    agent_responses=result.call_summary.agent_responses,
                    recommended_actions=result.case.recommended_task.recommended_actions,
                )
            # save_to_file(
            #     logs=call_logs,
            #     csv_file_path="output.csv"
            # )

            # os.system("python create_html.py")
            return analyzed_audio

    def _analyze(
        self,
        audio_urls: List[str],
        source: str,
        num_speakers: int,
        max_speakers: int,
        audio_files: Dict[str, ndarray] = {},
        verbose: bool = False,
    ) -> Dict[str, AnalyzeResponse]:
        transcribe_responses = self.audio_transcriber_service.transcribe(
            transcribe_request=TranscribeRequest(
                audio_urls=audio_urls,
                source=source,
                num_speakers=num_speakers,
                max_speakers=max_speakers,
                verbose=verbose,
                audio_files=audio_files,
            )
        )

        # transcriptions = transcribe_responses.transcriptions.get(audio_url, [])
        analyze_statuses = {}
        summarized_calls = {}
        classified_tasks = {}
        transcriptions = {
            url: transcription
            for url, transcription in transcribe_responses.transcriptions.items()
        }
        for audio_url, transcribe in transcriptions.items():
            analyze_statuses[audio_url] = self.transcription_analyzer_service.evaluate(
                transcriptions=transcribe, source=source
            )

            summarized_calls[audio_url] = self.transcription_analyzer_service.summary(
                summary_request=SummaryRequest(
                    transcriptions=transcribe,
                    source=source,
                    language=get_most_common_language(transcriptions=transcribe),
                )
            ).call_summary

            classified_tasks[audio_url] = (
                self.transcription_analyzer_service.classify_case(
                    classify_request=ClassifyRequest(
                        call_summary=summarized_calls[audio_url],
                        source=source,
                    )
                )
            )

        combined_summaries = self._combine_summaries(
            summaries=summarized_calls, source=source
        )

        analyze_responses = {}
        for audio_url in audio_urls:
            combined_summaries[audio_url].agent_responses = summarized_calls[
                audio_url
            ].agent_responses

            analyze_responses[audio_url] = AnalyzeResponse(
                transcriptions=transcriptions[audio_url],
                call_summary=combined_summaries[audio_url],
                case=Case(
                    task_type=classified_tasks[audio_url].tasks.get(
                        Enum.TASK_TYPE, None
                    ),  # type: ignore
                    next_task=classified_tasks[audio_url].tasks.get(
                        Enum.NEXT_TASK_VI, None
                    ),  # type: ignore
                    recommended_task=classified_tasks[audio_url].tasks.get(
                        Enum.RECOMMENDED_ACTIONS, None
                    ),  # type: ignore
                ),
                analyze_status=analyze_statuses[audio_url],  # type: ignore
            )

        return analyze_responses

    def _combine_summaries(
        self, summaries: Dict[str, CallSummary], source: str
    ) -> Dict[str, CallSummary]:
        if len(summaries) <= 1:
            return summaries

        combined_summaries = {}

        combined_urls = []
        for audio_url, _ in summaries.items():
            combined_urls.append(audio_url)
            combined_summaries[audio_url] = (
                self.transcription_analyzer_service.combine_summaries(
                    summaries=[
                        summaries[combined_url] for combined_url in combined_urls
                    ],
                    source=source,
                )
            )

        return combined_summaries
