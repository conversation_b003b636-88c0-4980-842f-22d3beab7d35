from typing import List
from numpy import ndarray

from src.domain.enum import Enum
from src.domain.transcription import Transcription

from src.processor.audio_transcriber import AudioTranscriber
from src.processor.audio_preprocessing import AudioPreprocessing
from src.processor.transcription_refiner import TranscriptionRefiner

from src.controller.requests.transcribe_request import TranscribeRequest
from src.controller.requests.transcribe_response import TranscribeResponse


class AudioTranscriberService:
    def transcribe(self, transcribe_request: TranscribeRequest) -> TranscribeResponse: # type: ignore
        pass


class AudioTranscriberServiceImpl(AudioTranscriberService):
    def __init__(
            self,
            audio_transcriber: AudioTranscriber,
            audio_preprocessing: AudioPreprocessing,
            transcription_refiner: TranscriptionRefiner,
    ):
        self.audio_transcriber: AudioTranscriber = audio_transcriber
        self.audio_preprocessing: AudioPreprocessing = audio_preprocessing
        self.transcription_refiner: TranscriptionRefiner = transcription_refiner


    def transcribe(self, transcribe_request: TranscribeRequest) -> TranscribeResponse:
        transcriptions = {}
        audio_urls = []
        if transcribe_request.audio_files is None or len(transcribe_request.audio_files) == 0:
            audio_urls = transcribe_request.audio_urls
        else:
            audio_urls = list(transcribe_request.audio_files.keys())

        for audio_url in audio_urls:
            transcriptions[audio_url] = self._transcribe(
                audio_url=audio_url,
                source=transcribe_request.source,
                audio_file=transcribe_request.audio_files.get(audio_url, None),
                verbose=False
            )

        return TranscribeResponse(
            transcriptions=transcriptions,
            source=transcribe_request.source
        )


    def _transcribe(
            self, 
            audio_url: str, 
            source: str,
            audio_file: ndarray = None,
            num_speakers: int = Enum.DEFAULT_NUM_SPEAKERS,
            max_speakers: int = Enum.DEFAULT_MAX_SPEAKERS,
            verbose: bool = False
    ) -> List[Transcription]:
        processed_audio, sample_rate = self.audio_preprocessing.process_pipeline(
            audio_path=audio_url,
            audio=audio_file
        )
        language = self.audio_transcriber.detect_language_from_ndarray(processed_audio)


        transcriptions = self.audio_transcriber.transcribe_from_ndarray(
            audio=processed_audio,
            sample_rate=sample_rate,
            min_speakers=num_speakers,
            max_speakers=max_speakers,
        )
        if verbose:
            print(audio_file)
            print(f"{self.__class__.__name__} -> {self._transcribe.__name__}")
            print(f"Transcription results for {audio_url}:")
            for i, transcription in enumerate(transcriptions):
                print(f"[{i+1}] {transcription.speaker} ({transcription.start:.2f}s - {transcription.end:.2f}s) ({transcription.language}): {transcription.text}")

        transcriptions = self.transcription_refiner.refine(
            transcriptions=transcriptions,
            source=TranscriptionRefiner.build_source(source, language),
            audio=processed_audio,
        )

        if verbose:
            print(audio_file)
            print(f"{self.__class__.__name__} -> {self._transcribe.__name__}")
            print(f"Refined transcription results for {audio_url}:")
            for i, transcription in enumerate(transcriptions):
                print(f"[{i+1}] {transcription.speaker} ({transcription.start:.2f}s - {transcription.end:.2f}s): {transcription.text}")

        return transcriptions

