import yaml


class PromptParser:
    PROMPT = "prompt"
    NESTED_PROMPTS = "nested_prompts"
    RETURN_FORMAT = "return_format"
    PARSING_PATTERN = "parsing_pattern"
    SPLITTER = "splitter"

    def __init__(
        self,         
        prompt: str, 
        return_format: str, 
        parsing_pattern: str,
        splitter: str = None,
        nested_prompts: dict = {},
    ):
        self.prompt: str = prompt
        self.return_format: str = return_format
        self.parsing_pattern: str = parsing_pattern
        self.splitter: str = splitter
        self.nested_prompts: dict = nested_prompts

    @staticmethod
    def _load_prompt(prompt_path: str) -> dict:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            return {
                PromptParser.PROMPT: config.get(PromptParser.PROMPT, ""),
                PromptParser.RETURN_FORMAT: config.get(PromptParser.RETURN_FORMAT, ""),
                PromptParser.PARSING_PATTERN: config.get(PromptParser.PARSING_PATTERN, ""),
                PromptParser.NESTED_PROMPTS: PromptParser._parse_nested_prompts(config.get(PromptParser.PROMPT, {})),
                PromptParser.SPLITTER: config.get(PromptParser.SPLITTER, None)  
            }

    @staticmethod
    def _parse_nested_prompts(prompts: dict) -> dict:
        parsed_prompts = {}
        if isinstance(prompts, str):
            return {}
        else:
            for key, value in prompts.items():
                if isinstance(value, dict):
                    parsed_prompts[key] = PromptParser._parse_nested_prompts(value)
                else:
                    parsed_prompts[key] = value
            return parsed_prompts
    
    @staticmethod
    def from_config_file(config_file: str) -> 'PromptParser':
        """
        Factory method to create a PromptParser instance from a configuration file.
        :param config_file: Path to the YAML configuration file.
        :return: An instance of PromptParser.
        """
        config = PromptParser._load_prompt(prompt_path=config_file)
        return PromptParser(**config)
