import torch
import numpy as np
from numpy import ndarray
from pyannote.audio.pipelines import SpeakerDiarization

from src.domain.enum import Enum
from src.utils.util import get_device

class AudioDiarization:
    def diarize_from_file(
            self,
            audio_path: str,
            min_speakers: int=2,
            max_speakers: int=3
    ) -> SpeakerDiarization:
        pass

    def diarize_from_ndarray(
            self,
            audio: ndarray,
            sample_rate: int=Enum.TARGET_SR,
            min_speakers: int=2,
            max_speakers: int=3
    ) -> SpeakerDiarization:
        pass

class AudioDiarizationImpl(AudioDiarization):
    def __init__(
            self,
            model_path: str,
            hf_token: str,
            is_using_gpu: bool = True
    ):
        self.diarization_pipeline = SpeakerDiarization.from_pretrained(
            model_path,
            use_auth_token=hf_token
        )

        self.device = get_device(is_using_gpu)
        self.diarization_pipeline.to(self.device)

    def diarize_from_file(
            self,
            audio_path: str,
            sample_rate: int=Enum.TARGET_SR,
            min_speakers: int=2,
            max_speakers: int=3
    ):
        diarization = self.diarization_pipeline(
            audio_path,
            min_speakers=min_speakers,
            max_speakers=max_speakers
        )


        return diarization

    def diarize_from_ndarray(
            self,
            audio: ndarray,
            sample_rate: int=Enum.TARGET_SR,
            min_speakers: int=2,
            max_speakers: int=3
    ) -> SpeakerDiarization:
        """Process audio that has been processed by VAD (Voice Activity Detection)

        Args:
            audio: The numpy array output from the _vad function
            sample_rate: The sample rate of the audio
            num_speakers: The number of speakers to detect

        Returns:
            The diarization result
        """
        # Convert int16 to float32 and normalize if needed
        if audio.dtype == np.int16:
            audio = audio.astype(np.float32) / 32767.0

        # Ensure the audio is in the right shape for the diarization pipeline
        if audio.ndim == 1:
            audio = np.expand_dims(audio, axis=0)

        # Convert to PyTorch tensor
        audio = torch.from_numpy(audio).float()

        # Move tensor to the same device as the model
        audio = audio.to(self.device)

        diarization = self.diarization_pipeline(
            file={
                "waveform": audio,
                "sample_rate": sample_rate
            },
            min_speakers=min_speakers,
            max_speakers=max_speakers
        )

        # Enforce the number of speakers by post-processing
        # diarization = self._limit_speakers(diarization, num_speakers)

        return diarization
