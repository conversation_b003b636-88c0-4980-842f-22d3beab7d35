import io
import tiktoken
import soundfile as sf
from numpy import ndarray
from jinja2 import Template
from typing import List, Dict

from src.domain.enum import Enum
from src.domain.llm_config import LLMConfig
from src.domain.transcription import Transcription

from src.client.llm_client import AzureOpenAIClient

from src.utils.util import chunk_audio
from src.utils.util import get_most_common_language

from src.processor.prompt_parser import PromptParser

class TranscriptionRefiner:
    def refine(self, transcriptions: List[Transcription], source: str, audio: ndarray) -> List[Transcription]:
        pass

    @staticmethod
    def build_source(source: str, language: str) -> str:
        return f"{source}_{language}"

    @staticmethod
    def get_language_from_source(source: str) -> str:
        return source.split("_")[1]

class TranscriptionRefinerImpl(TranscriptionRefiner):
    def __init__(
            self,
            llm_client: AzureOpenAIClient,
            system_prompt: str,
            refiner_prompts: str,
            merger_prompt: str,
            transcriber_prompt: str,
            possible_missing_words: Dict[str, str],
            llm_config: LLMConfig,
            describe_llm_config: LLMConfig = None,
            max_audio_duration_in_second: int = 240,  # seconds
    ):
        self.llm_client: AzureOpenAIClient = llm_client
        self.llm_config: LLMConfig = llm_config
        self.describe_llm_config: LLMConfig = describe_llm_config
        self.possible_missing_words: Dict[str, str] = possible_missing_words
        
        self.system_prompt_config = PromptParser.from_config_file(system_prompt)

        self.merger_prompt_config = PromptParser.from_config_file(merger_prompt)
        self.merger_prompt: Template = Template(self.merger_prompt_config.prompt)

        self.transcriber_prompt_config = PromptParser.from_config_file(transcriber_prompt)
        self.transcriber_prompt: Template = Template(self.transcriber_prompt_config.prompt)

        self.refiner_prompts_config = PromptParser.from_config_file(refiner_prompts)
        self.refiner_prompts: Dict[str, Template] = {
            source: Template(prompt)
            for source, prompt in self.refiner_prompts_config.nested_prompts.items()
        }

        self.max_audio_duration_in_second: int = max_audio_duration_in_second
        


    def refine(self, transcriptions: List[Transcription], source: str, audio: ndarray) -> List[Transcription]:
        aligned_transcriptions = self._align_transcriptions(transcriptions, source, audio)

        prompt = self.refiner_prompts[source].render(
            conversation=self._get_conversation(aligned_transcriptions),
            splitter=self.refiner_prompts_config.splitter,
            possible_missing_words=self.possible_missing_words
        )

        is_retrying = True
        while is_retrying:
            completion = self.llm_client.client.chat.completions.create(
                model=self.llm_config.model_name,
                messages=[
                    {
                        Enum.ROLE: Enum.USER,
                        Enum.CONTENT: prompt
                    }
                ],
                max_tokens=self.llm_config.max_tokens,
            )
            # print("completion.choices[0].message.content ", completion)
            if completion.choices[0].finish_reason != Enum.STOP_STATUS:
                # print(f"Refining transcription... Stop reason {completion.choices[0].finish_reason}, retrying...")
                is_retrying = True
            else:
                # print(f"Refining transcription... Stop reason {completion.choices[0].finish_reason}, not retrying...")
                is_retrying = False
            # print("completion.choices[0].message.content ", completion.choices[0].finish_reason)
        language = get_most_common_language(transcriptions)
        return self._rebuild_transcriptions(completion.choices[0].message.content, language)


    def _align_transcriptions(self, transcriptions: List[Transcription], source: str, audio: ndarray) -> List[Transcription]:

        full_paragraph = self._generate_paragraph(
            audio=audio,
            transcriptions=transcriptions,
            language=get_most_common_language(transcriptions)
        )

        merged_transcriptions = self._merge_transcriptions(audio, transcriptions, full_paragraph)
        return merged_transcriptions
        # merged_transcriptions = "\n".join(merged_transcriptions)
        # return self._rebuild_transcriptions(merged_transcriptions, language=get_most_common_language(transcriptions))

    def _generate_paragraph(self, audio: ndarray, transcriptions: List[Transcription], language: str, temperature: float = None ) -> str:
        chunks = chunk_audio(
            audio, 
            sample_rate=Enum.TARGET_SR, 
            chunk_duration_in_seconds=120, 
            transcriptions=transcriptions
        )

        print(f"Number of chunks after splitting: {len(chunks)}")
        prompt = self.transcriber_prompt.render(
            possible_missing_words=self.possible_missing_words
        )
        full_paragraph = ""
        for chunk in chunks:
            # Step 2: Write ndarray to WAV format in memory
            buffer = io.BytesIO()
            sf.write(buffer, chunk, Enum.TARGET_SR, format='WAV')
            buffer.seek(0)  # Reset con trỏ về đầu
            buffer.name = f"{language}.wav"
            buffered_reader = io.BufferedReader(buffer)

            paragraph = self.llm_client.client.audio.transcriptions.create(
                model=self.describe_llm_config.model_name,
                file=buffered_reader,
                response_format="json",
                language=language,
                prompt=prompt,
                extra_body={
                    "turn_detection": {
                        "type": "server_vad",
                        "threshold": 0.5,
                        # "prefix_padding_ms": 300,
                        # "silence_duration_ms": 500,
                    },
                    # "input_audio_noise_reduction": {
                    #     "type": "near_field"
                    # },
                    "max_tokens": self.describe_llm_config.max_tokens,
                    "temperature": self.describe_llm_config.temperature if temperature is None else temperature,
                    "sampling": self.describe_llm_config.sampling,
                }
            )


            full_paragraph += paragraph.text + ". "

        return full_paragraph

    def _merge_transcriptions(self, audio: ndarray, transcriptions: List[Transcription], paragraph: str) -> List[Transcription]:    
        conversation = self._get_conversation(transcriptions)
        
        is_retrying = True
        while is_retrying:
            prompt = self.merger_prompt.render(
                whisper_trans=conversation,
                transcription=paragraph
            )

            completion = self.llm_client.client.chat.completions.create(
                model=self.llm_config.model_name,
                messages=[
                    {
                        Enum.ROLE: Enum.SYSTEM,
                        Enum.CONTENT: self.system_prompt_config.prompt
                    },
                    {
                        Enum.ROLE: Enum.USER,
                        Enum.CONTENT: prompt
                    }
                ],
                max_tokens=self.llm_config.max_tokens,
            )

            if completion.choices[0].finish_reason != Enum.STOP_STATUS:
                # print(f"Merging transcriptions... Stop reason {completion.choices[0].finish_reason}, retrying...")
                is_retrying = True
                paragraph = self._generate_paragraph(
                    audio=audio,
                    transcriptions=transcriptions,
                    language=get_most_common_language(transcriptions),
                    temperature=0.2
                )
                # print("Regenerating paragraph transcription...", paragraph)
            else:
                # print(f"Merging transcriptions...Stop reason {completion.choices[0].finish_reason}, not retrying...")
                is_retrying = False

        return self._rebuild_transcriptions(
            refined_script=completion.choices[0].message.content,
            language=get_most_common_language(transcriptions)
        )


    def _rebuild_transcriptions(self, refined_script: str, language: str) -> List[Transcription]:
        transcriptions = []
        scripts = refined_script.split(self.refiner_prompts_config.splitter)
        for script in scripts:
            parts = script.split(":", 1)
            if len(parts) != 2:
                continue
            speaker = parts[0].strip().replace("\n", " ")
            text = parts[1].strip().replace("\n", " ")
            start = 0
            end = 0
            tran = Transcription(
                speaker=speaker,
                start=start,
                end=end,
                text=text,
                language=language
            )
            transcriptions.append(tran)
        return transcriptions

    def _get_conversation(self, transcriptions: List[Transcription]) -> str:
        conversation = ""
        for transcription in transcriptions:
            conversation += f"{transcription.speaker}: {transcription.text}\n"

        return conversation

    def _count_tokens(self, prompt: str) -> int:
        model_mapping = {
            "gpt-4.1": "cl100k_base",
            "gpt-4.1-mini": "cl100k_base",
            "gpt-3.5-turbo": "cl100k_base",
            "gpt-3.5-turbo-16k": "cl100k_base"
        }
        encoding = tiktoken.get_encoding(model_mapping[self.llm_config.model_name])
        # Count tokens
        num_tokens = len(encoding.encode(prompt))
        return num_tokens