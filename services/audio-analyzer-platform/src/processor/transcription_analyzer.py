import re
import json
from jinja2 import Template
from typing import  List, Dict

from src.domain.enum import Enum
from src.domain.llm_config import LLMConfig
from src.domain.call_summary import CallSummary
from src.domain.transcription import Transcription
from src.domain.analyze_status import AnalyzeStatus

from src.client.llm_client import AzureOpenAIClient

from src.processor.prompt_parser import PromptParser

class TranscriptionAnalyzer:
    def analyze(self, transcriptions: List[Transcription], source: str) -> CallSummary: # type: ignore
        pass

    def evaluate(self, transcriptions: List[Transcription], source: str) -> AnalyzeStatus:
        pass

    def combine_summaries(self, summaries: List[CallSummary], source: str) -> CallSummary:
        pass

    @staticmethod
    def build_source(source: str, language: str) -> str:
        return f"{source}_{language}"

    @staticmethod
    def get_language_from_source(source: str) -> str:
        return source.split("_")[1]

class TranscriptionAnalyzerImpl(TranscriptionAnalyzer):
    def __init__(
            self,
            llm_client: AzureOpenAIClient,
            llm_config: LLMConfig,
            analyzer_prompt: str,
            summary_merger_prompt: str,
            evaluator_prompt: str
    ):
        self.llm_client: AzureOpenAIClient = llm_client
        self.llm_config: LLMConfig = llm_config

        self.analyzer_prompt_config: PromptParser = PromptParser.from_config_file(analyzer_prompt)
        self.analyzer_prompt: Template = Template(self.analyzer_prompt_config.prompt)

        self.summary_merger_prompt_config: PromptParser = PromptParser.from_config_file(summary_merger_prompt)
        self.summary_merger_prompt: Template = Template(self.summary_merger_prompt_config.prompt)

        self.evaluator_prompt_config: PromptParser = PromptParser.from_config_file(evaluator_prompt)
        self.evaluator_prompt: Template = Template(self.evaluator_prompt_config.prompt)

        self.parsing_pattern = self.analyzer_prompt_config.parsing_pattern

    def analyze(self, transcriptions: List[Transcription], source: str) -> CallSummary:
        if len(transcriptions) == 0:
            return None

        prompt = self.analyzer_prompt.render(
            conversation=self._get_conversation(transcriptions),
            return_format=self.analyzer_prompt_config.return_format,
        )
        completion = self.llm_client.client.chat.completions.create(
            extra_body={},
            model=self.llm_config.model_name,
            messages=[
                {
                    Enum.ROLE: Enum.USER,
                    Enum.CONTENT: prompt
                }
            ]
        )

        summarized_conversation = self._parse_llm_response(completion.choices[0].message.content)

        return CallSummary(
            customer_info=summarized_conversation.get(Enum.customer_info, []),
            customer_requests=summarized_conversation.get(Enum.customer_request, []),
            agent_responses=summarized_conversation.get(Enum.agent_response, []),
            summary=summarized_conversation.get(Enum.summary_conversation, ""),
            is_interrupted=summarized_conversation.get(Enum.is_interrupted, False)
        )


    def evaluate(self, transcriptions: List[Transcription], source: str) -> AnalyzeStatus:
        if len(transcriptions) == 0:
            return None

        prompt = self.evaluator_prompt.render(
            transcription=self._get_conversation(transcriptions),
        )

        completion = self.llm_client.client.chat.completions.create(
            extra_body={},
            model=self.llm_config.model_name,
            messages=[
                {
                    Enum.ROLE: Enum.USER,
                    Enum.CONTENT: prompt
                }
            ]
        )

        response = self._parse_llm_response(completion.choices[0].message.content)

        return AnalyzeStatus(
            status=response.get(AnalyzeStatus.OVERALL_QUALITY),
            detail=response.get(AnalyzeStatus.SUGGESTIONS)
        )

    def combine_summaries(self, summaries: List[CallSummary], source: str) -> CallSummary:
        if len(summaries) == 0:
            return None

        prompt = self.summary_merger_prompt.render(
            summaries=[summary.summary for summary in summaries],
        )

        completion = self.llm_client.client.chat.completions.create(
            extra_body={},
            model=self.llm_config.model_name,
            messages=[
                {
                    Enum.ROLE: Enum.USER,
                    Enum.CONTENT: prompt
                }
            ]
        )

        merged_summary = self._parse_llm_response(completion.choices[0].message.content)

        return CallSummary(
            customer_info=merged_summary.get(Enum.customer_info, []),
            customer_requests=summaries[-1].customer_requests,
            agent_responses=None,
            summary=merged_summary.get(Enum.summary_conversation, "")
        )

    def _parse_llm_response(self, llm_response: str) -> Dict[str, List[str]]:
        try:
            matches = re.findall(self.parsing_pattern, llm_response, re.DOTALL)
            return json.loads(matches[0])
        except:
            return json.loads(llm_response)


    def _get_conversation(self, transcriptions: List[Transcription]) -> str:
        conversation = ""
        for transcription in transcriptions:
            conversation += f"{transcription.speaker}: {transcription.text}\n"

        return conversation