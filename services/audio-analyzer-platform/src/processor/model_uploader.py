import os
from azure.storage.blob import BlobServiceClient


class ModelUploader:
    def __init__(self, connection_str: str, container_name: str, local_file_path: str) -> None:
        """
        Initializes the ModelDownloader.
        """
        self.connection_str = connection_str
        self.container_name = container_name
        self.local_file_path = local_file_path
        self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_str)
        self.container_client = self.blob_service_client.get_container_client(self.container_name)

        try:
            self.container_client.create_container()
        except Exception:
            pass 

    def upload_model(self, local_file_path: str, destination: str) -> None:
        blob_name = os.path.basename(local_file_path)    # You can also customize the name
        blob_client = self.blob_service_client.get_blob_client(container=self.container_name, blob=blob_name)

        with open(local_file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)

        print(f"Model uploaded to container '{self.container_name}' as blob '{blob_name}'.")
        

    def upload_model_dir(self, local_dir: str, destination: str) -> None:
        """
        Uploads all files in the local directory to the blob container, preserving directory structure under 'destination'.
        """
        all_files = self._get_all_files(local_dir)
        for file_path in all_files:
            rel_path = os.path.relpath(file_path, local_dir)
            blob_name = os.path.join(destination, rel_path).replace("\\", "/")
            self.upload_model(file_path, blob_name)
        
    
    def _get_all_files(self, local_dir: str) -> list:
        """
        Returns a list of all files in the local directory.
        """
        all_files = []
        for root, _, files in os.walk(local_dir):
            for file in files:
                all_files.append(os.path.join(root, file))
        return all_files