import os
import torch
import whisper
import webrtcvad
import numpy as np
from tqdm import tqdm
from typing import List
from numpy import ndarray
from pydub import AudioSegment
from pyannote.audio.pipelines import SpeakerDiarization

from src.domain.enum import Enum
from src.domain.transcription import Transcription


from src.utils.util import get_device
from src.processor.audio_diarization import AudioDiarization

class AudioTranscriber:
    def transcribe(self, audio_path: str) -> List[Transcription]:
        pass

    def transcribe_from_ndarray(
            self,
            audio: ndarray,
            sample_rate: int=Enum.TARGET_SR,
            min_speakers: int=2,
            max_speakers: int=3,
            original_audio_path: str = None
    ) -> List[Transcription]:
        pass

    def detect_language(self, audio_file: str) -> str:
        pass

    def detect_language_from_ndarray(
            self,
            audio: ndarray,
            prefer_languages: List[str] = [
                Enum.LANG_VI, Enum.LANG_EN, Enum.LANG_KOR
            ]
    ) -> str:
        pass

class AudioTranscriberImpl(AudioTranscriber):
    def __init__(
            self,
            model_name: str,
            local_model_path: str,
            audio_diarization: AudioDiarization,
            min_turn_duration_in_ms: int = 200,
            ignoring_sentences: List[str] = [],
            is_using_gpu: bool = True
    ):
        self.device: torch.device = get_device(is_using_gpu)
        self.local_model_path: str = local_model_path
        self.whisper_model = whisper.load_model(
            model_name, 
            device=self.device,
            download_root=self.local_model_path
        )
        self.audio_diarization: AudioDiarization = audio_diarization
        self.min_turn_duration_in_ms: float = min_turn_duration_in_ms
        self.ignoring_sentences: List[str] = [
            ignore.lower()
            for ignore in ignoring_sentences
        ]
        self.temp_dir: str = Enum.TEMP_DIR
        os.makedirs(self.temp_dir, exist_ok=True)
        # print("ignoring_sentences\n", ignoring_sentences)

    def transcribe_from_ndarray(
            self,
            audio: ndarray,
            sample_rate: int=Enum.TARGET_SR,
            min_speakers: int=2,
            max_speakers: int=3,
            original_audio_path: str = None
    ) -> List[Transcription]:
        """Transcribe audio from a numpy array

        Args:
            audio: Audio data as numpy array
            sample_rate: Sample rate of the audio
            num_speakers: Number of speakers to detect

        Returns:
            List of Transcription objects
        """
        # Diarize the audio
        if original_audio_path is None:
            diarization = self.audio_diarization.diarize_from_ndarray(
                audio=audio,
                sample_rate=sample_rate,
                min_speakers=min_speakers,
                max_speakers=max_speakers,
            )
        else:
            diarization = self.audio_diarization.diarize_from_file(
                audio_path=original_audio_path,
                min_speakers=min_speakers,
                max_speakers=max_speakers,
            )

        # Detect language
        language = self.detect_language_from_ndarray(audio)

        # Transcribe directly from ndarray
        transcriptions = self._transcribe_from_ndarray(
            audio=audio,
            sample_rate=sample_rate,
            diarization=diarization,
            language=language
        )

        return transcriptions
    
    def _transcribe_from_ndarray(
            self,
            audio: ndarray,
            sample_rate: int,
            diarization: SpeakerDiarization,
            language: str,
    ) -> List[Transcription]:
        """Transcribe audio from a numpy array

        Args:
            audio: Audio data as numpy array
            sample_rate: Sample rate of the audio
            diarization: Diarization result
            language: Detected language code

        Returns:
            List of Transcription objects
        """
        transcriptions = []
        last_speaker = None
        full_audio = AudioSegment(
            audio.tobytes(),
            frame_rate=sample_rate,
            sample_width=2,  # 2 bytes for int16
            channels=1       # Assuming mono audio
        )
        first_turn = True
        for turn, _, speaker in tqdm(diarization.itertracks(yield_label=True), desc="Transcribing segments..."):
            # print(f"{speaker} ({turn.start:.2f}s - {turn.end:.2f}s")
            start_ms = int(turn.start * 1000)
            end_ms = int(turn.end * 1000)
            if self._is_valid_turn(start_ms, end_ms) is False:
                continue
            segment = full_audio[start_ms:end_ms]
            segment = self._vad(segment)
            segment_array = np.array(segment).astype(np.float32) / 32767.0
            # segment_array = np.array(segment.get_array_of_samples()).astype(np.float32) / 32767.0
            if first_turn:
                _language = Enum.LANG_VI
                first_turn = False
            else:
                _language = language
            try:
                # Transcribe the segment
                result = self.transcribe_audio_from_ndarray(
                    audio=segment_array,
                    task=Enum.TRANSCRIBE,
                    language=_language
                )
                text = result[Enum.TEXT]
                # print(f"{speaker} ({turn.start:.2f}s - {turn.end:.2f}s language {_language}: {text}")


                if self._is_valid_sentence(text) is False:
                    if _language == Enum.LANG_VI and language != Enum.LANG_VI:
                        text = "Hello"
                    else:
                        continue
                # Add to transcriptions
                if speaker != last_speaker:
                    transcription = Transcription(
                        start=turn.start,
                        end=turn.end,
                        text=text,
                        speaker=speaker,
                        language=_language
                    )
                    transcriptions.append(transcription)
                    last_speaker = speaker
                else:
                    transcriptions[-1].end = turn.end
                    transcriptions[-1].text = transcriptions[-1].text + ' ' + text
            except Exception as e:
                print(f"Error transcribing segment {start_ms}-{end_ms}: {e}")


        return transcriptions

    def _is_valid_turn(self, start_in_ms: float, end_in_ms: float) -> bool:
        if end_in_ms - start_in_ms < self.min_turn_duration_in_ms:
            return False
        return True

    def _is_valid_sentence(self, sentence: str) -> bool:
        sentence = sentence.strip()
        is_valid = not any(ignore in sentence.lower() for ignore in self.ignoring_sentences)
        # print(f"Sentence|{sentence}|{is_valid}|")
        return is_valid

    def transcribe_audio(
            self,
            audio: str,
            task: str = Enum.TRANSCRIBE,
            language: str = Enum.LANG_VI
    ):
        """Transcribe audio using Whisper

        Args:
            audio: Path to audio file or audio array
            task: Transcription task (e.g., 'transcribe')
            language: Language code (e.g., 'en', 'vi')

        Returns:
            Dictionary with transcription result
        """
        result = self.whisper_model.transcribe(
            audio=audio,
            task=task,
            language=language
        )
        return result


    def transcribe_audio_from_ndarray(
            self,
            audio: ndarray,
            task: str = Enum.TRANSCRIBE,
            language: str = Enum.LANG_VI
    ):
        """Transcribe audio using Whisper

        Args:
            audio: Path to audio file or audio array
            task: Transcription task (e.g., 'transcribe')
            language: Language code (e.g., 'en', 'vi')

        Returns:
            Dictionary with transcription result
        """
        # Convert to float32 if it's not already
        if isinstance(audio, ndarray) and audio.dtype != np.float32:
            if audio.dtype == np.int16:
                # Convert int16 to float32 and normalize to [-1, 1]
                audio = audio.astype(np.float32) / 32767.0
            else:
                # For other types, just convert to float32
                audio = audio.astype(np.float32)

        result = self.whisper_model.transcribe(
            audio=audio,
            task=task,
            language=language,
            fp16=True,
            # beam_size=5,
            # temperature=0
        )

        return result


        # import io
        # from openai import AzureOpenAI
        # from scipy.io import wavfile
        # from src.utils.util import split_silence_and_merge_min_duration

        # chunks = split_silence_and_merge_min_duration(audio, sample_rate=Enum.TARGET_SR)

        # full_transcription = ""
        # for chunk in chunks:
        #     # Step 2: Write ndarray to WAV format in memory
        #     wav_buffer = io.BytesIO()
        #     wavfile.write(wav_buffer, Enum.TARGET_SR, chunk)
        #     wav_buffer.seek(0)  # Move to start so it acts like a file
        #     wav_buffer.name = "audio.wav"  # Add a name attribute to mimic a file upload


        #     client = AzureOpenAI(
        #         base_url="https://haicv-m9uq4a6z-eastus2.cognitiveservices.azure.com/openai",
        #         api_version="2024-12-01-preview",
        #         api_key="6BBqjE7am52GSWilae3x8ieMKZfemBQF3yQZsWhwsDGAzrhNafJAJQQJ99BDACHYHv6XJ3w3AAAAACOGCuoq"
        #     )


        #     transcription = client.audio.transcriptions.create(
        #         model="gpt-4o-transcribe", 
        #         file=wav_buffer, 
        #         response_format="text",
        #         language=language,
        #         extra_body={
        #             "turn_detection": {
        #                 "type": "server_vad",
        #                 "threshold": 0.5,
        #                 # "prefix_padding_ms": 200,
        #                 # "silence_duration_ms": 200,
        #             },
        #             "max_tokens": self.max_generated_tokens,
        #             "temperature": 0.0,
        #             "sampling": False
        #         }
        #     )
        #     full_transcription += transcription + " "
        #     print("Transcription result:", transcription)

        # return full_transcription

    def detect_language_from_ndarray(
            self,
            audio: ndarray,
            prefer_languages: List[str] = [
                Enum.LANG_VI, Enum.LANG_EN, Enum.LANG_KOR
            ]
    ) -> str:
        """Detect language from an audio numpy array

        Args:
            audio: Audio data as numpy array

        Returns:
            Detected language code (e.g., 'en', 'vi')
        """
        # Convert to float32 if it's not already
        # If stereo: convert to mono
        duration_ms = len(audio)
        mid_start = duration_ms // 4
        mid_end = mid_start * 3
        audio = audio[mid_start:mid_end]

        audio = whisper.pad_or_trim(audio)

        audio = np.array(audio, dtype=np.float32)
        if audio.ndim == 2:
            audio = np.mean(audio, axis=0)

        # Normalize to [-1, 1] if not already
        if np.max(np.abs(audio)) > 1:
            audio = audio / np.max(np.abs(audio))
        # Pad or trim the audio to the required length
        audio = whisper.pad_or_trim(audio)

        # Convert to mel spectrogram
        mel = whisper.log_mel_spectrogram(
            audio,
            n_mels=self.whisper_model.dims.n_mels
        ).to(self.whisper_model.device)

        # Detect language
        _, probs = self.whisper_model.detect_language(mel)
        sorted_probs = dict(sorted(probs.items(), key=lambda item: item[1], reverse=True))
        for lang_code, prob in sorted_probs.items():
            if lang_code in prefer_languages:
                return lang_code
        return max(probs, key=probs.get)


    def _vad(
            self,
            audio: AudioSegment,
            frame_duration_ms: int=30,
            aggressiveness: int=2,
            target_sr: int=16000
    ):
        audio = audio.set_channels(1).set_frame_rate(target_sr)
        samples = np.array(audio.get_array_of_samples())
        vad = webrtcvad.Vad(aggressiveness)
        frame_size = int(target_sr * frame_duration_ms / 1000)
        voiced_samples = []

        for i in range(0, len(samples), frame_size):
            frame = samples[i:i + frame_size]
            if len(frame) < frame_size:
                break
            is_speech = vad.is_speech(frame.tobytes(), sample_rate=target_sr)
            if is_speech:
                voiced_samples.extend(frame)

        voiced_array = np.array(voiced_samples, dtype=np.int16)
        return voiced_array

