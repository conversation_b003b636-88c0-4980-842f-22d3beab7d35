import os
import os.path
import pathlib
import librosa
import webrtcvad
import subprocess
import numpy as np
from uuid import uuid4
from typing import <PERSON><PERSON>
from numpy import ndarray
from pydub import AudioSegment

from src.domain.enum import Enum

class AudioPreprocessing:
    def process_pipeline(self, audio_path: str, audio: ndarray = None) -> Tuple[ndarray, int]: # type: ignore
        pass

    
class AudioPreprocessingImpl(AudioPreprocessing):
    def __init__(self, model_name: str, demucs_model_root: str):
        self.model_name: str = model_name
        self.demucs_model_root: str = demucs_model_root
        self.target_sr: int = Enum.TARGET_SR
        self.temp_dir: str = Enum.TEMP_DIR
        self._config_model_path(self.demucs_model_root)
        # print("Torch home: ", os.environ.get('TORCH_HOME'))

    def _config_model_path(self, model_root_path: str):
        os.environ["TORCH_HOME"] = model_root_path


    def process_pipeline(self, audio_path: str, audio: ndarray = None) -> Tuple[ndarray, int]: # type: ignore
        processed_audio_path = ""
        try:
            # audio_path = self._remove_noise(audio_path)
            # print("AUDIO PATH:", processed_audio_path)
            processed_audio, sample_rate = self._load_audio(audio_path=audio_path, audio=audio)
            processed_audio = np.array(processed_audio.get_array_of_samples())
            # sf.write("processed_audio.wav", processed_audio, sample_rate)
            return processed_audio, sample_rate  # Return the numpy array directly for diarization
        except Exception as e:
            raise Exception(f"Error preprocessing audio: {e}")
        finally:
            temp_dir = self._get_temp_dir(processed_audio_path)
            if len(temp_dir) > 0:
                os.system(f"rm -rf {temp_dir}")


    def _load_audio(self, audio_path: str, audio: ndarray = None) -> Tuple[AudioSegment, int]:
        if audio is None:
            audio, sr = librosa.load(audio_path, sr=self.target_sr, mono=True)
        else:
            sr = self.target_sr

        audio = (audio * 32767).astype(np.int16)
        audio = AudioSegment(
            audio.tobytes(),
            frame_rate=sr,
            sample_width=2, 
            channels=1
        )

        return audio, sr

    def _vad(
            self,
            audio: AudioSegment,
            frame_duration_ms: int=30,
            aggressiveness: int=2
    ):
        audio = audio.set_channels(1).set_frame_rate(self.target_sr)
        samples = np.array(audio.get_array_of_samples())
        vad = webrtcvad.Vad(aggressiveness)
        frame_size = int(self.target_sr * frame_duration_ms / 1000)
        voiced_samples = []

        for i in range(0, len(samples), frame_size):
            frame = samples[i:i + frame_size]
            if len(frame) < frame_size:
                break
            is_speech = vad.is_speech(frame.tobytes(), sample_rate=self.target_sr)
            if is_speech:
                voiced_samples.extend(frame)

        voiced_array = np.array(voiced_samples, dtype=np.int16)
        return voiced_array


    def _remove_noise(self, audio_path: str, n_processors: int = 4) -> str:
        output_dir = os.path.join(self.temp_dir, str(uuid4()))
        os.makedirs(output_dir)
        subprocess.run([
            "demucs",
            audio_path,
            "-n", self.model_name,
            "-o", output_dir,
            # "--two-stems", "vocals",
            "-j", str(n_processors)
        ])

        return os.path.join(
            self._get_htdemucs_output_dir(output_dir=output_dir, audio_path=audio_path),
            "vocals.wav"
        )

    def _get_temp_dir(self, audio_path: str) -> str:
        try:
            return audio_path.rsplit("/", 1)[0]
        except:
            return ""

    def _get_htdemucs_output_dir(self, output_dir: str, audio_path: str) -> str:
        return os.path.join(output_dir, "htdemucs", pathlib.Path(audio_path).stem)