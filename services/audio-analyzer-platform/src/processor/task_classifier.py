import re
import json
from jinja2 import Template
from typing import List, Dict, Tuple

from src.domain.enum import Enum
from src.domain.task import Task
from src.domain.llm_config import LLMConfig

from src.client.llm_client import AzureOpenAIClient

from src.processor.prompt_parser import PromptParser

class TaskClassifier:
    def classify(self, call_summary: str, source: str) -> Dict[str, Task]:
        pass

    @staticmethod
    def build_source(source: str, language: str) -> str:
        return f"{source}_{language}"

    @staticmethod
    def get_language_from_source(source: str) -> str:
        return source.split("_")[1]

class TaskClassifierImpl(TaskClassifier):
    def __init__(
            self,
            llm_client: AzureOpenAIClient,
            llm_config: LLMConfig,
            classifier_prompt: str,
            task_description: List[str]
    ):
        self.llm_client: AzureOpenAIClient = llm_client
        self.llm_config: LLMConfig = llm_config


        self.classifier_prompt_config: PromptParser = PromptParser.from_config_file(classifier_prompt)
        self.classifier_prompt: Template = Template(self.classifier_prompt_config.prompt)

        self.task_description: List[str] = task_description


    def classify(self, call_summary: str, source: str) -> Dict[str, Task]:
        try:
            prompt = self.classifier_prompt.render(
                call_summary=call_summary,
                task_description=self.task_description
            )
            completion = self.llm_client.client.chat.completions.create(
                extra_body={},
                model=self.llm_config.model_name,
                messages=[
                    {
                        Enum.ROLE: Enum.USER,
                        Enum.CONTENT: prompt
                    }
                ]
            )
            return self._parse_task(
                tasks=self._parse_llm_response(completion.choices[0].message.content)
            )
        except Exception as e:
            raise RuntimeError(
                f"TaskClassifierImpl::classify::Error while classifying task: {e}"
            ) 

    def _enhance_prompt(self, user_prompt: str) -> str:
        sys_prompt = f"Hãy chỉnh sửa đoạn prompt sau trở nên rõ ràng, đầy đủ thông tin và dễ hiểu hơn:\n{user_prompt}.\nLưu ý: Hãy chỉ trả về prompt mới và Phải liệt kê các Mô tả của các Nhóm danh mục, Danh mục và Người phụ trách."
        completion = self.llm_client.client.chat.completions.create(
            extra_body={},
            model=self.llm_config.model_name,
            messages=[
                {
                    Enum.ROLE: Enum.USER,
                    Enum.CONTENT: sys_prompt
                }
            ]
        )
        return  completion.choices[0].message.content

    def _parse_task(self, tasks: dict) -> Dict[str, Task]:
        def _parse(task: str):
            task_parts = task.split(self.classifier_prompt_config.splitter)
            if len(task_parts) == 3:
                category_group = task_parts[0].strip()
                category = task_parts[1].strip() if len(task_parts) > 1 else ""
                pic = task_parts[2].strip() if len(task_parts) > 2 else ""
                return Task(category_group, category, pic, "")
            else:
                task_parts = task.split(".")
                category_group = task_parts[0].strip()

                category = task_parts[1].strip() if len(task_parts) > 1 else ""
                pic = task_parts[2].strip() if len(task_parts) > 2 else ""
                return Task(category_group, category, pic, "")

        _recommend_task = tasks[Enum.NEXT_TASK_VI][Enum.RECOMMENDED_ACTIONS]
        _next_task = _parse(tasks[Enum.NEXT_TASK_VI][Enum.TASK_TYPE])
        if _next_task.group_category != Enum.processed_case:
            _next_task.description = tasks[Enum.NEXT_TASK_VI][Enum.TASK_DETAIL]
        return {
            Enum.TASK_TYPE: _parse(tasks[Enum.TASK_TYPE]),
            Enum.NEXT_TASK_VI: _next_task,
            Enum.RECOMMENDED_ACTIONS: Task(
                group_category="",
                category="",
                pic="",
                recommended_actions=_recommend_task
            )
        }

    def _get_available_categories(self, task_description: List[str]) -> Tuple[List[str], List[str], Dict[str, List[str]]]:
        tasks = []
        for task in task_description:
            task = Task.from_task_description(task)
            if task is not None:
                tasks.append(task)
        group_categories = list(set([task.group_category for task in tasks]))
        pic = list(set([task.pic for task in tasks]))
        categories = {
            group_category: [task.category for task in tasks if task.group_category == group_category]
            for group_category in group_categories
        }
        return group_categories, pic, categories

    def _parse_llm_response(self, llm_response: str) -> Dict[str, List[str]]:
        matches = re.findall(self.classifier_prompt_config.parsing_pattern, llm_response, re.DOTALL)
        return json.loads(matches[0])
