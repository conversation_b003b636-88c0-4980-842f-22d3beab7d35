import requests
from typing import Dict

from src.domain.enum import Enum
from src.domain.task import Task

from src.controller.requests.analyze_response import AnalyzeResponse

class VinpearlCase:
    CLOSED_CASE = "Closed"
    OPEN_CASE = "Open"
    VINPEARL = "Vinpearl"
    PHONE = "Phone"
    VINAI = "VinAI"

    def __init__(
        self,
        Subject: str,
        Description: str,
        Status: str,
        Sector__c: str,
        Cisco_Phone__c: str,
        RecordType: str,
        FullName__c: str,
        Origin: str,
        Category_Group__c: str,
        Category__c: str,
        customer_info: str,
        call_summary: str,
        next_actions: Dict[str, str],
        suggestion: str,
        quality_extraction: str,
    ):
        self.Subject: str = Subject
        self.Description: str = Description
        self.Status: str = Status
        self.Sector__c: str = Sector__c
        self.Cisco_Phone__c: str = Cisco_Phone__c
        self.RecordType: str = RecordType
        self.FullName__c: str = FullName__c
        self.Origin: str = Origin
        self.Category_Group__c: str = Category_Group__c
        self.Category__c: str = Category__c
        self.customer_info: str = customer_info
        self.call_summary: str = call_summary
        self.next_actions: Dict[str, str] = next_actions
        self.suggestion: str = suggestion
        self.quality_extraction: str = quality_extraction

    def to_dict(self):
        return {
            "Subject": self.Subject,
            "Description": self.Description,
            "Status": self.Status,
            "Sector__c": self.Sector__c,
            "Cisco_Phone__c": self.Cisco_Phone__c,
            "RecordType": self.RecordType,
            "FullName__c": self.FullName__c,
            "Origin": self.Origin,
            "Category_Group__c": self.Category_Group__c,
            "Category__c": self.Category__c,
            "customer_info": self.customer_info,
            "call_summary": self.call_summary,
            "next_actions": self.next_actions,
            "suggestion": self.suggestion,
            "quality_extraction": self.quality_extraction
        }
    


class SfmcClient:
    CATEGORIES_MAPPING = {
        'Góp ý [Vinpearl]': {
            'Góp ý - Sản phẩm': 'Góp ý - Dịch vụ [Vinpearl]', 
            'Góp ý - Dịch vụ': 'Góp ý - Sản phẩm [Vinpearl]'
        },
        'Hỗ trợ [Vinpearl]': {
            'Đặt mua Combo Khách sạn và Vé máy bay': 'Đặt mua Combo KS + VMB',
            'Đặt mua dịch vụ khác': 'Đặt mua dịch vụ khác', 
            # 'Đặt mua vé máy bay': '', 
            'Đặt mua Voucher/E-Ticket': 'Đặt mua Voucher, E-Ticket (VAP, SAF, Golf)',
            'Đặt phòng khách sạn': 'Đặt phòng khách sạn', 
            # 'Mua Tour Trọn gói': '',
            'Sử dụng Voucher đã mua': 'Sử dụng Voucher đã mua',
            'Thay đổi Đặt phòng/Đơn hàng/Vé': 'Thay đổi Đặt phòng/Đơn hàng', 
            'Kiểm tra tình trạng Voucher/E-ticket': 'Kiểm tra tình trạng Voucher/E-ticket', 
            'Kiểm tra thông tin Booking': 'Kiểm tra thông tin Booking',
            'Xác nhận thanh toán/đơn hàng': 'Xác nhận thanh toán/đơn hàng', 
            'Hủy Booking': 'Hủy Booking',
            'Thao tác đặt phòng trên App/Web': 'Thao tác đặt phòng trên App/Web', 
            'CBT-Hỗ trợ về đặt phòng': 'CBT-Hỗ trợ về đặt phòng',
            'CBT-Yêu cầu thăm căn/thẩm định căn': 'CBT-Yêu cầu thẩm căn/ thẩm định căn',
            'CBT-Tiến độ thanh toán thu nhập': 'CBT-Tiến độ thanh toán thu nhập cho thuê'
        },
        'Khiếu nại [Vinpearl]': {
            'Khiếu nại khác': 'Khiếu nại khác', 
            'Khiếu nại về quá trình sử dụng dịch vụ': 'Khiếu nại về quá trình sử dụng dịch vụ', 
            'Khiếu nại về thái độ nhân viên': 'Khiếu nại về thái độ nhân viên', 
            # 'CBT-Khiếu nại Quyền lợi': '', 
            # 'CBT-Khiếu nại Hợp đồng mua bán': '',
            'Thông tin trên website' : 'Thông tin trên website',
            'Khiếu nại về quá trình đặt dịch vụ': 'Khiếu nại về quá trình đặt dịch vụ', 
            'Cơ sở vật chất': 'Cơ sở vật chất', 
            # 'Chính sách của Vinpearl': ''
        },
        'Tư vấn [Vinpearl]': {
            'Tư vấn Combo Khách sạn + Vé máy bay': 'Tư vấn Combo Khách sạn + Vé máy bay', 
            'Tư vấn thông tin phòng khách sạn': 'Tư vấn thông tin phòng khách sạn', 
            'Tư vấn Tour trọn gói': 'Tư vấn Tour trọn gói', 
            'Tư vấn Vé máy bay': 'Tư vấn Vé máy bay', 
            'Tư vấn Voucher/E-Ticket': 'Tư vấn Voucher, E-Ticket (VAP, SAF, Golf)', 
            # 'Tư vấn đặt phòng cho CBNV Vingroup': '', 
            'Tư vấn Phụ thu': 'Tư vấn Phụ thu', 
            'Đón tiễn Sân bay': 'Đón tiễn Sân bay', 
            'Thủ tục thanh toán': 'Thủ tục thanh toán', 
            'Yêu cầu hợp tác của Đại lý': 'Yêu cầu hợp tác của Đại lý', 
            'CBT-Tư vấn thông tin': 'CBT-Thủ tục chuyển nhượng', 
            # 'CBT-Báo cáo tài chính': '', 
            'CBT-Thủ tục chuyển nhượng': 'CBT-Thủ tục chuyển nhượng', 
            'CBT-Cập nhật thông tin Chủ sở hữu': 'CBT-Cập nhật thay đổi thông tin CSH', 
            'CBT-Tiến độ bàn giao': 'CBT-Tiến độ bàn giao', 
            'CBT-Tiến độ cấp giấy chứng nhận đất': 'CBT-Tiến độ cấp giấy chứng nhận quyền sử dụng đất'
        },
        'Quick case [Vinpearl]': {
            'Quick case - Liên hệ nhầm': 'Quick case - Liên hệ nhầm [Vinpearl]', 
            'Quick case - Quấy rối': 'Quick case - Quấy rối [Vinpearl]', 
            'Quick case - Chatbot': 'Quick case - chatbot', 
            'Quick case - SPAM/Trùng sự vụ': 'Quick case - SPAM/trùng sự vụ [Vinpearl]'
        }
    }

    def __init__(
            self,
            endpoint_url: str,
            api_key: str,
    ):
        self.endpoint_url: str = endpoint_url
        self.api_key: str = api_key
        self.headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8',
            'authKey': self.api_key
        }

    def push(self, analyze_response: AnalyzeResponse) -> None:
        """
        Pushes the analyze response to the SFMC endpoint.

        Args:
            analyze_response (AnalyzeResponse): The analyze response to be pushed.
        """
        print("analyze_response.call_summary.customer_info ", analyze_response.call_summary.customer_info)
        case = VinpearlCase(
            Subject=analyze_response.call_summary.summary.split(".")[0],
            Description=analyze_response.call_summary.customer_requests[Enum.MAIN_REQUEST],
            Status=self._get_status(analyze_response.case.next_task),
            Sector__c=VinpearlCase.VINPEARL,
            Cisco_Phone__c=analyze_response.customer_phone_number,
            RecordType=VinpearlCase.VINPEARL,
            FullName__c=self._get_fullname(analyze_response.call_summary.customer_info),
            Origin=VinpearlCase.PHONE,
            Category_Group__c=analyze_response.case.task_type.group_category,
            Category__c=self._get_category(
                category_group=analyze_response.case.task_type.group_category,
                category=analyze_response.case.task_type.category
            ),
            customer_info=self._get_customer_info(analyze_response.call_summary.customer_info),
            call_summary=analyze_response.call_summary.summary if analyze_response.call_summary else "",
            next_actions=self._get_next_actions(analyze_response.case.next_task) if analyze_response.case else {
                Enum.GROUP_CATEGORY: Enum.processed_case
            },
            suggestion=analyze_response.case.next_task.description if analyze_response.case else "",
            quality_extraction=analyze_response.analyze_status.status if analyze_response.analyze_status else ""
        )

        payload = self._build_payload(case)
        response = requests.post(self.endpoint_url, headers=self.headers, json=payload)

        print(f"Sending request to {self.endpoint_url} with payload: {payload}")
        print(f"Response status code: {response.json()}")

        return response.json()

    def _build_payload(self, case: VinpearlCase) -> Dict[str, str]:
        return case.to_dict()
    
    def _get_status(self, next_task: Task) -> str:
        if Enum.processed_case in next_task.group_category:
            return VinpearlCase.CLOSED_CASE
        return VinpearlCase.OPEN_CASE

    def _get_category(self, category_group: str, category: str) -> str:
        return SfmcClient.CATEGORIES_MAPPING.get(category_group, {}).get(category, category)

    
    def _get_next_actions(self, next_task: Task) -> Dict[str, str]:
        """
        Returns the next actions as a string.
        
        Args:
            next_task (Task): The next task to be performed.
        
        Returns:
            str: The next actions as a string.
        """
        return {
            Enum.GROUP_CATEGORY: next_task.group_category,
            Enum.CATEGORY: next_task.category,
            Enum.PIC: next_task.pic,
            Enum.DESCRIPTION: next_task.description,
        }
    

    def _get_customer_info(self, customer_info: Dict[str, str]) -> str:
        """
        Joins customer info into a single string.
        
        Args:
            customer_info (List[str]): List of customer information strings.
        
        Returns:
            str: Joined customer information.
        """
        customer_info_str = ""
        for key, value in customer_info.items():
            if value is None or len(value) == 0:
                value = "Chưa có thông tin" 
            customer_info_str += f"\n- {key}: {value}"
        
        return customer_info_str
    

    def _get_fullname(self, customer_info: Dict[str, str]) -> str:
        """
        Returns the full name from customer info.
        
        Args:
            customer_info (Dict[str, str]): Dictionary containing customer information.
        
        Returns:
            str: Full name of the customer.
        """
        return customer_info.get(Enum.CUSTOMER_NAME, "Chưa có thông tin") if customer_info else "Chưa có thông tin"

