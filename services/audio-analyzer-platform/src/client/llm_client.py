from openai import AzureOpenAI, OpenAI


class AzureOpenAIClient:
    def __init__(
            self,
            api_version: str,
            azure_endpoint: str,
            api_key: str

    ):
        self.api_version: str = api_version
        self.azure_endpoint: str = azure_endpoint
        self.api_key: str = api_key
        self.client: AzureOpenAI = AzureOpenAI(
            api_version=api_version,
            azure_endpoint=azure_endpoint,
            api_key=api_key,
        )

class OpenAIClient:
    def __init__(
            self,
            api_version: str,
            endpoint: str,
            api_key: str
    ):
        self.api_version: str = api_version
        self.endpoint: str = endpoint
        self.api_key: str = api_key
        self.client: OpenAI = OpenAI(
            base_url=endpoint,
            api_key=api_key,
        )
