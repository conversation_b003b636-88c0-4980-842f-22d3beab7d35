import os
import io
import sys
import torch
import datetime
import gpustat
import psutil
import requests
import librosa
from collections import Counter
import soundfile as sf
from datetime import datetime
from typing import List, Iterable
# from flask import Blueprint, Flask
from urllib.parse import urlparse
from typing import Dict
from dependency_injector.containers import Container
import numpy as np
from numpy import ndarray
from io import BytesIO
from fastapi import UploadFile

from src.domain.transcription import Transcription
from src.domain.exceptions import VException
from src.domain.enum import Enum

def get_device(using_gpu: bool) -> torch.device:
    return torch.device("cuda") if using_gpu and torch.cuda.is_available() else torch.device("cpu")


def create_injector(container: Container, modules: list):
    mode = os.environ['APP_MODE']

    modules = [*modules]

    print(f"Mode: {mode}")
    print(f"Loaded config: conf/{mode}.yml")
    print(f"Modules: {modules}")

    # Try to load from conf directory first
    config_path = f"conf/{mode}.yml"
    if not os.path.exists(config_path):
        # If not found, try loading from config.yaml in the root directory
        config_path = "config.yaml"
        print(f"Config not found at conf/{mode}.yml, using {config_path} instead")

    container.config.from_yaml(config_path)
    container.wire(modules)

    print("Wire completed")

    return container

# def create_http_server(blueprints: List[Blueprint]) -> Flask:
#     app = Flask(__name__)
#     app.url_map.strict_slashes = False
#     app.debug = False

#     for blueprint in blueprints:
#         app.register_blueprint(blueprint)

#     return app


def get_current_time():
    return datetime.now().strftime("%d/%m/%Y %H:%M:%S")


def get_gpu_device_info() -> List[str]:
    gpu_stats = gpustat.GPUStatCollection.new_query()
    infos = []
    for gpu in gpu_stats.gpus:
        infos.append(
            f"Time: {get_current_time()}, " +
            f"GPU {gpu.index}: {gpu.name}, " +
            f"Utilization: {gpu.utilization}%, " +
            f"Memory available: {gpu.memory_available} GB, " +
            f"Used memory: {gpu.memory_used} GB"
        )

    return infos


def get_cpu_device_info() -> str:
    memory = psutil.virtual_memory()

    info = f"Time: {get_current_time()}, " \
           f"Utilization: {memory.percent}%, " \
           f"Memory available: {round(memory.available / (1024 ** 2), 2)} MB, " \
           f"Used memory: {round(memory.used / 1024 ** 2, 2)} MB"

    return info


def get_disk_info() -> str:
    disk_usage = psutil.disk_usage('/')
    info = f"Time: {get_current_time()}, " \
           f"Utilization: {disk_usage.percent}%, " \
           f"Disk available: {round(disk_usage.free / (1024 ** 3), 2)} GB, " \
           f"Used disk: {round(disk_usage.used / (1024 ** 3), 2)} GB"

    return info


def chunk_iterable(iterable: Iterable, chunk_size: int) -> Iterable:
    chunks = iterable
    if isinstance(iterable, dict):
        chunks = []
        keys = list(iterable.keys())
        for i in range(0, len(iterable), chunk_size):
            chunks.append({
                key: iterable[key]
                for key in keys[i:i + chunk_size]
            })
    elif isinstance(iterable, list):
        # For lists, tuples, strings, and other iterables
        chunks = []
        for i in range(0, len(iterable), chunk_size):
            chunks.append(iterable[i:i + chunk_size])

    return chunks

def replace_meta_predicted(values: List[str]) -> List[str]:
    values = [
        value.replace("/meta_", "/").replace("/mena_", "/")
        for value in values
    ]
    _values = []
    for value in values:
        for i in range(0, 99):
            value = value.replace(f"/vi_{str(i).zfill(2)}_", "/")
        _values.append(value)
    values = _values

    return values


def is_url(path_or_url: str) -> bool:
    parsed = urlparse(path_or_url)
    return parsed.scheme in ("http", "https") and bool(parsed.netloc)



def load_audio_from_url(url: str, sr=None):
    # Step 1: Download the audio to memory
    response = requests.get(url)
    response.raise_for_status()  # Ensure the request succeeded

    # Step 2: Read audio from memory using soundfile
    audio_buffer = io.BytesIO(response.content)
    audio, samplerate = sf.read(audio_buffer)

    # # Step 3: Optionally resample using librosa
    # if sr is not None and samplerate != sr:
    #     audio = librosa.resample(audio.T, orig_sr=samplerate, target_sr=sr)
    #     samplerate = sr
    #     audio = audio.T

    return audio, samplerate

def load_audio(url: str, file_name: str) -> str:
    if not os.path.exists(file_name):
        os.system(f"wget {url} -O {file_name}")
    return file_name



from src.domain.call_log import CallLog
from src.domain.transcription import Transcription
from src.domain.task import Task
from src.domain.call_summary import CallSummary
from typing import Tuple, List, Dict
import pandas as pd

def get_result(
        transcripts: List[Transcription],
        summarized_conversation: CallSummary,
        next_task: Task,
        recommended_actions: str,
) -> Tuple[str, str, str, str, str, str, str]:
    prefix = "\n"
    transcript_str = ""
    customer_info_str = ""
    customer_requests_str = ""
    agent_responses_str = ""
    for i, transcript in enumerate(transcripts):
        transcript_str += f"{prefix}{transcript.speaker}: {transcript.text}"
    for customer_info in summarized_conversation.customer_info.items():
        customer_info_str += f"{prefix}- {customer_info}"

    for customer_requests in summarized_conversation.customer_requests:
        customer_requests_str += f"{prefix}- {customer_requests}"

    for agent_responses in summarized_conversation.agent_responses:
        agent_responses_str += f"{prefix}- {agent_responses}"

    next_task_str = next_task.to_str()
    language = transcripts[0].language
    
    return transcript_str, customer_info_str, customer_requests_str, agent_responses_str, next_task_str, language, recommended_actions



def save_to_file(logs: Dict[str, CallLog], csv_file_path: str):
    data = {
        "audio_file": [],
        "transcript": [],
        "customer_info": [],
        "customer_requests": [],
        "agent_responses": [],
        "next_task": [],
        "recommended_actions": [],
        "summary_content": [],
        "language": []
    }
    for audio_file, call_log in logs.items():
        transcript_str, customer_info_str, customer_requests_str, agent_responses_str, next_task_str, language, recommended_actions = get_result(
            transcripts=call_log.transcriptions,
            summarized_conversation=call_log.call_summary,
            next_task=call_log.next_task,
            recommended_actions=call_log.recommended_actions
        )
        data["audio_file"].append(audio_file)
        data["transcript"].append(transcript_str)
        data["customer_info"].append(customer_info_str)
        data["customer_requests"].append(customer_requests_str)
        data["agent_responses"].append(agent_responses_str)
        data["next_task"].append(next_task_str)
        data["language"].append(language)
        data["recommended_actions"].append(recommended_actions)
        data["summary_content"].append(call_log.call_summary.summary)

    df = pd.DataFrame(data)
    df.to_csv(csv_file_path, index=False)





def get_most_common_language(transcriptions: List[Transcription]) -> str:
    if not transcriptions:
        return Enum.LANG_EMPTY
    languages = [t.language for t in transcriptions if hasattr(t, "language") and t.language]
    if not languages:
        return Enum.LANG_EMPTY
    most_common = Counter(languages).most_common(1)
    return most_common[0][0] if most_common else Enum.LANG_VI



def _parse_audio_file(audio_file: UploadFile) -> ndarray:
    audio_bytes = audio_file.file.read()  # Đọc nội dung file dưới dạng bytes
    audio_stream = BytesIO(audio_bytes)

    try:
        audio, sr = sf.read(audio_stream)  # data là ndarray
        audio = _create_louder_audio(audio)
        

    except RuntimeError as e:
        raise ValueError(f"Could not read file: {e}")

    print(audio)

    return audio


def _create_louder_audio(audio: ndarray) -> ndarray:
    # Bước 2: Tính RMS hiện tại
    current_rms = _compute_rms(audio)

    # Bước 3: Chọn RMS mục tiêu — ví dụ: 0.1 là âm lượng khá rõ, không bị méo
    target_rms = 0.1

    # Bước 4: Tính hệ số khuếch đại (boost factor)
    if current_rms == 0:
        boost_factor = 1.0  # Tránh chia cho 0
    else:
        boost_factor = target_rms / current_rms

    # Bước 5: Áp dụng hệ số và giới hạn tránh clipping
    print(f"Current RMS: {current_rms}, Target RMS: {target_rms}, Boost Factor: {boost_factor}")
    boosted_audio = audio * boost_factor
    boosted_audio = np.clip(boosted_audio, -1.0, 1.0)

    return boosted_audio



def _compute_rms(audio):
    return np.sqrt(np.mean(audio**2))




from pydub import AudioSegment, silence
import numpy as np

def ndarray_to_audiosegment(data: np.ndarray, sample_rate: int = 16000, channels: int = 1) -> AudioSegment:
    # Ensure the array is in the correct shape (mono or stereo)
    if data.ndim == 2:
        # shape: (channels, samples) → transpose to (samples, channels)
        data = data.T
    elif data.ndim == 1 and channels == 2:
        raise ValueError("Stereo audio requires 2D ndarray with shape (2, samples)")

    # Normalize if float (-1.0 to 1.0) and convert to int16
    if np.issubdtype(data.dtype, np.floating):
        data = (data * 32767).astype(np.int16)
    elif data.dtype != np.int16:
        data = data.astype(np.int16)

    # Convert ndarray to raw bytes
    raw_data = data.tobytes()

    # Create AudioSegment
    audio_segment = AudioSegment(
        data=raw_data,
        sample_width=2,       # 2 bytes for int16
        frame_rate=sample_rate,
        channels=channels
    )

    return audio_segment

def audiosegment_to_ndarray(audio: AudioSegment) -> np.ndarray:
    # Get raw audio data as bytes
    raw_data = audio.raw_data

    # Determine the sample width and dtype
    sample_width = audio.sample_width  # in bytes (usually 2)
    dtype_map = {1: np.int8, 2: np.int16, 4: np.int32}
    dtype = dtype_map[sample_width]

    # Convert bytes to ndarray
    samples = np.frombuffer(raw_data, dtype=dtype)

    # Reshape based on channels
    if audio.channels > 1:
        samples = samples.reshape((-1, audio.channels))  # shape: (samples, channels)

    return samples

def chunk_audio(audio: ndarray, sample_rate: int, chunk_duration_in_seconds: int, transcriptions: List[Transcription]) -> List[ndarray]:
    audio_seg = ndarray_to_audiosegment(audio, sample_rate)
    chunks = []
    last_end_ms = 0
    end_ms = -np.inf
    for transcription in transcriptions:
        start_ms = int(transcription.start * 1000)
        end_ms = int(transcription.end * 1000)
        if end_ms > (len(chunks) + 1) * chunk_duration_in_seconds * 1000:
            chunks.append(audio_seg[last_end_ms:end_ms])
            # audio_seg[last_end_ms:end_ms].export("chunk.wav", format="wav")/
            last_end_ms = end_ms

    if last_end_ms < end_ms:
        chunks.append(audio_seg[last_end_ms:end_ms])
    chunks = [audio_seg] if len(chunks) == 0 else chunks
    # Convert to ndarray
    return [audiosegment_to_ndarray(seg) for seg in chunks]