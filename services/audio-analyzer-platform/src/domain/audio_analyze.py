from typing import List

from src.domain.task import Task
from src.domain.enum import Enum
from src.domain.transcription import Transcription
from src.domain.call_summary import CallSummary
from src.domain.case import Case

class AudioAnalysis:
    TRANSCRIPTIONS = "transcriptions"
    CALL_SUMMARY = "call_summary"
    CASE = "case"

    def __init__(
            self,
            transcriptions: List[Transcription],
            call_summary: CallSummary,
            case: Case
    ):
        self.transcriptions: List[Transcription] = transcriptions
        self.call_summary: CallSummary = call_summary
        self.case: Case = case

    def to_dict(self) -> dict:
        return {
            AudioAnalysis.TRANSCRIPTIONS: [t.to_dict() for t in self.transcriptions],
            AudioAnalysis.CALL_SUMMARY: self.call_summary.to_dict(),
            AudioAnalysis.CASE: self.case.to_dict()
        }
    