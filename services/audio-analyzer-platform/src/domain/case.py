import re

from src.domain.task import Task

class Case:
    TASK_TYPE = "task_type"
    NEXT_TASK = "next_task"
    RECOMMENDED_TASK = "recommended_task"
    
    def __init__(
            self,
            task_type: Task,
            next_task: Task,
            recommended_task: Task,
    ):
        self.task_type: Task = task_type
        self.next_task: Task = next_task
        self.recommended_task: Task = recommended_task

    def to_dict(self) -> dict:
        return {
            Case.TASK_TYPE: self.task_type.to_dict(),
            Case.NEXT_TASK: self.next_task.to_dict(),
            Case.RECOMMENDED_TASK: self.recommended_task.to_dict()
        }