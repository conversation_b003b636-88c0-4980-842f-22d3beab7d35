from typing import List, Dict

from src.domain.task import Task

class CallSummary:
    CUSTOMER_INFO = "customer_info"
    CUSTOMER_REQUESTS = "customer_requests"
    AGENT_RESPONSES = "agent_responses"
    SUMMARY = "summary"
    IS_INTERRUPTED = "is_interrupted"

    def __init__(
            self,
            customer_info: Dict[str, str],
            customer_requests: Dict[str, str],
            agent_responses: List[str],
            summary: str,
            is_interrupted: bool = False,
    ):
        self.customer_info: Dict[str, str] = customer_info
        self.customer_requests: Dict[str, str] = customer_requests
        self.agent_responses: List[str] = agent_responses
        self.is_interrupted: bool = is_interrupted
        self.summary: str = summary


    def to_str(self) -> str:
        prefix = "\n- "
        customer_info_str = prefix.join(self.customer_info)
        customer_requests_str = prefix.join(self.customer_requests)
        agent_responses_str = prefix.join(self.agent_responses)
        return  f"Thông tin khách hàng: {prefix}{customer_info_str}" \
                f"\nYêu cầu của khách hàng: {prefix}{customer_requests_str}" \
                f"\nPhản hồi của tổng đài viên: {prefix}{agent_responses_str}" \
                f"\nTóm tắt cuộc gọi: {self.summary}"

    def to_dict(self) -> dict:
        return {
            CallSummary.CUSTOMER_INFO: self.customer_info,
            CallSummary.CUSTOMER_REQUESTS: self.customer_requests,
            CallSummary.AGENT_RESPONSES: self.agent_responses,
            CallSummary.SUMMARY: self.summary,
            CallSummary.IS_INTERRUPTED: self.is_interrupted
        }

    @staticmethod
    def from_dict(data: dict) -> 'CallSummary':
        return CallSummary(
            customer_info=data.get(CallSummary.CUSTOMER_INFO, []),
            customer_requests=data.get(CallSummary.CUSTOMER_REQUESTS, {}),
            agent_responses=data.get(CallSummary.AGENT_RESPONSES, []),
            summary=data.get(CallSummary.SUMMARY, ""),
            is_interrupted=data.get(CallSummary.IS_INTERRUPTED, False)
        )