from typing import List

from src.domain.task import Task
from src.domain.transcription import Transcription
from src.domain.call_summary import CallSummary

class CallLog:
    DEFAULT_ID = "default_id"

    def __init__(
            self,
            call_summary: CallSummary,
            task: Task,
            next_task: Task,
            transcriptions: List[Transcription] = [],
            customer_requests: List[str] = [],
            agent_responses: List[str] = [],
            recommended_actions: str = ""
    ):
        self.call_summary: CallSummary = call_summary
        self.task: Task = task
        self.next_task: Task = next_task
        self.transcriptions: List[Transcription] = transcriptions
        self.customer_requests: List[str] = customer_requests
        self.agent_responses: List[str] = agent_responses
        self.recommended_actions: str = recommended_actions


    def to_str(self) -> str:
        prefix = "\n- "
        call_summary_str = self.call_summary.to_str()
        task_str = self.task.to_str()
        next_task_str = self.next_task.to_str()
        _str =  f"{call_summary_str}" \
                f"\nTác v<PERSON> hiện tại: {task_str}" \
                f"\nT<PERSON>c vụ tiếp theo: {next_task_str}"

        if self.customer_requests is not None and len(self.customer_requests) > 0:
            _str += f"\nYêu cầu của khách hàng: {prefix}{prefix.join(self.customer_requests)}"

        if self.agent_responses is not None and len(self.agent_responses) > 0:
            _str += f"\nPhản hồi từ tổng đài viên: {prefix}{prefix.join(self.agent_responses)}"

        if self.recommended_actions is not None and len(self.recommended_actions) > 0:
            _str += f"\nĐề xuất: {self.recommended_actions}"

        return _str

