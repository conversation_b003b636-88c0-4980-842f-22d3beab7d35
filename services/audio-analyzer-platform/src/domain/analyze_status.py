
from src.domain.enum import Enum

class AnalyzeStatus:
    EXCELLENT = "Excellent"
    ACCEPTABLE = "Acceptable"
    NEEDS_REVIEW = "Needs Review"

    OVERALL_QUALITY = "overall_quality"
    SUGGESTIONS = "suggestions"

    def __init__(
            self,
            status: str,
            detail: str,
    ):
        self.status: str = status
        self.detail: str = detail

    
    def to_dict(self) -> dict:
        return {
            Enum.STATUS: self.status,
            Enum.DETAIL: self.detail
        }