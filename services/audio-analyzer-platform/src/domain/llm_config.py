class LLMConfig:
    def __init__(
            self,
            model_name: str,
            temperature: float = 0.0,
            max_tokens: int = 32768,
            sampling: bool = False,
    ):
        self.model_name: str = model_name
        self.temperature: float = temperature
        self.max_tokens: int = max_tokens
        self.sampling: bool = sampling  

    def to_dict(self) -> dict:
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "sampling": self.sampling
        }