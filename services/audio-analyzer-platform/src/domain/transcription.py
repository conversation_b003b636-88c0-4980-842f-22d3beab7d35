from src.domain.enum import Enum


class Transcription:
    SPEAKER = "speaker"
    START = "start"
    END = "end"
    TEXT = "text"
    LANGUAGE = "language"


    def __init__(
            self,
            speaker: str,
            start: float,
            end: float,
            text: str,
            language: str = Enum.LANG_VI
    ):
        self.speaker: str = speaker
        self.start: float = start
        self.end: float = end
        self.text: str = text
        self.language: str = language

    def to_dict(self) -> dict:
        return {
            Transcription.SPEAKER: self.speaker,
            Transcription.START: self.start,
            Transcription.END: self.end,
            Transcription.TEXT: self.text,
            Transcription.LANGUAGE: self.language
        }
    
    @staticmethod
    def from_dict(data: dict) -> 'Transcription':
        return Transcription(
            speaker=data.get(Transcription.SPEAKER, ""),
            start=data.get(Transcription.START, 0.0),
            end=data.get(Transcription.END, 0.0),
            text=data.get(Transcription.TEXT, ""),
            language=data.get(Transcription.LANGUAGE, Enum.LANG_VI)
        )