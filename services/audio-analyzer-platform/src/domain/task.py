import re

from src.domain.enum import Enum

class Task:
    DEFAULT_ID = "default_id"

    def __init__(
            self,
            group_category: str,
            category: str,
            pic: str,
            task_id: str = DEFAULT_ID,
            description: str = "",
            feature: str = "",
            recommended_actions: str = ""
    ):
        self.group_category: str = group_category
        self.category: str = category
        self.pic: str = pic
        self.task_id: str = task_id
        self.description: str = description
        self.feature: str = feature
        self.recommended_actions: str = recommended_actions

    @staticmethod
    def from_task_description(task_description: str, task_id: str = DEFAULT_ID):
        pattern = r"Nhóm danh mục:\s*(.+?)\s*Danh mục:\s*(.+?)\s*Người phụ trách:\s*(.+?)\s*Đặc điểm phân biệt:\s*(.+?)\s*Mô tả công việc:\s*(.+)"

        match = re.search(pattern, task_description)

        if match:
            result = {
                Enum.GROUP_CATEGORY: match.group(1).strip(),
                Enum.CATEGORY: match.group(2).strip(),
                Enum.PIC: match.group(3).strip(),
                Enum.FEATURE: match.group(4).strip(),
                Enum.DESCRIPTION: match.group(5).strip(),
            }

            return Task(
                group_category=result[Enum.GROUP_CATEGORY],
                category=result[Enum.CATEGORY],
                pic=result[Enum.PIC],
                task_id=task_id,
                description=result[Enum.DESCRIPTION],
                feature=result[Enum.FEATURE]
            )
        else:
            return None

    def to_str(self) -> str:
        prefix = "\n- "
        _str = ""
        if self.group_category is not None and len(self.group_category) > 0:
            _str =  f"{prefix}Nhóm danh mục: {self.group_category}"
        if self.category is not None and len(self.category) > 0:
            _str += f"{prefix}Danh mục: {self.category}"
        if self.pic is not None and len(self.pic) > 0:
            _str += f"{prefix}Người phụ trách: {self.pic}"
        if self.feature is not None and len(self.feature) > 0:
            _str += f"{prefix}Đặc điểm phân biệt: {self.feature}"
        if self.description is not None and len(self.description) > 0:
            _str += f"{prefix}Mô tả công việc: {self.description}"
        if self.recommended_actions is not None and len(self.recommended_actions) > 0:
            _str += f"{prefix}Đề xuất: {self.recommended_actions}"

        return _str
    
    def to_dict(self) -> dict:
        return {
            Enum.GROUP_CATEGORY: self.group_category,
            Enum.CATEGORY: self.category,
            Enum.PIC: self.pic,
            Enum.FEATURE: self.feature,
            Enum.DESCRIPTION: self.description,
            Enum.RECOMMENDED_ACTIONS: self.recommended_actions
        }