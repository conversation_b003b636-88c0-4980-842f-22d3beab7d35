class TranscriberModelConfig:
    def __init__(
            self,
            model_path: str,
            chunk_size: int,
            left_context_size: int,
            right_context_size: int,
            total_batch_duration: int,
            is_using_gpu: bool = True
    ):
        self.model_path: str = model_path
        self.chunk_size: int = chunk_size
        self.left_context_size: int = left_context_size
        self.right_context_size: int = right_context_size
        self.total_batch_duration: int = total_batch_duration
        self.is_using_gpu: bool = is_using_gpu


