from fastapi import APIRouter, UploadFile, Depends
from dependency_injector.wiring import inject, Provide
from src.module.application_container import ApplicationContainer

router = APIRouter(prefix="/api")

@router.post("/upload-audio")
@inject
def upload_audio(
    audio_file: UploadFile,
    audio_service = Depends(Provide[ApplicationContainer.audio_analyzer_service]),
):
    # Xử lý audio
    audio_data = audio_file.read()
    # processing_result = audio_service.process_audio(audio_data)

    return {
        "filename": audio_file.filename,
        "processing": "processing_result"
    }