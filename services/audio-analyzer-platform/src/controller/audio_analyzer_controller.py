
from dependency_injector.wiring import Provide, inject

from src.controller.requests.analyze_request import Ana<PERSON><PERSON>Request
from src.module.application_container import ApplicationContainer
from src.service.audio_analyzer_service import AudioAnalyzerService

@inject
def get_audio_analyzer_service(
    audio_analyzer_service: AudioAnalyzerService = Provide[
        ApplicationContainer.audio_analyzer_service
    ],
):
    return audio_analyzer_service


def _analyze_audio(
    audio_analyzer_service: AudioAnalyzerService, analyze_request: AnalyzeRequest
):
    analyze_response = audio_analyzer_service.analyze(analyze_request)

    return {
        audio_url: analyze_response.to_dict()
        for audio_url, analyze_response in analyze_response.items()
    }

