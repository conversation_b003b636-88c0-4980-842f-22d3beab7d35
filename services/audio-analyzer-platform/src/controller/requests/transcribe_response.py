from typing import List, Dict

from src.domain.transcription import Transcription

class TranscribeResponse:
    def __init__(self, transcriptions: Dict[str, List[Transcription]], source: str, statuses: Dict[str, str] = {}):
        """
        Args:
            transcriptions (Dict[str, List[Transcription]]): Dictionary of transcriptions with speaker IDs as keys.
            statuses (Dict[str, str]): Dictionary of statuses with speaker IDs as keys.
            source (str): Source of the audio (e.g., "customer", "agent").
        """
        self.transcriptions: Dict[str, List[Transcription]] = transcriptions
        self.statuses: Dict[str, str] = statuses
        self.source: str = source

    def to_dict(self) -> dict:
        return {
            "transcriptions": {speaker_id: [t.to_dict() for t in transcriptions] for speaker_id, transcriptions in self.transcriptions.items()},
            "statuses": self.statuses,
            "source": self.source
        }