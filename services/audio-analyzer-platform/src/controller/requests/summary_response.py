from src.domain.call_summary import CallSummary

class SummaryResponse:
    def __init__(
            self,
            call_summary: CallSummary
        ):
        """
        Args:
            call_summary (CallSummary): Summary of the call.
        """
        self.call_summary: CallSummary = call_summary

    def to_dict(self) -> dict:
        return {
            "call_summary": self.call_summary.to_dict()
        }