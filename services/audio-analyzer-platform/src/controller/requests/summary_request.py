from typing import List

from src.domain.enum import Enum
from src.domain.transcription import Transcription

class SummaryRequest:
    def __init__(
            self, 
            transcriptions: List[Transcription],
            source: str,
            language: str = Enum.LANG_VI
        ):
        """
        Args:
            transcriptions (List[Transcription]): List of transcriptions to summarize.
            source (str): Source of the audio (e.g., "customer", "agent").
        """
        self.transcriptions: List[Transcription] = transcriptions
        self.source: str = source
        self.language: str = language