from numpy import ndarray
from typing import List, Dict


# class AnalyzeRequest(BaseModel):
#     audio_urls: List[str]
#     source: str
#     customer_phone_numbers: Dict[str, str] = {}
#     num_speakers: int = 2
#     max_speakers: int = 3
#     audio_files: Dict[str, ndarray] = {}
#     verbose: bool = False

class AnalyzeRequest:
    def __init__(
            self, 
            audio_urls: List[str], 
            source: str, 
            customer_phone_numbers: Dict[str, str],
            num_speakers: int, 
            max_speakers: int, 
            audio_files: Dict[str, ndarray] = {},
            verbose: bool = False
            ):
        """
        Args:
            audio_urls (List[str]): List of audio URLs to analyze.
            source (str): Source of the audio (e.g., "customer", "agent").
            num_speakers (int): Minimum number of speakers to detect.
            max_speakers (int): Maximum number of speakers to detect.
            verbose (bool): If True, print detailed information during processing.
        """
        self.audio_urls: List[str] = audio_urls
        self.audio_files: Dict[str, ndarray] = audio_files
        self.source: str = source
        self.customer_phone_numbers: Dict[str, str] = customer_phone_numbers
        self.num_speakers: int = num_speakers
        self.max_speakers: int = max_speakers
        self.verbose: bool = verbose
    