from numpy import ndarray
from typing import List, Dict


class TranscribeRequest:
    def __init__(
        self, 
        audio_urls: List[str],
        source: str,
        num_speakers: int,
        max_speakers: int,
        audio_files: Dict[str, ndarray] = {},
        verbose: bool = False
    ):
        """
        Args:
            audio_urls (List[str]): List of audio URLs to transcribe.
            source (str): Source of the audio (e.g., "customer", "agent").
            num_speakers (int): Minimum number of speakers to detect.
            max_speakers (int): Maximum number of speakers to detect.
            verbose (bool): If True, print detailed information during processing.
        """
        self.audio_urls: List[str] = audio_urls
        self.source: str = source
        self.num_speakers: int = num_speakers
        self.max_speakers: int = max_speakers
        self.audio_files: Dict[str, ndarray] = audio_files
        self.verbose: bool = verbose
    