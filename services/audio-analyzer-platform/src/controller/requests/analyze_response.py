from typing import List

from src.domain.enum import Enum
from src.domain.case import Case
from src.domain.call_summary import Call<PERSON><PERSON>mary
from src.domain.transcription import Transcription
from src.domain.analyze_status import AnalyzeStatus

class AnalyzeResponse:
    def __init__(
            self, 
            transcriptions: List[Transcription], 
            call_summary: CallSummary,
            case: Case,
            analyze_status: AnalyzeStatus,
            customer_phone_number: str = ""
    ):
        """
        Args:
            transcriptions (Dict[str, List[Transcription]]): Dictionary of transcriptions with speaker IDs as keys.
            statuses (Dict[str, str]): Dictionary of statuses with speaker IDs as keys.
            source (str): Source of the audio (e.g., "customer", "agent").
        """
        self.transcriptions: List[Transcription] = transcriptions
        self.call_summary: CallSummary = call_summary
        self.case: Case = case
        self.analyze_status: AnalyzeStatus = analyze_status
        self.customer_phone_number: str = customer_phone_number

    def to_dict(self) -> dict:
        return {
            Enum.TRANSCRIPTIONS: [transcription.to_dict() for transcription in self.transcriptions],
            Enum.CALL_SUMMARY: self.call_summary.to_dict(),
            Enum.CASE: self.case.to_dict(),
            Enum.ANALYZE_STATUS: self.analyze_status.to_dict(),
            Enum.CUSTOMER_PHONE_NUMBERS: self.customer_phone_number  
        }