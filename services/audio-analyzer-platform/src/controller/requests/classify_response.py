from typing import Dict
from src.domain.task import Task

class ClassifyResponse:
    def __init__(
            self,
            tasks: Dict[str, Task]
        ):
        """
        Args:
            tasks (Dict[str, Task]): A dictionary of tasks classified from the conversation.
        """
        self.tasks: Dict[str, Task] = tasks

    def to_dict(self) -> dict:
        return {
            key: task.to_dict() 
            for key, task in self.tasks.items()
        }