from src.domain.call_summary import CallSummary

class ClassifyRequest:
    def __init__(
            self, 
            call_summary: CallSummary,
            source: str
        ):
        """
        Args:
            call_summary (CallSummary): Summary of the call.
            source (str): Source of the audio (e.g., "customer", "agent").
        """
        self.call_summary: CallSummary = call_summary
        self.source: str = source