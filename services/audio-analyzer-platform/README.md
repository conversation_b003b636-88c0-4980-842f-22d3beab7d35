# Invoice Parsing Platform

## Overview

This platform is designed to help accountants quickly and accurately extract key details from invoices using OCR (Optical Character Recognition). It currently supports invoice parsing, with more automation features coming soon.

### 1. Prerequisites

*   Python 3.7+
*   `pip` package manager

### 2. Install Dependencies

Navigate to the root directory of the project (where `app.py`, `core.py`, etc., are located) and run:

```bash
pip install -r requirements.txt
```

### 3. Configure Environment Variables

Copy the `example.env` file to `.env` and update the values as needed.

### 4. Run the Application

To run the application, use the following command:

```bash
uvicorn app.main:app --reload
```

This will start the application and make it available at `http://localhost:8000`.

## Usage

The application provides a simple RESTful API. The API endpoints are:

*   `/invoice/parse`: Upload an invoice file to extract the relevant information.

The API uses the following request format:

```json
{
  "file": "invoice.pdf",
  "user_id": "user1"
}
```

- The `session_id` field is optional and can be used to maintain context for the chatbot.
- The `user_id` field is optional and can be used to identify the user to apply role-based security.

## Testing URL
To curl to the `/invoice/parse` endpoint with a JSON body, use this command:
```curl -X POST http://localhost:8000/invoice/parse \
   -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/invoice.pdf" \
  -F "user_id=user1"
```

## Deployment

To deploy the application, you can use Docker Compose.

```bash
docker-compose up -d
```

This will start the application and make it available at `http://localhost:8000`.