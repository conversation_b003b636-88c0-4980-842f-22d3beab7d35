import re
import pandas as pd

agents = [
    "Agent",
    "agent",
    "nhân viên",
    "직원"
]
customers = [
    "Customer",
    "customer",
    'kh<PERSON>ch',
    "khách hàng",
    "고객"
]

languages = {
    "vi": "Tiếng Việt",
    "en": "<PERSON><PERSON>ế<PERSON> An<PERSON>",
    "ko": "Tiế<PERSON> Hà<PERSON>"
}

def is_agent(speaker):
    for agent in agents:
        if agent in speaker:
            return True
    return False

def is_customer(speaker):
    for customer in customers:
        if customer in speaker:
            return True
    return False

# Load CSV
# df = pd.read_csv("output.csv")
df = pd.read_csv("output.csv")

def format_transcript(transcript):
    lines = transcript.splitlines()
    formatted = '<div style="display: flex; flex-direction: column; gap: 4px;">'
    for line in lines:
        speaker = line.strip().lower()
        if is_agent(speaker):
            content = line.partition(":")[2].strip()
            formatted += f'<div style="background:#e6f0ff;padding:6px;border-radius:8px;align-self:flex-start;"><strong>Agent:</strong> {content}</div>'
        elif is_customer(speaker):
            content = line.partition(":")[2].strip()
            formatted += f'<div style="background:#fef3c7;padding:6px;border-radius:8px;align-self:flex-end;"><strong>Customer:</strong> {content}</div>'
        else:
            formatted += f'<div style="padding:6px;">{line}</div>'
    formatted += '</div>'
    return formatted

def format_with_line_breaks(text):
    try:
        # Split the text by '-' and join with <br> for line breaks
        parts = re.split(r'\s*(?=\d+\.\s)', text)

        # Xoá phần tử rỗng nếu có
        parts = [p.strip() for p in parts if p.strip()]
        return '<br> '.join(parts)
    except:
        return "Thông tin chưa được xác định"

def format_next_task(text):
    try:
        # Split the text by '-' and join with <br> for line breaks

        # Xoá phần tử rỗng nếu có
        parts = [p.strip() for p in text.split("-")]
        return '<br> * '.join(parts)
    except:
        return "Thông tin chưa được xác định"

# HTML header with styling
html_header = """
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Call Data Table</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    h2 {
      display: inline-block;
      width: auto;
      padding: 8px 16px;
      margin-bottom: 20px;
      height: 10px;
      white-space: nowrap;
      font-size: 20px;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      table-layout: fixed;
    }
    th, td {
      border: 1px solid #ccc;
      padding: 10px;
      height: 320px; /* Doubled row height */
      vertical-align: top;
    }
    th {
      background-color: #f2f2f2;
      text-align: left;
      font-size: 16px;
    }

    td > div {
      max-height: 280px; /* Adjust to match doubled row height */
      overflow-y: auto;
      font-size: 14px;
    }

    audio {
      width: 100%;
    }
    thead th:nth-child(3), tbody td:nth-child(3) {
      width: 30%;
    }
    thead th, tbody td {
      word-wrap: break-word;
    }
  </style>
</head>
<body>
  <h2>Call Data Table</h2>
  <table>
    <thead>
      <tr>
        <th>Audio</th>
        <th>File ghi âm</th>
        <th>Đoạn hội thoại</th>
        <th>Ngôn ngữ</th>
        <th>Thông tin khách hàng</th>
        <th>Tóm tắt cuộc gọi</th>
        <th>Tác vụ tiếp theo</th>
        <th>Đề xuất</th>
      </tr>
    </thead>
    <tbody>
"""


# Generate HTML rows
rows = ""
for _, row in df.iterrows():
    audio_path = row['audio_file']
    formatted_transcript = format_transcript(row['transcript'])

    # Apply line breaks for the relevant columns
    formatted_customer_info = format_next_task(row['customer_info'])
    formatted_summary_content = format_with_line_breaks(row['summary_content'])
    formatted_recommended_actions = format_with_line_breaks(row['recommended_actions'])
    formatted_next_task = format_next_task(row['next_task'])
    formatted_language = languages[row['language']]

    rows += f"""
      <tr>
        <td>
          <audio controls>
            <source src="{audio_path}" type="audio/mpeg">
            Your browser does not support the audio element.
          </audio>
        </td>
        <td><div>{audio_path}</div></td>
        <td><div>{formatted_transcript}</div></td>
        <td><div>{formatted_language}</div></td>
        <td><div>{formatted_customer_info}</div></td>
        <td><div>{formatted_summary_content}</div></td>
        <td><div>{formatted_next_task}</div></td>
        <td><div>{formatted_recommended_actions}</div></td>
      </tr>
    """

# Closing HTML
html_footer = """
    </tbody>
  </table>
</body>
</html>
"""

# Combine all parts and save
with open("call_data_table.html", "w", encoding="utf-8") as f:
    f.write(html_header + rows + html_footer)

print("✅ HTML file 'call_data_table.html' has been created.")
