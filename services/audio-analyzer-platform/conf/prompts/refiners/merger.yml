
prompt: |
  Bạn sẽ được cung cấp hai bản transcript của một cuộc hội thoại:

  (1) Bản 1 - từ mô hình Whisper:
  G<PERSON><PERSON> các câu thoại đã phân vai người nó<PERSON> (Customer hoặc Agent), tuy nhiên chất lượng nhận diện giọng nói và phân vai có thể chưa chính xác.

  (2) Bản 2 - từ mô hình GPT-4o-Transcribe:
  <PERSON><PERSON> phiên bản transcript chất lư<PERSON><PERSON> cao hơ<PERSON>, đầy đủ và rõ ràng hơn, tuy nhiên chưa phân vai người nói.

  Nhiệm vụ của bạn:
    1. Dựa vào nội dung rõ ràng hơn trong bản GPT-4o-Transcribe (Bản 2) và cấu trúc phân vai từ bản Whisper (Bản 1), hãy tạo lại một phiên bản hội thoại hoàn chỉnh.
    2. <PERSON><PERSON><PERSON> một cụm từ có thể nghe giống từ không phù hợp (do lỗi nhận dạng giọng nói), hãy giả định nó mang nghĩa trong sáng hoặc an toàn theo ngữ cảnh, đặc biệt trong các cuộc gọi dịch vụ khách hàng.
    3. Tránh hiểu sai các cụm từ tiếng Việt là nội dung nhạy cảm trừ khi nghĩa thực sự rõ ràng và trực tiếp.
  
  Cần đảm bảo:
    1. Nội dung theo bản GPT-4o là chính.
    2. Vai trò người nói được gán chính xác (Agent hoặc Customer) dựa theo logic cuộc trò chuyện và gợi ý từ bản Whisper.
    3. Nếu phân vai ở bản Whisper chưa đúng, bạn có thể chỉnh lại cho hợp lý để khớp nội dung cuộc hội thoại, lưu ý rằng:
        - Agent là nhân viên chăm sóc khách hàng của Vinpearl.
        - Customer là khách hàng đang gọi đến để hỏi thông tin hoặc yêu cầu hỗ trợ.
    4. Kết quả mong muốn là một đoạn hội thoại rõ ràng, chính xác, có phân vai từng lượt thoại.

  Bản Whisper: {{whisper_trans}}
  Bản GPT-4o-Transcribe: {{transcription}}

  Yêu cầu về định dạng đầu ra:
    - Viết lại hội thoại theo cấu trúc: SPEAKER: <Lời thoại>
    - Không được xuống dòng trong phần nội dung lời thoại
    - Chỉ chỉnh sửa những câu thực sự cần thiết (nếu câu đã đúng, hãy giữ nguyên)
    - Không thay đổi ngôn ngữ gốc
    - Chỉ trả về đoạn hội thoại đã được chỉnh sửa, không kèm bất kỳ lời giải thích hoặc nhận xét nào khác

