
return_format: |
  ```json
  {
    "Thông tin khách hàng": {
      "Tên khách hàng": "<Tên khách hàng>",
      "Email": "<Email khách hàng>",
      "S<PERSON> điện thoại": "<Số điện thoại khách hàng>",
      "Số lượng khách": "<Số lượng khách>",
      "Thông tin từng khách": [
        {
          "<Tên khách 1>" - "<Tuổi khách 1>" - "<Giới tính khách 1>" - "<Thông tin khác về khách 1>",
          ...
        }
      ],
    },
    "Y<PERSON>u cầu của khách hàng": {
      "Yêu cầu chính": "<Yêu cầu chính của khách trong cuộc hội thoại>",
      "<PERSON><PERSON>u cầu cuối cùng": "<Yêu cầu cuối cùng của khách trong cuộc hội thoại>"
    },
    "Phản hồi của nhân viên chăm sóc khách hàng": {
      "Phản hồi": [
        "<Phản hồi 1>",
        "<Phản hồi 2>",
        ...
      ],
      "Hứa hẹn": [
        "<Hứa hẹn 1>",
        "<Hứa hẹn 2>",
        ...
      ]
    },
    "Cuộc gọi dang dở": <true | false>,
    "Tóm tắt": <tóm tắt ý chính của cuộc hội thoại>
  }
  ```

prompt: |
  Đây là đoạn hội thoại giữa nhân viên chăm sóc khách hàng Vinpearl và Khách hàng.
  Hãy trích xuất thông tin sau từ đoạn hội thoại và trả kết quả dưới dạng JSON:

    1. Thông tin khách hàng:
      * Tên khách hàng
      * Email
      * Số điện thoại
      * Số lượng khách và thông tin của từng khách
      * Thông tin khác 

    2. Yêu cầu của khách hàng:
      * Yêu cầu chính
      * Yêu cầu cuối cùng

    3. Phản hồi của nhân viên chăm sóc khách hàng:
      * Phản hồi 1
      * Phản hồi 2
      * ...

    4. Cuộc gọi có đang dang dở không (true/false):          
      + Nếu cuộc gọi bị kết thúc đột ngột, giữa chừng, khách hoặc nhân viên chưa phản hồi → Chi tiết cho tác vụ tiếp theo nên là "Gọi lại cho khách để tiếp tục hỗ trợ", phân loại phù hợp theo thông tin đã có.

    5. Tóm tắt:
      * Viết một đoạn văn ngắn gọn, rõ ràng, lịch sự, bắt buộc phải bao gồm những thông tin nếu được đề cập đến:
        + Giá gói dịch vụ mong muốn
        + Thông tin giảm giá (nếu có)
        + Tên khách sạn/biệt thự/nơi ở muốn lưu trú
        + Tên nhà hàng muốn dùng bữa
        + Thời gian lưu trú.
        + Lưu ý những cam kết mà nhân viên CSKH hứa sẽ gửi lại cho khách.

  ---
  ### Định dạng trả về (JSON):
    {{return_format}}
  ---
  Cuộc hội thoại:
  {{conversation}}

parsing_pattern: "```json\n(.*?)\n```"
