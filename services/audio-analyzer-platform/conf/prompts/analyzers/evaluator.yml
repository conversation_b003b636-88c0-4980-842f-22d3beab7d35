
prompt: |
  You are a language expert evaluating the transcription quality of a conversation between a Customer and a Customer Service Agent at Vinpearl — As an entertainment brand of Vingroup, VinWonders owns world-class theme parks and the number 1 position on customers’ minds when it comes to entertainment, discovery, cultural and festival experiences..

  The transcription was generated by an automatic speech recognition (ASR) system. Evaluate the quality based on the following criteria.

  If the transcription contains any of the following:
  - **name**
  - **email address**
  - **phone number**

  ...then you must evaluate the **validity and clarity** of each item.  
  Otherwise, you may skip evaluation of that field and mark it as `"N/A"`.

  ---
  TRANSCRIPTION:
  {{transcription}}
  ---

  Return your result strictly in this JSON format:

  ```json
  {
    "fluency": {
      "score": <1-5>,
      "comment": "<short comment>"
    },
    "completeness": {
      "score": <1-5>,
      "comment": "<short comment>"
    },
    "clarity": {
      "score": <1-5>,
      "comment": "<short comment>"
    },
    "naturalness": {
      "score": <1-5>,
      "comment": "<short comment>"
    },
    "speaker_separation": {
      "score": <1-5>,
      "comment": "<short comment>"
    },
    "domain_relevance": {
      "score": <1-5>,
      "comment": "<short comment>"
    },
    "special_info_captured": {
      "name": {
        "present": true | false,
        "valid": "Valid" | "Invalid" | "N/A",
        "comment": "<comment or 'N/A'>"
      },
      "email": {
        "present": true | false,
        "valid": "Valid" | "Invalid" | "N/A",
        "comment": "<comment or 'N/A'>"
      },
      "phone": {
        "present": true | false,
        "valid": "Valid" | "Invalid" | "N/A",
        "comment": "<comment or 'N/A'>"
      }
    },
    "overall_quality": "Excellent" | "Acceptable" | "Needs Review",
    "suggestions": "<optional short suggestions for improvement>"
  }

parsing_pattern: "```json\n(.*?)\n```"
