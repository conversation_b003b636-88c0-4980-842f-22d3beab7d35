
prompt: |
  Bạn sẽ nhận được tóm tắt của các cuộc gọi liên tiếp giữa cùng một khách hàng và nhân viên. Cu<PERSON><PERSON> gọi thứ hai là phần tiếp theo trực tiếp của cuộc gọi thứ nhất, cùng chủ đề và mang tính cập nhật hoặc tiếp tục xử lý vấn đề trước đó.

  Nhiệm vụ của bạn:
    1. K<PERSON><PERSON> hợ<PERSON> nội dung từ các cuộc gọi thành một bản tóm tắt thống nhất, logic, đầy đủ và không trùng lặp.
    2. Làm nổi bật diễn tiến của vấn đề hoặc câu chuyện giữa các cuộc gọi.
    3. Nếu cuộc gọi cuối cùng có thông tin mới, chỉnh sửa hoặc phản hồi lại nội dung trước đó, hãy phản ánh sự thay đổi một cách tự nhiên và chính xác.
    4. Thông tin khách hàng có thể sẽ được cập nhật qua các cuộc gọi, vì vậy hãy đảm bảo rằng thông tin cuối cùng là chính xác và đầy đủ.
  
  Yêu cầu định dạng:
    1. Viết thành một đoạn văn ngắn gọn (khoảng 4–8 câu).
    2. Diễn đạt lại bằng ngôn ngữ rõ ràng, dễ hiểu, trung lập và nhất quán.
    3. Không cần tách riêng nội dung từng cuộc gọi, chỉ cần kết hợp và trình bày như một cuộc gọi liền mạch.
    4. Trả về với định dạng JSON như sau:
      ```json
      {
        "Tóm tắt": 
          - "<Tóm tắt nội dung cuộc gọi 1>"
          - "<Tóm tắt nội dung cuộc gọi 2>"
          - ...
        "Thông tin khách hàng": {
          "Tên khách hàng": "<Tên khách hàng>",
          "Email": "<Email của khách hàng>",
          "Số điện thoại": "<Số điện thoại của khách hàng>",
          "Số lượng khách": "<Số lượng khách>",
          "Thông tin từng khách": [
            {
              "<Tên khách 1>" - "<Tuổi khách 1>" - "<Giới tính khách 1>" - "<Thông tin khác về khách 1>",
              ...
            }
          ]
        }
      ```
  Tóm tắt: {{summaries}}
  Thông tin khách hàng: {{customer_info}}

parsing_pattern: "```json\n(.*?)\n```"
