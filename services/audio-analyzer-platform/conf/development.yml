server:
  http:
    nthreads: 8
    port: 31661
  n_similarity_requests: 4

llm_config:
  max_tokens: 16384
  temperature: 0.0
  model_name: "dev-gpt-4.1"
  sampling: false

describe_llm_config:
  max_tokens: 32768
  temperature: 0.0
  model_name: "dev-gpt-4o-transcribe"
  sampling: false

salesforce_client:
  endpoint_url: "https://cloud.news.vinpearl.com/Create_Case_API_Test"

audio_preprocessing:
  model_name: htdemucs
  demucs_model_root: data/models/demucs

# Diarization configuration
audio_diarization:
  model_path: "data/models/speaker-diarization-3.1/snapshots/84fd25912480287da0247647c3d2b4853cb3ee5d/config.yaml"
  hf_token: "*************************************"
  is_using_gpu: true
  num_speakers: 2

audio_transcriber:
  min_turn_duration_in_ms: 200
  whisper:
    # model_name: "tiny"
    model_name: "large-v3"
    model_path: "data/models/whisper"
    is_using_gpu: true
    ignoring_sentences:
      - "H<PERSON><PERSON> subscribe"
      - "Cảm ơn các bạn"
      - "Các bạn hãy đăng"
      - "Cảm ơn các bạn đã"
      - "Chào mừng các bạn đã"
      - "Xin chào và hẹn gặp lại"
      - "Các bạn có thể nhận được những bài hát"
      - "Hẹn gặp lại các bạn"
      - "Hãy đăng ký kênh"
      - "Hãy đăng kí kênh"
      - "Nhớ đăng ký kênh"
      - "Nhớ đăng kí kênh"
      - "Hẹn gặp lại các bạn"
      - "like, share và đăng ký"

  chunk_former:
    model_path: "data/models/chunkformer-large-vie"
    chunk_size: 64
    left_context_size: 128
    right_context_size: 128
    total_batch_duration: 14400
    is_using_gpu: true

transcription_refiner:
  splitter: "\n"
  max_generated_tokens: 32768
  possible_missing_words:
    Tên thương hiệu: VinWonders, VinPearl, MyVinPearl, VinPearl Golf, VinClub, VinFast, VinHomes, Deep Sea, VF, WonderWorld, VinBus, GSM, Melia, Marriott
    Tên địa điểm: Nha Trang, Phú Quốc, Đà Nẵng, Hà Nội, Hội An, Nam Hội An, Hạ Long, Hòn Tằm, Hòn Tre.
    Tên hãng hàng không: VietJet, Vietnam Airlines.
    Địa chỉ Email: <EMAIL>, @, 
    Từ ngữ đặc thù: Telesale, Vpoint, Chăm sóc khách hàng, Combo, Voucher, Call Center, vinpearl.com, Luxury, Resort, Spa, Beachfont, Golf, VinHolidays, Fiesta, Bất Động Sản Nghĩ Dưỡng, VAT, VEC, Chủ Sở Hữu, book.
    Tên các gói combo: Xuân Vi Vu
  system_prompt: conf/prompts/refiners/system.yml
  refiner_prompt: conf/prompts/refiners/refiner.yml
  merger_prompt: conf/prompts/refiners/merger.yml
  transcriber_prompt: conf/prompts/refiners/transcriber.yml

transcription_analyzer_prompts:
  analyzer_prompt: conf/prompts/analyzers/analyzer.yml
  evaluator_prompt: conf/prompts/analyzers/evaluator.yml 
  summarizer_prompt: conf/prompts/analyzers/summarizer.yml

task_classifier:
  classifier_prompt: conf/prompts/classifiers/classifiers.yml
