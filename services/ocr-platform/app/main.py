import uvicorn

from fastapi import FastAPI, Request, Depends,  UploadFile, File
from sqlalchemy.orm import Session

from .core import get_invoice_data
from .db.session import SessionLocal


app = FastAPI()


# Dependency to get the database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@app.get("/")
async def get_page(request: Request):
    return {"status": "ok"}


@app.post("/invoice/parse")
async def parse_invoice_endpoint(
    file: UploadFile = File(...), db: Session = Depends(get_db)
):
    invoice_data = await get_invoice_data(file)
    return invoice_data


if __name__ == "__main__":
    # This is for running directly, e.g., python -m app.main
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
