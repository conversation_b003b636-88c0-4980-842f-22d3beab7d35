from sqlalchemy import (
    create_engine,
    event,
)
from pytz import timezone
from datetime import datetime
import uuid

from sqlalchemy import (
    Column,
    DateTime,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import Session

from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker


database_url = "sqlite:///data/database.sqlite"
engine = create_engine(database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    cursor = dbapi_connection.cursor()
    cursor.execute("PRAGMA journal_mode = WAL;")
    cursor.execute("PRAGMA wal_autocheckpoint = 50;")
    cursor.execute("PRAGMA busy_timeout = 5000;")
    cursor.execute("PRAGMA synchronous = NORMAL;")
    cursor.execute("PRAGMA temp_store = MEMORY;")
    cursor.execute("PRAGMA foreign_keys=ON;")
    cursor.close()


def lazy_utc_now():
    return datetime.now(tz=timezone.utc)


class User(Base):
    __tablename__ = "user"

    id = Column(
        Integer, primary_key=True, autoincrement=False
    )  # User id as primary key
    chat_id = Column(Integer, nullable=False)
    username = Column(String, default="")
    first_name = Column(String, default="")
    last_name = Column(String, default="")
    last_interaction = Column(DateTime, default=lazy_utc_now)
    first_seen = Column(DateTime, default=lazy_utc_now)


# Create tables
Base.metadata.create_all(bind=engine)
