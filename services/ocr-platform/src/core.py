import base64
from datetime import datetime

from pydantic import BaseModel, Field, field_validator
from typing import Optional
import pymupdf
from openai import AsyncOpenAI
from .consts import settings

openai_client = AsyncOpenAI(
    api_key=settings.openai_api_key,
    base_url=settings.openai_endpoint,
)


def convert_pdf_to_img(doc):
    max_page = 5
    base64_images = []
    for idx, page in enumerate(doc):
        if idx >= max_page:
            break
        pix = page.get_pixmap(dpi=150)
        img_byte_arr = pix.tobytes(output="png")
        base64_image = base64.b64encode(img_byte_arr).decode("utf-8")
        base64_images.append(f"data:image/png;base64,{base64_image}")
    return base64_images


class InvoiceData(BaseModel):
    invoice_date: Optional[str] = Field(..., description="Invoice date.")
    reference: Optional[str] = Field(..., description="Invoice reference number.")
    amount: Optional[float] = Field(..., description="Total amount of the invoice.")
    currency: Optional[str] = Field(..., description="Currency of the invoice.")
    tax_amount: Optional[float] = Field(..., description="Tax amount of the invoice.")
    tax_type: Optional[str] = Field(..., description="Type of tax applied.")
    posting_date: Optional[str] = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d"),
        description="Posting date of the invoice.",
    )
    bus_place_section: Optional[str] = Field(
        ..., description="Business place section of the invoice."
    )
    text: Optional[str] = Field(..., description="Text content of the invoice.")

    @field_validator("invoice_date")
    def convert_invoice_date(cls, invoice_date):
        if not invoice_date:
            return None
        if invoice_date.lower() in ["n/a", "not provided", "not found"]:
            return None

        return (
            invoice_date
            if invoice_date.lower() not in ["present", "current", "now"]
            else datetime.now().strftime("%Y-%m")
        )

    @field_validator("posting_date")
    def convert_posting_date(cls, posting_date):
        return datetime.now().strftime("%Y-%m-%d")

    @field_validator("reference")
    def convert_reference(cls, reference):
        return (
            reference
            if reference.lower() not in ["n/a", "not found", "not provided"]
            else None
        )


default_invoice_system_prompt = """You are given an image. Your task is to determine whether the image is an official VAT invoice. Only proceed to extract data if the image meets the criteria below. If the criteria are not met, return an empty YAML object structured as:

invoice: []

**Strict Criteria for Invoice Identification:**
- The phrase **"HÓA ĐƠN GIÁ TRỊ GIA TĂNG"** must be clearly visible and legible in the image.
- This phrase must appear prominently as a title or header — not in footnotes, body text, or decorative elements.
- If this phrase is missing, partially visible, blurred, cropped, or used out of context, treat the image as **not** a VAT invoice.

**Expected Output Format (only if image is a valid invoice):**

invoice:
    invoice_date: (date or null) — Format: DD/MM/YYYY. The official issuance date of the invoice.
    reference: (string or null) — Format: "Mã ký hiệu#Số hóa đơn". A unique invoice identifier composed of a reference code and invoice number.
    posting_date: (date or null) — Format: DD/MM/YYYY. The accounting or system entry date, if shown.
    amount: (number or null) — Format: Decimal number (e.g., 123456.78). **Total amount after VAT is added** (gross amount).
    tax_amount: (number or null) — Format: Decimal number (e.g., 11234.56). Total value-added tax (VAT) shown on the invoice.
    bus_place_section: (string or null) — Business place or section name related to the transaction, if available.
    text: (string or null) — Diễn giải nghiệp vụ theo cấu trúc sau: “[Segment]-TT VD hàng hóa NCC [mã NCC] PO[mã PO]”
    po_reference: (string or null) — PO reference (INV PO Code), if the invoice includes a purchase order reference.

Instructions:
1. All field names must match exactly as shown.
2. If any field is missing, unclear, or not visible in the image, return its value as null.
3. If the image is not a valid VAT invoice according to the strict criteria above, return:
invoice: []
"""


async def parse_invoice(
    invoice_file: str
) -> Optional[InvoiceData]:
    doc = pymupdf.open(invoice_file)
    max_page = 5
    invoice_text = ""
    for idx, page in enumerate(doc):
        if idx >= max_page:
            break
        text = page.get_text()
        invoice_text += "\n" + text

    if len(invoice_text.split()) < 50:
        invoice_images_base64 = convert_pdf_to_img(doc)
        if not invoice_images_base64:
            return None
        user_msg = [
            {
                "type": "text",
                "text": "Parse the invoice information from the following image(s):",
            }
        ]
        for img_b64 in invoice_images_base64:
            user_msg.append({"type": "image_url", "image_url": {"url": img_b64}})
    else:
        user_msg = f"Parse the following invoice:\n\n{invoice_text}"

    completion = await openai_client.beta.chat.completions.parse(
        model=settings.openai_model_name,
        messages=[
            {"role": "system", "content": default_invoice_system_prompt},
            {"role": "user", "content": user_msg},
        ],
        timeout=180,
        response_format=InvoiceData,
    )
    result = completion.choices[0].message.parsed
    return result
