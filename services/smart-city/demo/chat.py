import base64
import numpy as np
from PIL import Image
from io import BytesIO
from openai import OpenAI
from qwen_vl_utils import process_vision_info


# Set OpenAI's API key and API base to use vLLM's API server.
openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"

client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)


# video_messages = [
#     {"role": "system", "content": "You are a helpful assistant."},
#     {"role": "user", "content": [
#         {"type": "text", "text": "Video này có xảy ra hành vi vi phạm gì không?"},
#         {
#             "type": "video",
#             "video": "/mnt/disk1/hai_workspace/glass-break/004.mp4",
#             "total_pixels": 20480 * 28 * 28, "min_pixels": 16 * 28 * 2,
#             'fps': 3.0  # The default value is 2.0, but for demonstration purposes, we set it to 3.0.
#         }]
#     },
# ]


def prepare_message_for_vllm(content_messages):
    """
    The frame extraction logic for videos in `vLLM` differs from that of `qwen_vl_utils`.
    Here, we utilize `qwen_vl_utils` to extract video frames, with the `media_typ`e of the video explicitly set to `video/jpeg`.
    By doing so, vLLM will no longer attempt to extract frames from the input base64-encoded images.
    """
    vllm_messages, fps_list = [], []
    for message in content_messages:
        message_content_list = message["content"]
        if not isinstance(message_content_list, list):
            vllm_messages.append(message)
            continue

        new_content_list = []
        for part_message in message_content_list:
            if "video" in part_message:
                video_message = [{"content": [part_message]}]
                image_inputs, video_inputs, video_kwargs = process_vision_info(
                    video_message, return_video_kwargs=True
                )
                assert video_inputs is not None, "video_inputs should not be None"
                video_input = (
                    (video_inputs.pop()).permute(0, 2, 3, 1).numpy().astype(np.uint8)
                )
                fps_list.extend(video_kwargs.get("fps", []))

                # encode image with base64
                base64_frames = []
                for frame in video_input:
                    img = Image.fromarray(frame)
                    output_buffer = BytesIO()
                    img.save(output_buffer, format="jpeg")
                    byte_data = output_buffer.getvalue()
                    base64_str = base64.b64encode(byte_data).decode("utf-8")
                    base64_frames.append(base64_str)

                part_message = {
                    "type": "video_url",
                    "video_url": {
                        "url": f"data:video/jpeg;base64,{','.join(base64_frames)}"
                    },
                }
            new_content_list.append(part_message)
        message["content"] = new_content_list
        vllm_messages.append(message)
    return vllm_messages, {"fps": fps_list}


def chat_video(video_messages):
    video_messages, video_kwargs = prepare_message_for_vllm(video_messages)
    chat_response = client.chat.completions.create(
        model="Qwen/Qwen2.5-VL-32B-Instruct",
        messages=video_messages,
        temperature=0.1,
        extra_body={"mm_processor_kwargs": video_kwargs},
        stream=True,
    )
    return chat_response.choices[0].message.content.strip()


def find_image_in_video(image_path: str, video_path: str):
    prompt = "Xác định người trong hình ảnh thứ hai có trong video hay không? Chỉ trả về kết quả 1 (Có) và 0 (Không). Kết quả của bạn: "
    with open(image_path, "rb") as f:
        encoded_image = base64.b64encode(f.read())
    encoded_image_text = encoded_image.decode("utf-8")
    base64_qwen = f"data:image;base64,{encoded_image_text}"
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt,
                },
                {
                    "type": "video",
                    "video": video_path,
                    "total_pixels": 20480 * 28 * 28,
                    "min_pixels": 16 * 28 * 2,
                    "fps": 3.0,  # The default value is 2.0, but for demonstration purposes, we set it to 3.0.
                },
                {
                    "type": "image_url",
                    "image_url": {"url": base64_qwen},
                },
            ],
        },
    ]
    video_messages, video_kwargs = prepare_message_for_vllm(messages)
    chat_response = client.chat.completions.create(
        model="Qwen/Qwen2.5-VL-32B-Instruct",
        messages=video_messages,
        temperature=0.1,
        extra_body={"mm_processor_kwargs": video_kwargs},
    )
    if "1" in chat_response.choices[0].message.content.strip():
        return True
    else:
        return False
