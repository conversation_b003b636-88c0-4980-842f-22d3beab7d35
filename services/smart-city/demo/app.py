#!/usr/bin/env python3
import base64

import gradio as gr
import os
from main import load_moondream, process_video
import shutil
import torch
import glob

# from visualization import visualize_detections
from persistence import load_detection_data
import matplotlib.pyplot as plt
import io
from PIL import Image
import pandas as pd
from openai import OpenAI

from chat import prepare_message_for_vllm, find_image_in_video
# from video_visualization import create_video_visualization

# Get absolute path to workspace root
WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
INPUTS_DIR = os.path.join(WORKSPACE_ROOT, "inputs")

openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"

chat_client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

# Check CUDA availability
print(f"Is CUDA available: {torch.cuda.is_available()}")
# We want to get True
print(f"CUDA device: {torch.cuda.get_device_name(torch.cuda.current_device())}")
# GPU Name

# Initialize Moondream model globally for reuse (will be loaded on first use)
model, tokenizer = load_moondream()
# model, tokenizer = None, None
# video_path = None


def process_video_file(
    input_video_path,
    target_object,
    box_style,
    grid_rows,
    grid_cols,
):
    """Process a video file through the Gradio interface."""
    try:
        # # Load models if not already loaded
        # global model, tokenizer
        # if model is None or tokenizer is None:
        #     model, tokenizer = load_moondream()

        # Ensure input/output directories exist using absolute paths
        inputs_dir = os.path.join(WORKSPACE_ROOT, "inputs")
        outputs_dir = os.path.join(WORKSPACE_ROOT, "outputs")
        os.makedirs(inputs_dir, exist_ok=True)
        os.makedirs(outputs_dir, exist_ok=True)

        try:
            # Process the video
            video_filename = f"input_{os.path.basename(input_video_path)}"
            output_path = process_video(
                input_video_path,
                target_object,
                model=model,
                tokenizer=tokenizer,
                grid_rows=grid_rows,
                grid_cols=grid_cols,
                box_style=box_style,
            )

            # Get the corresponding JSON path
            base_name = os.path.splitext(os.path.basename(video_filename))[0]
            json_path = os.path.join(
                outputs_dir, f"{box_style}_{target_object}_{base_name}_detections.json"
            )

            # Verify output exists and is readable
            if not output_path or not os.path.exists(output_path):
                print(f"Warning: Output path {output_path} does not exist")
                # Try to find the output based on expected naming convention
                expected_output = os.path.join(
                    outputs_dir, f"{box_style}_{target_object}_{video_filename}"
                )
                if os.path.exists(expected_output):
                    output_path = expected_output
                else:
                    # Try searching in outputs directory for any matching file
                    matching_files = [
                        f
                        for f in os.listdir(outputs_dir)
                        if f.startswith(f"{box_style}_{target_object}_")
                    ]
                    if matching_files:
                        output_path = os.path.join(outputs_dir, matching_files[0])
                    else:
                        raise gr.Error("Failed to locate output video")

            # Convert output path to absolute path if it isn't already
            if not os.path.isabs(output_path):
                output_path = os.path.join(WORKSPACE_ROOT, output_path)

            print(f"Returning output path: {output_path}")
            return output_path, json_path

        finally:
            pass

    except Exception as e:
        print(f"Error in process_video_file: {str(e)}")
        raise gr.Error(f"Error processing video: {str(e)}")


def create_visualization_plots(json_path):
    """Create visualization plots and return them as images."""
    try:
        # Load the data
        data = load_detection_data(json_path)
        if not data:
            return None, None, None, None, None, None, None, None, "No data found"

        # Convert to DataFrame
        rows = []
        for frame_data in data["frame_detections"]:
            frame = frame_data["frame"]
            timestamp = frame_data["timestamp"]
            for obj in frame_data["objects"]:
                rows.append(
                    {
                        "frame": frame,
                        "timestamp": timestamp,
                        "keyword": obj["keyword"],
                        "x1": obj["bbox"][0],
                        "y1": obj["bbox"][1],
                        "x2": obj["bbox"][2],
                        "y2": obj["bbox"][3],
                        "area": (obj["bbox"][2] - obj["bbox"][0])
                        * (obj["bbox"][3] - obj["bbox"][1]),
                        "center_x": (obj["bbox"][0] + obj["bbox"][2]) / 2,
                        "center_y": (obj["bbox"][1] + obj["bbox"][3]) / 2,
                    }
                )

        if not rows:
            return (
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                "No detections found in the data",
            )

        df = pd.DataFrame(rows)
        plots = []

        # Create each plot and convert to image
        for plot_num in range(8):  # Increased to 8 plots
            plt.figure(figsize=(8, 6))

            if plot_num == 0:
                # Plot 1: Number of detections per frame (Original)
                detections_per_frame = df.groupby("frame").size()
                plt.plot(detections_per_frame.index, detections_per_frame.values)
                plt.xlabel("Frame")
                plt.ylabel("Number of Detections")
                plt.title("Detections Per Frame")

            elif plot_num == 1:
                # Plot 2: Distribution of detection areas (Original)
                df["area"].hist(bins=30)
                plt.xlabel("Detection Area (normalized)")
                plt.ylabel("Count")
                plt.title("Distribution of Detection Areas")

            elif plot_num == 2:
                # Plot 3: Average detection area over time (Original)
                avg_area = df.groupby("frame")["area"].mean()
                plt.plot(avg_area.index, avg_area.values)
                plt.xlabel("Frame")
                plt.ylabel("Average Detection Area")
                plt.title("Average Detection Area Over Time")

            elif plot_num == 3:
                # Plot 4: Heatmap of detection centers (Original)
                plt.hist2d(df["center_x"], df["center_y"], bins=30)
                plt.colorbar()
                plt.xlabel("X Position")
                plt.ylabel("Y Position")
                plt.title("Detection Center Heatmap")

            elif plot_num == 4:
                # Plot 5: Time-based Detection Density
                # Shows when in the video most detections occur
                df["time_bucket"] = pd.qcut(df["timestamp"], q=20, labels=False)
                time_density = df.groupby("time_bucket").size()
                plt.bar(time_density.index, time_density.values)
                plt.xlabel("Video Timeline (20 segments)")
                plt.ylabel("Number of Detections")
                plt.title("Detection Density Over Video Duration")

            elif plot_num == 5:
                # Plot 6: Screen Region Analysis
                # Divide screen into 3x3 grid and show detection counts
                try:
                    df["grid_x"] = pd.qcut(
                        df["center_x"],
                        q=3,
                        labels=["Left", "Center", "Right"],
                        duplicates="drop",
                    )
                    df["grid_y"] = pd.qcut(
                        df["center_y"],
                        q=3,
                        labels=["Top", "Middle", "Bottom"],
                        duplicates="drop",
                    )
                    region_counts = (
                        df.groupby(["grid_y", "grid_x"]).size().unstack(fill_value=0)
                    )
                    plt.imshow(region_counts, cmap="YlOrRd")
                    plt.colorbar(label="Detection Count")
                    for i in range(3):
                        for j in range(3):
                            plt.text(
                                j, i, region_counts.iloc[i, j], ha="center", va="center"
                            )
                    plt.xticks(range(3), ["Left", "Center", "Right"])
                    plt.yticks(range(3), ["Top", "Middle", "Bottom"])
                    plt.title("Screen Region Analysis")
                except Exception as e:
                    plt.text(
                        0.5,
                        0.5,
                        "Insufficient variation in detection positions",
                        ha="center",
                        va="center",
                    )
                    plt.title("Screen Region Analysis (Not Available)")

            elif plot_num == 6:
                # Plot 7: Detection Size Categories
                # Categorize detections by size for content moderation
                try:
                    size_labels = [
                        "Small (likely far/background)",
                        "Medium-small",
                        "Medium-large",
                        "Large (likely foreground/close)",
                    ]

                    # Handle cases with limited unique values
                    unique_areas = df["area"].nunique()
                    if unique_areas >= 4:
                        df["size_category"] = pd.qcut(
                            df["area"], q=4, labels=size_labels, duplicates="drop"
                        )
                    else:
                        # Alternative binning for limited unique values
                        df["size_category"] = pd.cut(
                            df["area"],
                            bins=unique_areas,
                            labels=size_labels[:unique_areas],
                        )

                    size_dist = df["size_category"].value_counts()
                    plt.pie(size_dist.values, labels=size_dist.index, autopct="%1.1f%%")
                    plt.title("Detection Size Distribution")
                except Exception as e:
                    plt.text(
                        0.5,
                        0.5,
                        "Insufficient variation in detection sizes",
                        ha="center",
                        va="center",
                    )
                    plt.title("Detection Size Distribution (Not Available)")

            elif plot_num == 7:
                # Plot 8: Temporal Pattern Analysis
                # Show patterns of when detections occur in sequence
                try:
                    detection_gaps = df.sort_values("frame")["frame"].diff()
                    if len(detection_gaps.dropna().unique()) > 1:
                        plt.hist(
                            detection_gaps.dropna(),
                            bins=min(30, len(detection_gaps.dropna().unique())),
                            edgecolor="black",
                        )
                        plt.xlabel("Frames Between Detections")
                        plt.ylabel("Frequency")
                        plt.title("Detection Temporal Pattern Analysis")
                    else:
                        plt.text(
                            0.5,
                            0.5,
                            "Uniform detection intervals",
                            ha="center",
                            va="center",
                        )
                        plt.title("Temporal Pattern Analysis (Uniform)")
                except Exception as e:
                    plt.text(
                        0.5, 0.5, "Insufficient temporal data", ha="center", va="center"
                    )
                    plt.title("Temporal Pattern Analysis (Not Available)")

            # Save plot to bytes
            buf = io.BytesIO()
            plt.savefig(buf, format="png", bbox_inches="tight")
            buf.seek(0)
            plots.append(Image.open(buf))
            plt.close()

        # Enhanced summary text
        summary = f"""Summary Statistics:
Total frames analyzed: {len(data["frame_detections"])}
Total detections: {len(df)}
Average detections per frame: {len(df) / len(data["frame_detections"]):.2f}

Detection Patterns:
- Peak detection count: {df.groupby("frame").size().max()} (in a single frame)
- Most common screen region: {df.groupby(["grid_y", "grid_x"]).size().idxmax()}
- Average detection size: {df["area"].mean():.3f}
- Median frames between detections: {detection_gaps.median():.1f}

Video metadata:
"""
        for key, value in data["video_metadata"].items():
            summary += f"{key}: {value}\n"

        return (
            plots[0],
            plots[1],
            plots[2],
            plots[3],
            plots[4],
            plots[5],
            plots[6],
            plots[7],
            summary,
        )

    except Exception as e:
        print(f"Error creating visualization: {str(e)}")
        import traceback

        traceback.print_exc()
        return (
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            f"Error creating visualization: {str(e)}",
        )


def fn_chat_video(message, history, uploaded_video_path_state):
    if uploaded_video_path_state is None:
        return "Chưa chạy video"

    messages = [
        {
            "role": "system",
            "content": (
                "You are an assistant designed to detect abnormal or illegal events in videos. Your working language is Vietnamese.\n\n"
                "Your task is to analyze the content of a video and identify any suspicious or unlawful activities, such as theft, assault, "
                "property damage, or any other unusual behavior.\n\n"
                "The scenarios include:\n"
                "knife: The video may depict a person carrying or threatening others with a knife, sword, or similar sharp object.\n"
                "wall-climb: The video may show unauthorized entry, such as climbing a wall or using ropes to bypass barriers.\n"
                "glass-break: The video may involve suspicious behavior around a vehicle, including potential vandalism, theft, or damage to mirrors or windows.\n\n"
                "Examples:\n"
                "- A person going around a car intending to break side mirrors.\n"
                 "Be aware of details about side mirrors being broken (mostly the thief break the car's side mirrors rather than break into the car).\n"
                "Provide a concise and accurate description of any abnormal or illegal behavior detected in the video."
            ),
        }
    ]
    # Construct the content for the current user message
    msg_text = message["text"]
    files = message["files"]
    if len(files) > 1:
        return "Hiện tại chưa hỗ trợ upload nhiều file"

    if len(history) == 0:
        current_user_content = [
            {"type": "text", "text": msg_text},
            {
                "type": "video",
                "video": uploaded_video_path_state,
                "total_pixels": 20480 * 28 * 28,
                "min_pixels": 16 * 28 * 2,
                "fps": 2.0,
            },
        ]
    else:
        first_msg = history[0]["content"]
        init_msg = [
            {"type": "text", "text": first_msg},
            {
                "type": "video",
                "video": uploaded_video_path_state,
                "total_pixels": 20480 * 28 * 28,
                "min_pixels": 16 * 28 * 2,
                "fps": 2.0,
            },
        ]
        messages.append({"role": "user", "content": init_msg})
        msg = {}
        for item in history[1:]:
            if item["role"] != msg.get("role"):
                if msg:
                    messages.append(msg)
                msg = {
                    "role": item["role"],
                    "content": [],
                }
            if isinstance(item["content"], tuple):
                file = item["content"][0]
                with open(file, "rb") as f:
                    encoded_image = base64.b64encode(f.read())
                encoded_image_text = encoded_image.decode("utf-8")
                base64_qwen = f"data:image;base64,{encoded_image_text}"
                msg["content"].append(
                    {
                        "type": "image_url",
                        "image_url": {"url": base64_qwen},
                    }
                )
            else:
                msg["content"].append({"type": "text", "text": item["content"]})
        if msg:
            messages.append(msg)
        current_user_content = msg_text
    if files:
        image_path = files[0]
        with open(image_path, "rb") as f:
            encoded_image = base64.b64encode(f.read())
        encoded_image_text = encoded_image.decode("utf-8")
        base64_qwen = f"data:image;base64,{encoded_image_text}"
        input_msg = {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": base64_qwen},
                },
                {"type": "text", "text": current_user_content},
            ],
        }
    else:
        input_msg = {"role": "user", "content": current_user_content}
    messages.append(input_msg)

    video_messages, video_kwargs = prepare_message_for_vllm(messages)
    stream = chat_client.chat.completions.create(
        model="Qwen/Qwen2.5-VL-32B-Instruct",
        messages=video_messages,
        temperature=0.1,
        extra_body={"mm_processor_kwargs": video_kwargs},
        stream=True,
    )
    partial_message = ""
    for chunk in stream:
        content = chunk.choices[0].delta.content or ""
        partial_message += content
        yield partial_message


def get_video_list(folder_path):
    """
    Returns a list of video files in the given folder.
    """
    video_extensions = ["*.mp4", "*.avi", "*.mov"]  # Add more if needed
    video_files = []
    for ext in video_extensions:
        video_files.extend(glob.glob(os.path.join(folder_path, ext)))
    return video_files


def search_image_in_video(image_path: str, video_paths: list[str]):
    """
    Searches for the given image in the given video.
    Returns the video path and the frame number where the image is found.
    """
    list_videos = []
    progress = gr.Progress()
    for video_path in progress.tqdm(video_paths, desc="Searching for image in video"):
        if find_image_in_video(image_path, video_path):
            list_videos.append(video_path)
    return list_videos


def handle_video_upload(uploaded_video_path):
    if uploaded_video_path is None:
        return None  # No file uploaded or upload failed

    # Create a persistent path in the inputs directory
    video_filename = f"input_{os.path.basename(uploaded_video_path)}"
    persistent_video_path = os.path.join(INPUTS_DIR, video_filename)
    shutil.copy2(uploaded_video_path, persistent_video_path)

    # Return the path to the copied file
    return persistent_video_path


# Create the Gradio interface
with gr.Blocks(title="VinIT - Smart Camera") as app:
    uploaded_video_path_state = gr.State(value=None)
    logo_path = "/mnt/disk1/hai_workspace/moondream/recipes/promptable-content-moderation/VinIT-Logo-Dark-Full.png"
    with gr.Row():
        with gr.Column(scale=1):  # Adjust scale for relative width
            gr.Image(
                logo_path,
                width=100,
                show_download_button=False,
                show_fullscreen_button=False,
                show_label=False,
            )
        with gr.Column(scale=5):  # Adjust scale for relative width
            gr.Markdown("# VinIT - Dự án camera thông minh của phòng AI") 
    with gr.Tabs():
        with gr.Tab("Process Video"):
            gr.Markdown(
                """
                # AI Agent xem xét video và báo cáo hành vi vi phạm pháp luật
                """
            )

            gr.Markdown(
                """
                Ứng dụng cũng đồng thời highlight đối tượng phụ thuộc vào ngữ cảnh người dùng input. Để được hỗ trợ, hãy liên hệ với team VinIT (Team AI).
            """
            )

            with gr.Row():
                with gr.Column():
                    # Input components
                    video_input = gr.Video(label="Upload Video", include_audio=False)

                    detect_input = gr.Textbox(
                        label="Nhập loại đối tượng cần xác định",
                        placeholder="e.g. face, cigarette, gun, etc.",
                        value="face",
                        info="AI Agent của VinSc có thể xác định bất kỳ loại đối tượng nào bạn có thể mô tả bằng tiếng Anh",
                    )

                    process_btn = gr.Button("Process Video", variant="primary")

                    with gr.Accordion("Advanced Settings", open=False):
                        box_style_input = gr.Radio(
                            choices=[
                                "censor",
                                "bounding-box",
                                "hitmarker",
                                "sam",
                                "sam-fast",
                            ],
                            value="bounding-box",
                            label="Visualization Style",
                            info="Chọn cách hiển thị kiểm duyệt: censor (hộp đen che), bounding-box (hộp đỏ có nhãn), hitmarker (dấu hiệu kiểu game COD), sam (phân đoạn chính xác), sam-fast (phân đoạn nhanh hơn nhưng kém chính xác hơn",
                        )
                        # preset_input = "fast"
                        # preset_input = gr.Dropdown(
                        #     choices=[
                        #         "ultrafast",
                        #         "superfast",
                        #         "veryfast",
                        #         "faster",
                        #         "fast",
                        #         "medium",
                        #         "slow",
                        #         "slower",
                        #         "veryslow",
                        #     ],
                        #     value="faster",
                        #     label="Processing Speed (faster = lower quality)",
                        # )
                        with gr.Row():
                            rows_input = gr.Slider(
                                minimum=1, maximum=4, value=1, step=1, label="Grid Rows"
                            )
                            cols_input = gr.Slider(
                                minimum=1,
                                maximum=4,
                                value=1,
                                step=1,
                                label="Grid Columns",
                            )

                        # test_mode_input = gr.Checkbox(
                        #     label="Test Mode (Process first 3 seconds only)",
                        #     value=True,
                        #     info="Enable to quickly test settings on a short clip before processing the full video (recommended). If using the data visualizations, disable.",
                        # )

                        # test_duration_input = gr.Slider(
                        #     minimum=1,
                        #     maximum=10,
                        #     value=3,
                        #     step=1,
                        #     label="Test Mode Duration (seconds)",
                        #     info="Number of seconds to process in test mode",
                        # )

                        # gr.Markdown(
                        #     """
                        # Note: Processing in test mode will only process the first 3 seconds of the video and is recommended for testing settings.
                        # """
                        # )

                        # gr.Markdown(
                        #     """
                        # We can get a rough estimate of how long the video will take to process by multiplying the videos framerate * seconds * the number of rows and columns and assuming 0.12 seconds processing time per detection.
                        # For example, a 3 second video at 30fps with 2x2 grid, the estimated time is 3 * 30 * 2 * 2 * 0.12 = 43.2 seconds (tested on a 4090 GPU).

                        # Note: Using the SAM visualization style will increase processing time significantly as it performs additional segmentation for each detection. The sam-fast option uses a smaller model for faster processing at the cost of some accuracy.
                        # """
                        # )

                with gr.Column():
                    # Output components
                    video_output = gr.Video(label="Processed Video")
                    json_output = gr.Text(label="Detection Data Path", visible=False)
                    demo = gr.ChatInterface(
                        fn_chat_video,
                        type="messages",
                        autofocus=False,
                        additional_inputs=uploaded_video_path_state,
                        multimodal=True,
                    )
        with gr.Tab("Search in Video"):
            gr.Markdown(
                """
                # Tìm kiếm hình ảnh trong nhiều video
                """
            )
            video_folder = "/mnt/disk1/hai_workspace/moondream/recipes/promptable-content-moderation/demo-video"
            video_list = get_video_list(video_folder)

            if not video_list:
                gr.Markdown("No videos found in the specified folder.")
            else:
                # Video Selection
                selected_video = gr.Dropdown(choices=video_list, label="Chọn Video")
                video_player = gr.Video(label="Video Player", include_audio=False)

                def play_video(video_path):
                    return video_path

                selected_video.change(
                    play_video, inputs=selected_video, outputs=video_player
                )

                # Image Search
                image_input = gr.Image(
                    type="filepath", label="Tải lên hình ảnh để tìm kiếm"
                )
                search_button = gr.Button("Tìm kiếm")
                search_results = gr.Gallery(label="Video Results", scale=2)

                def search_and_return_videos(image):
                    if image is None:
                        return []
                    found_videos = search_image_in_video(image, video_list)
                    return found_videos

                search_button.click(
                    search_and_return_videos, inputs=image_input, outputs=search_results
                )
    # Event handlers
    video_input.upload(
        fn=handle_video_upload,
        inputs=[video_input],
        outputs=[uploaded_video_path_state],
    )

    process_outputs = process_btn.click(
        fn=process_video_file,
        inputs=[
            uploaded_video_path_state,
            detect_input,
            box_style_input,
            rows_input,
            cols_input,
        ],
        outputs=[video_output, json_output],
    )

    # # Auto-analyze after processing
    # process_outputs.then(
    #     fn=create_visualization_plots,
    #     inputs=[json_output],
    #     outputs=[plot1, plot2, plot3, plot4, plot5, plot6, plot7, plot8, stats_output],
    # )

    # # Manual analysis button
    # analyze_btn.click(
    #     fn=create_visualization_plots,
    #     inputs=[json_input],
    #     outputs=[plot1, plot2, plot3, plot4, plot5, plot6, plot7, plot8, stats_output],
    # )

    # Video visualization button
    # visualize_btn.click(
    #     fn=lambda json_file, style: create_video_visualization(json_file.name if json_file else None, style),
    #     inputs=[json_input_realtime, viz_style],
    #     outputs=[video_visualization, stats_realtime],
    # )

if __name__ == "__main__":
    app.launch(share=False)
