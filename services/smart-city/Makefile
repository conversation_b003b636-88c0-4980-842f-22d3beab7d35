#!make
# Auto include environment variables to bash terminal for every cmd
include .env
export

OSFLAG :=
ifeq ($(OS),Windows_NT)
        OSFLAG = WIN
else
        UNAME_S := $(shell uname -s)
        ifeq ($(UNAME_S),Linux)
                OSFLAG = LINUX
        endif
        ifeq ($(UNAME_S),Darwin)
                OSFLAG = OSX
        endif
endif

# Run this to check if make is ready
hello:
        @conda --version
        @echo Ready for env "${APP_ENV}" in "$(OSFLAG)"
.PHONY: check

# Create virtual environment, only required at the first time if not existed
venv:
        @conda create -n smart-city python=3.10.14
        @python --version
.PHONY: venv

# First time setup with new computer, re-run might failed
# ffmpeg version 4.4.4 is required in macos, haven't found a proper way to install
bootstrap:
ifeq ($(OSFLAG),WIN)
        @choco install exiftool
        @echo Bootstrap completed
else ifeq ($(OSFLAG),OSX)
        @brew install exiftool
        @brew install ffmpeg@4
        @mv /opt/homebrew/opt/ffmpeg@4 /opt/homebrew/opt/ffmpeg
        @echo Bootstrap completed
else ifeq ($(OSFLAG),LINUX)
        @sudo apt-get -qq update && sudo apt-get install -y exiftool
        @echo Bootstrap completed
else
        $(error Unsupported OS)
endif
.PHONY: bootstrap

# Install dependencies, can be executed whenever dependencies change
install:
        @pip install poetry==2.1.2
        @poetry install --with lint,test
        @echo Install completed
.PHONY: setup

# Check all CI required steps
all: format lint test
.PHONY: all

# Run all tests with environment variables included
test:
        @pytest
.PHONY: test


# Check lint
lint:
        @ruff app src
.PHONY: lint

# Auto format code
format:
        @set -x
        @ruff format app src tests
        @ruff --fix app src
.PHONY: format

# Start local server
dev:
        @uvicorn app.main:app --reload --port 2000
.PHONY: dev