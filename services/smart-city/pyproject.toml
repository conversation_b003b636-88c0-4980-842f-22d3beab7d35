[project]
name = "smart-city"
version = "0.1.0"
description = ""
readme = "README.md"
requires-python = ">=3.11,<4"
dependencies = [
    "torch (==2.6.0)",
    "torchvision (==0.21.0)",
    "transformers (>=4.51.1,<5.0.0)",
    "accelerate (>=1.6.0,<2.0.0)",
    "xgrammar (==0.1.16)",
    "vllm (==0.8.2)",
    "qwen-vl-utils (>=0.0.10,<0.0.11)"
]

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "7.4.0"
pytest-cov = "4.0.0"
python-dotenv = "1.0.1"

[tool.poetry.group.lint]
optional = true

[tool.poetry.group.lint.dependencies]
ruff = "0.1.5"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.pytest.ini_options]
addopts = "--cov --cov-report term"
testpaths = ["tests"]

[tool.coverage.run]
source = ["src"]
omit = ["./venv/*", "*tests*", "*_Users_*"]

[tool.coverage.paths]
source = ["src", "/tmp/src"]

[tool.ruff]
line-length = 150
select = [
    "E", # pycodestyle errors
    "W", # pycodestyle warnings
    "F", # pyflakes
    "I", # isort
    "C", # flake8-comprehensions
    "B", # flake8-bugbear
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".mypy_cache",
    ".pytest_cache",
    ".ruff_cache",
    ".venv",
    "__pypackages__",
    "__pycache__",
    "build",
    "dist",
    "venv",
]