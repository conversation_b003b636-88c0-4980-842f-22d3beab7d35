# Introduction

This is a Python-based AI surveillance system designed to detect abnormal objects and behaviors using Vision-Language Large Models (VLMs). The system integrates real-time camera feeds and leverages advanced AI to identify potential safety or behavioral anomalies in residential environments. By continuously analyzing visual and contextual data, the system aims to enhance public safety, support incident prevention, and contribute to the overall quality of life for residents in VinHome communities.

# Installation

## Prerequisites

- Copy `.env_template` to `.env`, click on link in `.env` and replace link by secret from Bitwarden

**Window**

- Install [choco](https://chocolatey.org/install) package manager and run `choco install make`
- Install [Conda](https://docs.conda.io/projects/conda/en/latest/user-guide/install/windows.html)
- Open Anaconda Powershell Promt and run `conda init powershell` to activate conda for your Powershell
- Run `make hello` to verify whether it is ready for next step

## Steps

1. Install Python

Project is only compatible with `Python 3.11^`, it's recommended to install via conda

```bash
make venv
conda activate smart-city # To activate virtual environment (will auto activate at the next time open VSCode)
```

2. First time setup

Start terminal as administrator, then run the below install toolset at the first time with a new computer

```bash
make bootstrap
```

3. Install dependencies

Run the below command to download and install 3rd packages and libraries

```bash
make install
```

# Start Local Server

Once the system is installed, it can be run using the following command:

```bash
make dev
```

This will start the FastAPI server and the autopilot system will begin monitoring and controlling the greenhouse environment.

The system includes a swagger docs that can be accessed by visiting http://localhost:2000/docs in a web browser.

# Project code style

## Using scripts
The code style is formatted using `black` and `ruff`. The project's main style follows [PEP 8](https://peps.python.org/pep-0008/) and [google coding guidelines](https://google.github.io/styleguide/pyguide.html). Please use the code below to format or verify the code style.

```bash
make format # Format code
# bash scripts/format.sh if make is not available in your computer

make lint # Verify code style
# bash scripts/lint.sh if make is not available in your computer

make test # Run tests with coverage
```

## Using pre-commit hook
To automatically format the codebase without manually call above scripts. Let's install `pre-commit` as follows:

Using pip:
```
pip install pre-commit
```

Install the git hook scripts:
```
pre-commit install
```

Init pre-commit
```
pre-commit
```

Then commit your code as normal.