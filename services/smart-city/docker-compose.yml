version: "3.8"

services:
  vllm-node-1:
    image: vllm/vllm-openai:v0.8.4
    ports: []
    environment:
      - MODEL_NAME=Qwen/Qwen2.5-VL-3B-Instruct
    command: >
      --model Qwen/Qwen2.5-VL-3B-Instruct
      --dtype half
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    networks:
      - vllmnet

  vllm-node-2:
    image: vllm/vllm-openai:v0.8.4
    environment:
      - MODEL_NAME=Qwen/Qwen2.5-VL-3B-Instruct
    command: >
      --model Qwen/Qwen2.5-VL-3B-Instruct
      --dtype half
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    networks:
      - vllmnet

  vllm-node-3:
    image: vllm/vllm-openai:v0.8.4
    environment:
      - MODEL_NAME=Qwen/Qwen2.5-VL-3B-Instruct
    command: >
      --model Qwen/Qwen2.5-VL-3B-Instruct
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    networks:
      - vllmnet

  vllm-node-4:
    image: vllm/vllm-openai:v0.8.4
    environment:
      - MODEL_NAME=Qwen/Qwen2.5-VL-3B-Instruct
    command: >
      --model Qwen/Qwen2.5-VL-3B-Instruct
      --dtype half
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    networks:
      - vllmnet

  nginx:
    image: nginx:stable-alpine3.20-perl
    ports:
      - "8000:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - vllm-node-1
      - vllm-node-2
      - vllm-node-3
      - vllm-node-4
    networks:
      - vllmnet

networks:
  vllmnet:
    driver: bridge
