import base64
import numpy as np
from PIL import Image
from io import BytesIO
from openai import OpenAI
from time import time
from qwen_vl_utils import process_vision_info
import gc

import os
import json
import logging  # For better logging
from typing import Dict


def prepare_message_for_vllm(content_messages):
    """Prepares messages for vLLM, extracting frames using qwen_vl_utils."""
    vllm_messages, fps_list = [], []
    for message in content_messages:
        # Create a deep copy to avoid modifying the original message dict
        message_copy = json.loads(json.dumps(message))  # Simple deep copy for dicts/lists
        message_content_list = message_copy.get("content")

        if not isinstance(message_content_list, list):
            vllm_messages.append(message_copy)
            continue

        new_content_list = []
        video_processed = False
        for part_message in message_content_list:
            # Check if it's a video part and needs processing
            if isinstance(part_message, dict) and part_message.get("type") == "video":
                video_path = part_message.get("video")
                if video_path and os.path.exists(video_path):
                    # Use qwen_vl_utils to process the video file
                    temp_message_for_processing = [{"role": "user", "content": [part_message.copy()]}]  # Pass a copy
                    try:
                        # Pass fps from part_message if available, otherwise default
                        fps_setting = part_message.get("fps", 1.0)
                        # part_message_copy = part_message.copy() # Avoid modifying original dict - already working on a copy
                        part_message["fps"] = fps_setting  # Modify the part within the copied message

                        image_inputs, video_inputs, video_kwargs = process_vision_info(temp_message_for_processing, return_video_kwargs=True)
                        if video_inputs is None:
                            logging.warning(f"Could not process video {video_path} with qwen_vl_utils.")
                            new_content_list.append(part_message)  # Keep original if failed
                            continue

                        video_input = (video_inputs.pop()).permute(0, 2, 3, 1).numpy().astype(np.uint8)
                        fps_list.extend(video_kwargs.get("fps", []))

                        # encode image with base64
                        base64_frames = []
                        for frame in video_input:
                            img = Image.fromarray(frame)
                            output_buffer = BytesIO()
                            img.save(output_buffer, format="jpeg")
                            byte_data = output_buffer.getvalue()
                            base64_str = base64.b64encode(byte_data).decode("utf-8")
                            base64_frames.append(base64_str)

                        # Replace the original video part with the base64 encoded version for vLLM
                        processed_part = {
                            "type": "video_url",
                            "video_url": {"url": f"data:video/jpeg;base64,{','.join(base64_frames)}"},
                        }
                        new_content_list.append(processed_part)
                        video_processed = True  # Mark that we successfully processed a video part

                    except Exception as e:
                        logging.error(
                            f"Error processing video {video_path} with qwen_vl_utils: {e}",
                            exc_info=True,
                        )
                        new_content_list.append(part_message)  # Keep original if error
                        continue
                elif video_path:
                    logging.warning(f"Video path specified but not found: {video_path}")
                    new_content_list.append(part_message)  # Keep original if not found
                    continue
                else:
                    # Not a video part needing our processing, or missing path
                    new_content_list.append(part_message)
            else:
                # Keep non-video parts as they are
                new_content_list.append(part_message)

        message_copy["content"] = new_content_list
        vllm_messages.append(message_copy)

    # Only include video_kwargs if a video was actually processed
    final_video_kwargs = {"fps": fps_list} if video_processed else {}
    return vllm_messages, final_video_kwargs


def verify_clip_with_llm(
    openai_client: OpenAI, clip_path: str, event_descriptions: Dict[str, str], clip_duration: int, list_detected_label: list[str]
):
    """
    Sends a specific video clip to the LLM for verification against ONE event type.

    Args:
        clip_path (str): Path to the temporary video clip file.
        event_description (str): The description of the specific event to check for.
        clip_duration (int): The duration of the clip (for the prompt).

    Returns:
        tuple: (bool, str) indicating if the specific event was verified (True/False)
               and the reason provided by the LLM. Returns (False, "LLM Error") on failure.
    """

    event_prompt_text = "\n".join([f'- "{label}": {description}' for label, description in event_descriptions.items()])

    prompt = f"""
    You are a video analysis assistant. Watch the {clip_duration}-second video carefully.

    Detected objects in the video: {", ".join(list_detected_label)}

    Check if the following events occur:
    {event_prompt_text}

    Respond **only** one event that occur with JSON in the following format, no explanations outside of it:

    Example:
    ```json
    {{
        "reason": "..."
        "event": "<event_label>",
        "label": 1,  // 1 = event occurred, 0 = not occurred
    }}
    ```
    Instructions:

    - If an event clearly occurs, set "label": 1 and give a brief reason.

    - If not, set "label": 0 and briefly explain why (e.g., "No pet visible in the clip.").


    Note: If no events occur in the entire video, return an empty JSON {{}}.

    Only return the final JSON:
    ```json
    """

    # Frame extraction parameters
    qwen_vl_fps = 5.0
    total_pixels = 20480 * 28 * 28
    min_pixels = 16 * 28 * 2

    video_messages = [
        {
            "role": "system",
            "content": "You are a precise video analysis assistant focused on specific event detection. Only report events that clearly and unquestionably occur. Be concise and avoid any assumptions or unnecessary details",
        },
        {
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {
                    "type": "video",
                    "video": clip_path,
                    "total_pixels": total_pixels,
                    "min_pixels": min_pixels,
                    "fps": qwen_vl_fps,
                },
            ],
        },
    ]

    results: Dict[str, str] = {}
    prepared_messages = None

    try:
        prepared_messages, video_kwargs = prepare_message_for_vllm(video_messages)

        # Check if video processing actually prepared a video part for the LLM
        video_part_found = False
        for msg in prepared_messages:
            content = msg.get("content")
            if isinstance(content, list):
                for part in content:
                    if isinstance(part, dict) and part.get("type") == "video_url":
                        video_part_found = True
                        break
            if video_part_found:
                break

        if not video_part_found:
            logging.warning(f"Skipping LLM call for {os.path.basename(clip_path)} as video processing failed or yielded no video data.")
            return None

        # Ensure extra_body is included only if video_kwargs is not empty
        extra_body_args = {}
        if video_kwargs:
            extra_body_args["mm_processor_kwargs"] = video_kwargs

        start_time = time()
        chat_response = openai_client.chat.completions.create(
            model="Qwen/Qwen2.5-VL-3B-Instruct",  # Make sure model name is correct
            messages=prepared_messages,
            extra_body=extra_body_args if extra_body_args else None,  # Pass only if non-empty
            max_tokens=150,
            temperature=0.1,
            timeout=20,
        )
        llm_time = time() - start_time
        logging.info(f"LLM call for clip {os.path.basename(clip_path)}, took {llm_time:.2f}s")

        content = chat_response.choices[0].message.content.strip()

        # Extract JSON robustly
        json_result = None
        json_str = ""
        try:
            # Try extracting from ```json ... ```
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
                json_result = json.loads(json_str)
            # Try extracting from ``` ... ```
            elif "```" in content and content.startswith("```") and content.endswith("```"):
                json_str = content.split("```")[1].strip()
                # Sometimes the model might still include the word 'json' after ```
                if json_str.lower().startswith("json"):
                    json_str = json_str[4:].strip()
                json_result = json.loads(json_str)
            # Try parsing the whole content if no backticks and looks like JSON
            elif content.startswith("{") and content.endswith("}"):
                json_str = content
                json_result = json.loads(json_str)
            else:
                logging.error(f"LLM output for {os.path.basename(clip_path)} doesn't contain expected JSON markers: {content}")

        except json.JSONDecodeError as json_e:
            logging.error(f"Could not decode JSON for {os.path.basename(clip_path)}. String: '{json_str}'. Error: {json_e}. Raw Content: {content}")
        if not json_result:
            return results

        event_label = json_result["event"]
        label = json_result.get("label", 0)
        if event_label in event_descriptions and label:
            results[event_label] = {"reason": json_result["reason"], "label": label}
            print(json_result)
        else:
            logging.warning(f"LLM returned result for unexpected event: '{event_label}' - '{label}'")

    except Exception as e:
        logging.error(
            f"Error during LLM call for clip {os.path.basename(clip_path)}: {e}",
            exc_info=True,
        )
    finally:
        # Clean up frames? qwen_vl_utils might handle this, but double-check if memory grows.
        del video_messages
        if prepared_messages is not None:
            del prepared_messages

        if "chat_response" in locals():
            del chat_response
        if "json_result" in locals():
            del json_result
        if "video_kwargs" in locals():
            del video_kwargs
        gc.collect()  # Force garbage collection
    return results
