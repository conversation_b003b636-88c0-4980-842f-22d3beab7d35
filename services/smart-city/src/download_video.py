import json
import os

import pandas as pd
import requests
from tqdm import tqdm

tqdm.pandas()

os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)

file_root = "video_dir"


TOKEN_URL = "https://ocp2.vinhomes.vn/api/iam/v0/login/refresh-token"

REFRESH_TOKEN = os.environ["DOWNLOAD_VIDEO_REFRESH_TOKEN"]


def login_token():
    """Logs in using the refresh token and returns the new access token."""
    headers = {"accept": "*/*", "Content-Type": "application/json"}
    data = {"refreshToken": REFRESH_TOKEN}
    try:
        response = requests.post(TOKEN_URL, headers=headers, data=json.dumps(data))
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        return response.json()["data"]["access_token"]
    except requests.exceptions.RequestException as e:
        print(f"Error refreshing token: {e}")
        return None


def download_video(token, event_id, video_id):
    """Downloads the video using the provided token."""
    file_path = os.path.join(file_root, f"{video_id}.mp4")
    if os.path.exists(file_path):
        return
    url = f"https://ocp2.vinhomes.vn/api/cmsai-event/v0/event/{event_id}/video/{video_id}"
    headers = {"Authorization": f"Bearer {token}"}

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        if not data.get("data"):
            return
        link_download = data["data"]
        response = requests.get(link_download, headers=headers)
        if response.ok:
            with open(os.path.join(file_root, f"{video_id}.mp4"), "wb") as f:
                f.write(response.content)
            print("Download video")
        else:
            print(f"Failed to download video content. Status code: {response.status_code}, Response: {response.text}")
    else:
        print(f"Failed to get video download link. Status code: {response.status_code}, Response: {response.text}")


if __name__ == "__main__":
    df = pd.read_csv("event_snapshot.csv")
    token = login_token()

    for idx, row in tqdm(df.iterrows(), total=len(df)):
        download_video(token=token, event_id=row["event_id"], video_id=row["id_x"])
