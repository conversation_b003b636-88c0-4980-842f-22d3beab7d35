import base64
from glob import glob
import numpy as np
from PIL import Image
from io import BytesIO
from openai import OpenAI
from time import time
from qwen_vl_utils import process_vision_info
from ultralytics import YOLO
import cv2
from itertools import cycle
import concurrent.futures


from tqdm import tqdm
import os
import json
import tempfile  # For temporary clip files
from moviepy import VideoFileClip, CompositeVideoClip, vfx
import shutil  # For moving and copying files
import logging  # For better logging
from typing import Dict

import torch

torch.cuda.set_device(0)


# --- Configuration ---
BASE_VIDEO_DIR = "/home/<USER>/nvr/*.mp4"  # Directory containing source videos
OUTPUT_DATASET_DIR = (
    "./kinetics400_multi_event_dataset"  # Root directory for the generated dataset
)
CLIP_DURATION = 30  # Desired clip duration in seconds
STEP_SIZE = 10  # Sliding window step in seconds (creates overlap)

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


SERVERS = [
    "http://127.0.0.1:8000/v1",  # Adjusted to typical v1 API path
    "http://127.0.0.1:8001/v1",
    "http://127.0.0.1:8002/v1",
    "http://127.0.0.1:8003/v1",
]


# Map the final labels (folder names) back to the descriptive prompt for the LLM
# Keys MUST match the values in EVENT_LABEL_MAP
LABEL_PROMPT_DESCRIPTION = {
    "human_detection_face": "Video phải có con người đi qua lại và hiện rõ khuôn mặt chính diện trong video",
    "pet_detection": "Video phải có vật nuôi đi lại",
    "illegal_parking_car": "Video phải có xe oto đang đậu ngoài lề đường",
    "kidnap": "Video phải có cảnh bắt cóc người",
    "trespassing": "Video phải có người đang hoặc đã trèo tường hoặc bờ rào",
    "uncovered_truck": "Video phải có xe tải chở vật liệu xây dựng không che phủ",
}

# --- OpenAI Client Setup ---
os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)

SERVER_CYCLE = cycle(SERVERS)  # Creates an iterator that cycles through servers
NUM_WORKERS = len(SERVERS) * 2  # Use one worker per server


# --- Helper Functions (Adapted from your example) ---


def prepare_message_for_vllm(content_messages):
    """Prepares messages for vLLM, extracting frames using qwen_vl_utils."""
    vllm_messages, fps_list = [], []
    for message in content_messages:
        # Create a deep copy to avoid modifying the original message dict
        message_copy = json.loads(
            json.dumps(message)
        )  # Simple deep copy for dicts/lists
        message_content_list = message_copy.get("content")

        if not isinstance(message_content_list, list):
            vllm_messages.append(message_copy)
            continue

        new_content_list = []
        video_processed = False
        for part_message in message_content_list:
            # Check if it's a video part and needs processing
            if isinstance(part_message, dict) and part_message.get("type") == "video":
                video_path = part_message.get("video")
                if video_path and os.path.exists(video_path):
                    # Use qwen_vl_utils to process the video file
                    temp_message_for_processing = [
                        {"role": "user", "content": [part_message.copy()]}
                    ]  # Pass a copy
                    try:
                        # Pass fps from part_message if available, otherwise default
                        fps_setting = part_message.get("fps", 1.0)
                        # part_message_copy = part_message.copy() # Avoid modifying original dict - already working on a copy
                        part_message["fps"] = (
                            fps_setting  # Modify the part within the copied message
                        )

                        image_inputs, video_inputs, video_kwargs = process_vision_info(
                            temp_message_for_processing, return_video_kwargs=True
                        )
                        if video_inputs is None:
                            logging.warning(
                                f"Could not process video {video_path} with qwen_vl_utils."
                            )
                            new_content_list.append(
                                part_message
                            )  # Keep original if failed
                            continue

                        video_input = (
                            (video_inputs.pop())
                            .permute(0, 2, 3, 1)
                            .numpy()
                            .astype(np.uint8)
                        )
                        fps_list.extend(video_kwargs.get("fps", []))

                        # encode image with base64
                        base64_frames = []
                        for frame in video_input:
                            img = Image.fromarray(frame)
                            output_buffer = BytesIO()
                            img.save(output_buffer, format="jpeg")
                            byte_data = output_buffer.getvalue()
                            base64_str = base64.b64encode(byte_data).decode("utf-8")
                            base64_frames.append(base64_str)

                        # Replace the original video part with the base64 encoded version for vLLM
                        processed_part = {
                            "type": "video_url",
                            "video_url": {
                                "url": f"data:video/jpeg;base64,{','.join(base64_frames)}"
                            },
                        }
                        new_content_list.append(processed_part)
                        video_processed = (
                            True  # Mark that we successfully processed a video part
                        )

                    except Exception as e:
                        logging.error(
                            f"Error processing video {video_path} with qwen_vl_utils: {e}",
                            exc_info=True,
                        )
                        new_content_list.append(part_message)  # Keep original if error
                        continue
                elif video_path:
                    logging.warning(f"Video path specified but not found: {video_path}")
                    new_content_list.append(part_message)  # Keep original if not found
                    continue
                else:
                    # Not a video part needing our processing, or missing path
                    new_content_list.append(part_message)
            else:
                # Keep non-video parts as they are
                new_content_list.append(part_message)

        message_copy["content"] = new_content_list
        vllm_messages.append(message_copy)

    # Only include video_kwargs if a video was actually processed
    final_video_kwargs = {"fps": fps_list} if video_processed else {}
    return vllm_messages, final_video_kwargs


def detect_objects_yolov8(model, video_path: str) -> bool:
    """
    Uses YOLOv8 to detect objects (people, car, dog, cat, truck) in the video.

    Args:
        video_path (str): Path to the video file.

    Returns:
        bool: True if any target object is detected, False otherwise.
    """
    # Load YOLOv8 model (pre-trained on COCO dataset)

    # Target classes from COCO dataset
    target_classes = ["person", "car", "dog", "cat", "truck"]

    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logging.error(f"Failed to open video: {video_path}")
        return False
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_interval = int(fps)  # process 1 frame per second

    detected = False
    frame_idx = 0
    count = 0

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        if frame_idx % frame_interval == 0:
            resized_frame = cv2.resize(frame, (640, 360))
            results = model(resized_frame, verbose=False)
            count += 1

        for result in results:
            for box in result.boxes:
                class_name = model.names[int(box.cls)]
                if class_name in target_classes:
                    detected = True
                    break
            if detected:
                break

        if detected:
            break

        frame_idx += 1

    cap.release()
    return detected


def verify_clip_with_llm(
    openai_client: OpenAI,
    model_yolo,
    clip_path: str,
    event_descriptions: Dict[str, str],
    clip_duration: int,
):
    """
    Sends a specific video clip to the LLM for verification against ONE event type.

    Args:
        clip_path (str): Path to the temporary video clip file.
        event_description (str): The description of the specific event to check for.
        clip_duration (int): The duration of the clip (for the prompt).

    Returns:
        tuple: (bool, str) indicating if the specific event was verified (True/False)
               and the reason provided by the LLM. Returns (False, "LLM Error") on failure.
    """
    if not detect_objects_yolov8(model_yolo, clip_path):
        logging.info(f"No relevant objects detected in {os.path.basename(clip_path)}")
        return None

    event_prompt_text = "\n".join(
        [
            f'- "{label}": {description}'
            for label, description in event_descriptions.items()
        ]
    )

    prompt = f"""
    Bạn là trợ lý phân tích video. Hãy xem kỹ clip video dài {clip_duration} giây này.
    Kiểm tra xem các sự kiện sau có xảy ra trong clip không:
    {event_prompt_text}
    Chỉ trả lời bằng định dạng JSON sau, không thêm bất kỳ giải thích nào khác bên ngoài JSON.

    Ví dụ JSON:
    ```json
    {{
        "event": "dog_present",
        "reason": "A dog is clearly visible in the clip."
    }}
    Nếu các sự kiện ĐƯỢC MÔ TẢ CỤ THỂ xảy ra, và nêu lý do ngắn gọn tại sao sự kiện đó không xảy ra trong "reason".
    Note: Nếu không có sự kiện nào xãy ra, thì trả lời là một JSON đầy đủ rỗng (ex: '{{}}').
    Kết quả của bạn (CHỈ JSON):
    ```json
    """

    # Frame extraction parameters
    qwen_vl_fps = 1.0
    total_pixels = 20480 * 28 * 28
    min_pixels = 16 * 28 * 2

    video_messages = [
        {
            "role": "system",
            "content": "You are a helpful video analysis assistant focused on specific event detection.",
        },
        {
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {
                    "type": "video",
                    "video": clip_path,
                    "total_pixels": total_pixels,
                    "min_pixels": min_pixels,
                    "fps": qwen_vl_fps,
                },
            ],
        },
    ]

    results: Dict[str, str] = {}

    try:
        prepared_messages, video_kwargs = prepare_message_for_vllm(video_messages)

        # Check if video processing actually prepared a video part for the LLM
        video_part_found = False
        for msg in prepared_messages:
            content = msg.get("content")
            if isinstance(content, list):
                for part in content:
                    if isinstance(part, dict) and part.get("type") == "video_url":
                        video_part_found = True
                        break
            if video_part_found:
                break

        if not video_part_found:
            logging.warning(
                f"Skipping LLM call for {os.path.basename(clip_path)} as video processing failed or yielded no video data."
            )
            return None

        # Ensure extra_body is included only if video_kwargs is not empty
        extra_body_args = {}
        if video_kwargs:
            extra_body_args["mm_processor_kwargs"] = video_kwargs

        start_time = time()
        chat_response = openai_client.chat.completions.create(
            model="Qwen/Qwen2.5-VL-3B-Instruct-AWQ",  # Make sure model name is correct
            messages=prepared_messages,
            extra_body=extra_body_args
            if extra_body_args
            else None,  # Pass only if non-empty
            max_tokens=150,
            temperature=0.1,
        )
        llm_time = time() - start_time
        logging.debug(
            f"LLM call for clip {os.path.basename(clip_path)}, took {llm_time:.2f}s"
        )

        content = chat_response.choices[0].message.content.strip()

        # Extract JSON robustly
        json_result = None
        json_str = ""
        try:
            # Try extracting from ```json ... ```
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
                json_result = json.loads(json_str)
            # Try extracting from ``` ... ```
            elif (
                "```" in content
                and content.startswith("```")
                and content.endswith("```")
            ):
                json_str = content.split("```")[1].strip()
                # Sometimes the model might still include the word 'json' after ```
                if json_str.lower().startswith("json"):
                    json_str = json_str[4:].strip()
                json_result = json.loads(json_str)
            # Try parsing the whole content if no backticks and looks like JSON
            elif content.startswith("{") and content.endswith("}"):
                json_str = content
                json_result = json.loads(json_str)
            else:
                logging.error(
                    f"LLM output for {os.path.basename(clip_path)} doesn't contain expected JSON markers: {content}"
                )

        except json.JSONDecodeError as json_e:
            logging.error(
                f"Could not decode JSON for {os.path.basename(clip_path)}. String: '{json_str}'. Error: {json_e}. Raw Content: {content}"
            )
        if not json_result:
            return results

        event_label = json_result["event"]
        if event_label in event_descriptions:
            results[event_label] = json_result["reason"]
        else:
            logging.warning(f"LLM returned result for unexpected event: {event_label}")

    except Exception as e:
        logging.error(
            f"Error during LLM call for clip {os.path.basename(clip_path)}: {e}",
            exc_info=True,
        )
    finally:
        # Clean up frames? qwen_vl_utils might handle this, but double-check if memory grows.
        pass
    return results


def process_clip_task(
    server_base_url: str,
    temp_clip_path: str,
    video_id: str,
    start_time: float,
    end_time: float,
    event_descriptions: Dict[str, str],
    clip_duration: int,
    output_dataset_dir: str,
) -> bool:
    """
    Task executed by each thread: creates client, verifies clip, saves if needed.

    Returns:
        bool: True if the clip was saved for *any* event, False otherwise.
    """
    clip_saved_for_any_event = False
    base_clip_name = os.path.basename(temp_clip_path)

    # Create a unique OpenAI client for this task/thread targeting the assigned server
    try:
        # You might need to add api_key='YOUR_API_KEY' if your vLLM servers require it
        # Or configure OPENAI_API_KEY environment variable
        # For local vLLM, often no key is needed, so api_key="no-key" might work.
        client = OpenAI(
            base_url=server_base_url, api_key="no-key"
        )  # Adjust api_key if needed
    except Exception as client_e:
        logging.error(
            f"Failed to create OpenAI client for {server_base_url}: {client_e}"
        )
        # Clean up the temporary clip if client creation fails
        if temp_clip_path and os.path.exists(temp_clip_path):
            try:
                os.remove(temp_clip_path)
                logging.debug(
                    f"Cleaned up temp file {base_clip_name} due to client error."
                )
            except OSError as remove_e:
                logging.error(f"Error removing temp file {base_clip_name}: {remove_e}")
        return False  # Cannot proceed

    # --- Verify the extracted clip with LLM ---
    llm_results = {}  # Default to empty results
    if os.path.exists(temp_clip_path) and os.path.getsize(temp_clip_path) > 0:
        logging.debug(
            f"  Worker verifying clip {base_clip_name} on {server_base_url}..."
        )
        model = YOLO(
            "yolo11n.pt"
        )  # Use yolov8n for lightweight model, adjust as needed
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        llm_results = verify_clip_with_llm(
            client, model, temp_clip_path, event_descriptions, clip_duration
        )
    else:
        logging.warning(
            f"Temp clip {temp_clip_path} not found or empty before LLM call."
        )
        # No LLM results possible, will skip saving below.

    # --- Process LLM results and Save Clip (if applicable) ---
    if not llm_results:
        # This happens if YOLO failed, LLM errored, video processing failed, or LLM returned {}
        logging.info(
            f"  No verifiable events found by LLM for clip {base_clip_name} [{start_time:.2f}s - {end_time:.2f}s]."
            # Decide if you want to save 'unknown' clips even if LLM returns empty {}
            # Current logic based on original code: Save as 'unknown' ONLY if LLM processing failed ENTIRELY (original code behavior)
            # If LLM successfully returned {}, it means "no events found", so we DON'T save as unknown here.
            # The original code saved as 'unknown' if llm_results was None/False, which maps to exceptions or YOLO fail.
            # Let's refine: Save as unknown only if verify_clip_with_llm had an internal error maybe?
            # For now, let's stick to NOT saving if llm_results is empty {}.
        )
        # Optionally save to 'unknown' if verify_clip_with_llm returned a specific error signal?
        # Example: modify verify_clip_with_llm to return None on critical error, {} otherwise
        output_clip_dir = os.path.join(OUTPUT_DATASET_DIR, "unknown")
        if not os.path.exists(output_clip_dir):
            os.mkdir(output_clip_dir)
        output_clip_name = f"{video_id}{int(start_time)}{int(end_time)}.mp4"
        final_clip_path = os.path.join(output_clip_dir, output_clip_name)
        shutil.copy2(temp_clip_path, final_clip_path)
    else:
        # Iterate through verified event types returned by the LLM
        for event_label, reason in llm_results.items():
            logging.debug(
                f"    LLM Result for '{event_label}' on {base_clip_name}: Reason='{reason}'"
            )
            # --- Save a COPY of the verified clip for this event ---
            output_clip_dir = os.path.join(output_dataset_dir, event_label)
            # No need to create dir here, done at the start

            output_clip_name = f"{video_id}_{int(start_time)}_{int(end_time)}.mp4"
            final_clip_path = os.path.join(output_clip_dir, output_clip_name)

            # Check if it already exists (less likely with unique names, but good practice)
            if not os.path.exists(final_clip_path):
                logging.info(
                    f"  Event '{event_label}' VERIFIED in clip {base_clip_name} [{start_time:.2f}s - {end_time:.2f}s]. Saving to {final_clip_path}"
                )
                try:
                    # Use copy2 to preserve metadata
                    shutil.copy2(temp_clip_path, final_clip_path)
                    clip_saved_for_any_event = True
                except Exception as copy_e:
                    logging.error(
                        f"Failed to copy clip {temp_clip_path} to {final_clip_path}: {copy_e}"
                    )
            else:
                logging.debug(
                    f"  Clip {final_clip_path} already exists, skipping copy for event '{event_label}'."
                )
                clip_saved_for_any_event = True  # Mark as 'saved' conceptually

    # --- Clean up the temporary clip ---
    if temp_clip_path and os.path.exists(temp_clip_path):
        try:
            os.remove(temp_clip_path)
            logging.debug(f"Cleaned up temp file {base_clip_name}")
        except OSError as remove_e:
            logging.error(f"Error removing temp file {base_clip_name}: {remove_e}")

    return clip_saved_for_any_event


# --- Main Processing Logic ---
def main():
    # Create the main output directory and subdirectories for target labels
    os.makedirs(OUTPUT_DATASET_DIR, exist_ok=True)
    for event_label in LABEL_PROMPT_DESCRIPTION.keys():
        os.makedirs(os.path.join(OUTPUT_DATASET_DIR, event_label), exist_ok=True)
    # Also create unknown if you decide to use it
    os.makedirs(os.path.join(OUTPUT_DATASET_DIR, "unknown"), exist_ok=True)

    logging.info(
        f"Starting dataset generation. Output: {OUTPUT_DATASET_DIR}. Workers: {NUM_WORKERS}"
    )

    # Use ThreadPoolExecutor to manage worker threads
    # max_workers=NUM_WORKERS ensures we don't overload the servers
    with concurrent.futures.ThreadPoolExecutor(max_workers=NUM_WORKERS) as executor:
        futures = []  # Keep track of submitted tasks

        video_files = glob(BASE_VIDEO_DIR)
        if not video_files:
            logging.error(f"No video files found matching pattern: {BASE_VIDEO_DIR}")
            return

        # Process each unique video ID
        for source_video_path in tqdm(video_files, desc="Processing Videos"):
            if not os.path.isfile(source_video_path):  # Check if it's actually a file
                logging.warning(f" Source path is not a file: {source_video_path}")
                continue

            video_id = os.path.basename(source_video_path).split(".")[0]
            logging.info(f"Starting processing for video: {video_id}")

            try:
                # Get duration quickly first
                try:
                    video = VideoFileClip(source_video_path)
                    duration = video.duration
                    video.close()  # Close immediately after getting duration
                except Exception as load_e:
                    logging.error(
                        f"Failed to load video {source_video_path} to get duration: {load_e}"
                    )
                    continue  # Skip this video

                if duration < CLIP_DURATION:
                    logging.info(
                        f"Video {video_id} shorter than clip duration, processing whole video."
                    )
                    # Handle video shorter than CLIP_DURATION - extract once
                    temp_clip_path = ""
                    try:
                        with tempfile.NamedTemporaryFile(
                            suffix=".mp4", delete=False
                        ) as temp_clip:
                            temp_clip_path = temp_clip.name
                        # Copy the whole short video instead of re-encoding
                        shutil.copy2(source_video_path, temp_clip_path)

                        # Submit this single clip task
                        server_url = next(SERVER_CYCLE)
                        future = executor.submit(
                            process_clip_task,
                            server_url,
                            temp_clip_path,  # Pass the path to the copy
                            video_id,
                            0,
                            duration,
                            LABEL_PROMPT_DESCRIPTION,
                            int(duration),  # Use actual duration for prompt
                            OUTPUT_DATASET_DIR,
                        )
                        futures.append(future)

                    except Exception as e:
                        logging.error(f"Error processing short video {video_id}: {e}")
                        if temp_clip_path and os.path.exists(temp_clip_path):
                            try:
                                os.remove(temp_clip_path)
                            except OSError:
                                pass
                    continue  # Move to the next video file

                # Iterate through the video using a sliding window for longer videos
                for start_time in np.arange(
                    0, duration - CLIP_DURATION + 1e-6, STEP_SIZE
                ):
                    end_time = start_time + CLIP_DURATION
                    end_time = min(end_time, duration)  # Clamp to actual duration
                    actual_clip_duration = end_time - start_time

                    # Check duration again, useful if STEP_SIZE doesn't align perfectly
                    if abs(actual_clip_duration - CLIP_DURATION) > 0.1:
                        logging.debug(
                            f"Skipping segment [{start_time:.2f}-{end_time:.2f}] for {video_id}: incorrect duration {actual_clip_duration:.2f}"
                        )
                        continue

                    # --- Extract the clip to a temporary file ---
                    temp_clip_path = ""
                    try:
                        # Create a unique temp file for each clip submission
                        with tempfile.NamedTemporaryFile(
                            suffix=".mp4", delete=False
                        ) as temp_clip:
                            temp_clip_path = temp_clip.name

                        logging.debug(
                            f"  Extracting clip: {video_id} [{start_time:.2f}s - {end_time:.2f}s] to {temp_clip_path}"
                        )
                        # Load video only when needed for subclip
                        video = VideoFileClip(source_video_path)
                        clip = video.subclipped(start_time, end_time).with_effects(
                            [vfx.Resize((640, 360))]
                        )

                        final_video = CompositeVideoClip([clip])
                        # Ensure no audio processing if not needed
                        final_video.write_videofile(
                            temp_clip_path,
                            codec="libx264",
                            audio=False,
                            logger=None,
                            fps=1,
                        )  # Change video to 1 fps
                        final_video.close()
                        video.close()  # Close video file handle

                        # Check if extraction actually produced a file
                        if (
                            not os.path.exists(temp_clip_path)
                            or os.path.getsize(temp_clip_path) == 0
                        ):
                            logging.warning(
                                f"Clip extraction failed or produced empty file for {video_id} [{start_time:.2f}-{end_time:.2f}]"
                            )
                            if os.path.exists(temp_clip_path):
                                os.remove(temp_clip_path)
                            continue  # Skip this segment

                        # --- Submit the verification task to the thread pool ---
                        server_url = next(
                            SERVER_CYCLE
                        )  # Get next server URL round-robin
                        future = executor.submit(
                            process_clip_task,
                            server_url,
                            temp_clip_path,
                            video_id,
                            start_time,
                            end_time,
                            LABEL_PROMPT_DESCRIPTION,
                            CLIP_DURATION,  # Use the target duration for the prompt
                            OUTPUT_DATASET_DIR,
                        )
                        futures.append(future)

                    except Exception as e:
                        logging.error(
                            f"Error extracting or submitting clip [{start_time:.2f}-{end_time:.2f}] from {video_id}: {e}",
                            exc_info=True,
                        )
                        # Clean up temp file if extraction failed before submission
                        if temp_clip_path and os.path.exists(temp_clip_path):
                            try:
                                os.remove(temp_clip_path)
                            except OSError:
                                pass
                        continue  # Skip to next clip start time

            except Exception as e:
                # Catch errors related to processing a whole video file
                logging.error(
                    f"Unhandled error processing video file {source_video_path}: {e}",
                    exc_info=True,
                )

        # --- Wait for all submitted tasks to complete ---
        logging.info(
            f"All clip extraction and task submission finished. Waiting for {len(futures)} LLM evaluations..."
        )

        completed_count = 0
        clip_saved_total_count = 0
        # Use as_completed to process results as they finish (optional, good for large scale)
        for future in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(futures),
            desc="Evaluating Clips",
        ):
            try:
                # Get the result (True if saved, False otherwise)
                saved = future.result()
                if saved:
                    clip_saved_total_count += 1
                completed_count += 1
            except Exception as e:
                logging.error(f"A clip processing task failed: {e}", exc_info=True)
                completed_count += 1  # Count as completed even if failed

        logging.info(
            f"All {completed_count} tasks finished. Total clips saved: {clip_saved_total_count}."
        )

    logging.info(f"Dataset generation complete. Location: {OUTPUT_DATASET_DIR}")


if __name__ == "__main__":
    main()

    # temp_clip_path = "/home/<USER>/vh-camera/kinetics400_multi_event_dataset/unknown/01d573ac-87f6-4190-a3b9-ed6c2140b6b5_0_10.mp4"
    # temp_clip_path = (
    #     "/home/<USER>/vh-camera/video_dir/0219cf83-4abf-4fc6-ae15-eecc80bad2ab.mp4"
    # )
    # llm_results = verify_clip_with_llm(
    #     temp_clip_path, LABEL_PROMPT_DESCRIPTION, CLIP_DURATION
    # )
    # breakpoint()
