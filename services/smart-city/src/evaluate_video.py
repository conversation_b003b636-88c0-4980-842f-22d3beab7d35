import base64
import numpy as np
from PIL import Image
from io import BytesIO
from openai import OpenAI
from time import time
from qwen_vl_utils import process_vision_info
# from test_unslot import run_video_unsloth
# from test_fb_model import run_video_fb
import pandas as pd
from tqdm import tqdm
import os
import json


os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)

# Set OpenAI's API key and API base to use vLLM's API server.
openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"

MODEL = "Qwen/Qwen2.5-VL-7B-Instruct-AWQ"
MODEL = 'OpenGVLab/InternVL3-1B'

client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

map_reason = {
    "Phát hiện và định danh đối tượng": "Video phải có con người đi qua lại và hiện rõ khuôn mặt chính diện trong video",
    "Phát hiện vật nuôi trái phép": "Video phải có vật nuôi đi lại",
    "Phát hiện đỗ xe trái phép": "Video phải có xe oto đang đậu ngoài lề đường",
    "Phát hiện xe chở VLXD không che chắn/phủ bạt": "Video phải có xe tải chở vật liệu xây dựng không che phủ",
    "Phát hiện người trèo tường": "Video phải có người đang hoặc đã trèo tường hoặc bờ rào",
    "Phát hiện hành vi bắt cóc": "Video phải có cảnh nghi ngờ bắt cóc người hoặc trẻ em trong không gian vắng hoặc ít người",
}

map_reason_en_reversed = {
    "The video must show people walking by with clearly visible front-facing faces.": "Phát hiện và định danh đối tượng",
    "The video must show pets moving around.": "Phát hiện vật nuôi trái phép",
    "The video must show cars parked on the roadside.": "Phát hiện đỗ xe trái phép",
    "The video must show construction material trucks without covers or tarps.": "Phát hiện xe chở VLXD không che chắn/phủ bạt",
    "The video must show a person currently climbing or having climbed over a wall or fence.": "Phát hiện người trèo tường",
    "The video must show suspected kidnapping of a person or child in a deserted or low-traffic area.": "Phát hiện hành vi bắt cóc",
}

list_reason = list(map_reason_en_reversed.keys())
list_reason.append("The video does not contain any noteworthy or detectable event.")
ignore_reason = ["Phát hiện người trong danh sách đen"]


def prepare_message_for_vllm(content_messages):
    """
    The frame extraction logic for videos in `vLLM` differs from that of `qwen_vl_utils`.
    Here, we utilize `qwen_vl_utils` to extract video frames, with the `media_typ`e of the video explicitly set to `video/jpeg`.
    By doing so, vLLM will no longer attempt to extract frames from the input base64-encoded images.
    """
    vllm_messages, fps_list = [], []
    for message in content_messages:
        message_content_list = message["content"]
        if not isinstance(message_content_list, list):
            vllm_messages.append(message)
            continue

        new_content_list = []
        for part_message in message_content_list:
            if "video" in part_message:
                video_message = [{"content": [part_message]}]
                image_inputs, video_inputs, video_kwargs = process_vision_info(
                    video_message, return_video_kwargs=True
                )
                assert video_inputs is not None, "video_inputs should not be None"
                video_input = (
                    (video_inputs.pop()).permute(0, 2, 3, 1).numpy().astype(np.uint8)
                )
                fps_list.extend(video_kwargs.get("fps", []))

                # encode image with base64
                base64_frames = []
                for frame in video_input:
                    img = Image.fromarray(frame)
                    output_buffer = BytesIO()
                    img.save(output_buffer, format="jpeg")
                    byte_data = output_buffer.getvalue()
                    base64_str = base64.b64encode(byte_data).decode("utf-8")
                    base64_frames.append(base64_str)

                part_message = {
                    "type": "video_url",
                    "video_url": {
                        "url": f"data:video/jpeg;base64,{','.join(base64_frames)}"
                    },
                }
            new_content_list.append(part_message)
        message["content"] = new_content_list
        vllm_messages.append(message)
    return vllm_messages, {"fps": fps_list}


def run_video(video_path, prompt):
    video_messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {
                    "type": "video",
                    "video": video_path,
                    "total_pixels": 20480 * 28 * 28,
                    "min_pixels": 16 * 28 * 2,
                    "fps": 1.0,  # The default value is 2.0, but for demonstration purposes, we set it to 3.0.
                },
            ],
        },
    ]

    video_messages, video_kwargs = prepare_message_for_vllm(video_messages)

    chat_response = client.chat.completions.create(
        model=MODEL,
        messages=video_messages,
        extra_body={"mm_processor_kwargs": video_kwargs},
    )
    content = chat_response.choices[0].message.content
    if "```" in content:
        json_format = content.split("```")[1]
        if "```json" in content:
            json_format = json_format[4:]
    else:
        json_format = content
    result = json.loads(json_format)
    return result


if __name__ == "__main__":
    df = pd.read_csv("event_snapshot2.csv")
    base_dir = "./video_dir"
    cache_file = f"evaluate_model_{MODEL.split('/')[-1]}.json"
    if os.path.exists(cache_file):
        with open(cache_file, "r") as f:
            exsist_result = json.load(f)
    else:
        exsist_result = {}
    result_map = {}
    df_result = df.copy()
    for idx, row in tqdm(df.iterrows(), total=len(df)):
        if row["event_name"] in ignore_reason:
            continue
        event = map_reason[row["event_name"]]
        prompt = f"""
        Video được dự đoán với event thông tin sau: {event}. Hãy coi video và đưa ra kết quả kiểm tra có xảy ra event trên hay không, nêu lý do kèm theo.
        Example json:
        ```json
        {{
            "label": 0, # 1 là đúng, 0 là sai
            "reason": "Không có xe máy trong video"
        }}
        Kết quả của bạn: 
        ```json
        """
        path_vid = f"{base_dir}/{row['id_x']}.mp4"
        if not os.path.exists(path_vid):
            print(f"video not exist at: {row['id_x']}")
            continue
        if exsist_result.get(row["id_x"]):
            result_map[row["id_x"]] = exsist_result[row["id_x"]]
            df_result.loc[idx, "event_new_model"] = int(
                result_map[row["id_x"]]["label"]
            )
            continue

        result = run_video(video_path=path_vid, prompt=prompt)
        # result = run_video_unsloth(video_path=path_vid, prompt=prompt)

        result["video_id"] = row["id_x"]
        result["original_prediction"] = row["event_name"]
        result_map[row["id_x"]] = result
        row["event_new_model"] = int(result["label"])
        df_result.loc[idx, "event_new_model"] = int(result["label"])


        # # NOTE: facebook inf
        # result_idx = run_video_fb(path_vid, list_label=list_reason)
        # if not result_idx or list_reason[result_idx] not in map_reason_en_reversed.keys():
        #     label = 0
        # else:
        #     if map_reason_en_reversed[list_reason[result_idx]] != row["event_name"]:
        #         label = 0
        #     else:
        #         label = 1
        # df_result.loc[idx, "event_new_model"] = label
            



    df_result["event_new_model"].fillna("#", inplace=True)

    df_result.to_csv(f"evaluate_model_{MODEL.split('/')[-1]}.csv")
    # with open(cache_file, "w") as f:
    #     json.dump(result_map, f)
