import asyncio
import logging
import multiprocessing
import os
import queue
import shutil
import subprocess
import threading
import time
from collections import Counter, deque
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from itertools import cycle
from pathlib import Path
from typing import Dict, List

import cv2
import numpy as np
import openvino as ov
import openvino.properties.hint as hints
import torch
from openai import OpenAI
from pymemcache.client.base import Client
from ultralytics import YOL<PERSON>

from video_verify import verify_clip_with_llm

# --- CONFIGURATION ---
os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(threadName)s] %(levelname)s: %(message)s")

# Stream sources (replace with your actual RTSP URLs or video file paths)
file_path = "./list.streams"
OUTPUT_DATASET_DIR = "./output_models"

# YOLO Configuration
YOLO_CONFIDENCE = 0.5
YOLO_TARGET_CLASSES = {  # Class IDs and names to detect
    0: "person",
    2: "car",
    7: "truck",
    15: "cat",
    16: "dog",
}
MAX_CONCURRENT_TASKS = 10
device = "cuda" if torch.cuda.is_available() else "cpu"
# Buffering & Output
BUFFER_DURATION_SECONDS = 5
OUTPUT_FRAME_RATE = 10  # FPS for the saved clip
TEMP_VIDEO_DIR = Path("./temp_clips")
TEMP_VIDEO_DIR.mkdir(exist_ok=True)

cache_client = Client("localhost")
EXPIRE_TIME = 300

# LLM API Configuration
LLM_SERVERS = [
    "http://127.0.0.1:8000/v1",
    "http://127.0.0.1:8001/v1",
    "http://127.0.0.1:8002/v1",
    # Add more server endpoints if available
]
LLM_SERVER_CYCLE = cycle(LLM_SERVERS)


LABEL_PROMPT_DESCRIPTION = {
    "human_detection_face": (
        "The video must show at least one person walking **toward the camera** "
        "with their **full face clearly visible**. The face should not be obscured "
        "by masks, sunglasses, or hats. The video should not focus on side or back views."
    ),
    "pet_detection": (
        "The video must show a **pet animal**, such as a **dog or cat**, clearly "
        "**moving around** in the frame. The animal should be the main focus of the scene. "
        "Do not include videos of wild animals, statues, or stationary pets."
    ),
    "illegal_parking_car": (
        "The video must show a **car parked improperly**, such as on the roadside, sidewalk, "
        "or in a **non-designated parking area**. The car should be **stationary**. "
        "Do not include videos of moving cars or cars parked in legal spaces."
    ),
    "uncovered_truck": (
        "The video must show a **large construction vehicle or truck** that is visibly "
        "**carrying construction materials without a cover**. The load must be **exposed**, "
        "such as gravel, sand, bricks, etc. Do not include covered trucks or empty vehicles."
    ),
    "trespassing": (
        "The video must show a person **actively climbing or having already climbed** over a "
        "**wall or fence**. The act of climbing should be visible or obviously implied. "
        "Do not include videos where the wall is only used for support while walking."
    ),
    "kidnap": "Show a actual kidnapping scence where a child is forcibly taken or dragged by someone in a quiet, low-traffic area (e.g., alley, side street). The action must be fast and subtle, reflecting the rarity of such events",
    "fall_down": "The video must clearly show a person accidentally falling (e.g., tripping, slipping, losing balance). Only one or two people should be present. Falls during sports activities are not allowed. After falling, the person must remain on the ground until the video ends.",
    "fighting": "The video must show two or more people engaging in physical fighting violently, such as punching, kicking, or struggling. The conflict should be clearly visible and should not appear as playful interactions or sports activities like martial arts training.",
    "public_urination": "The video must show a male urinating in a public place such as streets, walls. The act should be clearly implied or visible, focusing on inappropriate public behavior.",
}


# --- Queues ---
# Queue format: (stream_id: str, frame: np.ndarray)
# RAW_FRAME_QUEUE = queue.Queue(maxsize=100)  # Frames from cameras before YOLO
# Queue format: (stream_id: str, frame: np.ndarray, timestamp: float)
DETECTED_FRAME_QUEUE = queue.Queue(maxsize=100)  # Frames after YOLO detects objects


# --- MODEL LOADING ---
def load_yolo_vino():
    det_model_path = Path("./yolo11n_openvino_model//yolo11n.xml")  # Ensure this path is correct
    if not det_model_path.exists():
        raise FileNotFoundError(f"YOLOv11 OpenVINO model not found at {det_model_path}")

    core = ov.Core()
    det_ov_model = core.read_model(det_model_path)

    ov_config = {hints.performance_mode: hints.PerformanceMode.THROUGHPUT}
    device_value = "CPU"  # Or "GPU", "AUTO"
    if "GPU" in device_value or ("AUTO" in device_value and "GPU" in core.available_devices):
        ov_config = {"GPU_DISABLE_WINOGRAD_CONVOLUTION": "YES"}

    det_compiled_model = core.compile_model(det_ov_model, device_value, ov_config)

    # Load the Ultralytics YOLO model wrapper - primarily for pre/post processing convenience
    # The actual inference will use the compiled OpenVINO model if set correctly
    det_model = YOLO(det_model_path.parent, task="detect")

    custom_args = {
        "conf": YOLO_CONFIDENCE,
        "save": False,
        "mode": "predict",
        "verbose": False,
        "classes": list(YOLO_TARGET_CLASSES.keys()),
    }
    args = {**det_model.overrides, **custom_args}
    det_model.predictor = det_model._smart_load("predictor")(overrides=args, _callbacks=det_model.callbacks)
    det_model.predictor.setup_model(model=det_model.model)  # Sets up preprocessing etc.

    # Crucially, assign the compiled OV model to the predictor
    # This ensures the predictor uses our batch-ready compiled model
    det_model.predictor.model.ov_compiled_model = det_compiled_model
    det_model.predictor.model.model = det_compiled_model  # Also assign here for some Ultralytics versions

    logging.info(f"YOLOv11 OpenVINO model loaded and compiled for device {device_value}")
    return det_model


YOLO_MODEL = load_yolo_vino()


def is_stream_working(url: str, timeout=5) -> bool:
    try:
        cmd = ["ffmpeg", "-rtsp_transport", "tcp", "-i", url, "-t", str(timeout), "-f", "null", "-"]
        result = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, timeout=timeout + 2)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        return False
    except Exception:
        return False


def filter_working_streams(urls, max_workers=8):
    working_streams = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(is_stream_working, url): url for url in urls}
        for future in as_completed(futures):
            url = futures[future]
            try:
                if future.result():
                    working_streams.append(url)
            except Exception:
                continue
    return working_streams


# --- HELPER FUNCTIONS (Mostly unchanged LLM interaction) ---
def detect_objects_yolo_postprocess(video_path: str, batch_size: int = 16) -> list:
    """
    Uses YOLOv8 to detect objects in the video and returns a list of labels
    that were detected more than twice.

    Args:
        video_path (str): Path to the video file.
        batch_size (int): Number of frames to process in a batch.

    Returns:
        list: Labels detected more than twice in the video.
    """
    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logging.error(f"Failed to open video: {video_path}")
        return []

    label_counter = Counter()
    frames = []
    YOLO_BARE = YOLO("yolo11n.pt").to(device)

    YOLO_BARE.eval()

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)

        if len(frames) == batch_size:
            with torch.no_grad():
                results = YOLO_BARE(
                    frames,
                    verbose=False,
                    conf=0.5,
                    classes=list(YOLO_TARGET_CLASSES.keys()),
                )

            for result_batch in results:
                if result_batch is None:
                    continue
                for r in result_batch:
                    if r and hasattr(r, "names"):
                        labels = [r.names[int(cls)] for cls in r.boxes.cls]
                        label_counter.update(labels)

            frames = []

    # Process any remaining frames
    if frames:
        results = YOLO_BARE(
            frames,
            verbose=False,
            conf=0.5,
            classes=list(YOLO_TARGET_CLASSES.keys()),
        )

        for result_batch in results:
            if result_batch is None:
                continue
            for r in result_batch:
                if r and hasattr(r, "names"):
                    labels = [r.names[int(cls)] for cls in r.boxes.cls]
                    label_counter.update(labels)

    cap.release()

    # Filter labels that appear more than twice
    frequent_labels = [label for label, count in label_counter.items() if count > 2]

    return frequent_labels


def save_video_clip(frames: List[np.ndarray], path: Path, fps: int = OUTPUT_FRAME_RATE):
    """Saves a list of frames as an MP4 video."""
    if not frames:
        logging.warning(f"Attempted to save video clip {path} with zero frames.")
        return
    out = None
    try:
        # Assuming frames is a list of numpy arrays
        h, w = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*"avc1")
        out = cv2.VideoWriter(str(path), fourcc, fps, (w, h))

        for i, frame in enumerate(frames):
            if frame is None:
                print(f"Warning: frame {i} is None")
                continue

            if len(frame.shape) == 2:  # grayscale
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

            elif frame.shape[2] == 1:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

            # Ensure frame size matches writer size
            if frame.shape[:2] != (h, w):
                frame = cv2.resize(frame, (w, h))

            out.write(frame)
        logging.info(f"Saved video clip: {path} ({len(frames)} frames)")
    except Exception as e:
        logging.error(f"Failed to save video clip {path}: {e}", exc_info=False)
    finally:
        if out is not None and out.isOpened():
            out.release()


# --- WORKER THREADS ---
def detect_motion(prev_frame, current_frame, threshold=25):
    diff = cv2.absdiff(prev_frame, current_frame)
    gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
    _, thresh = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    motion_score = cv2.countNonZero(thresh)
    return motion_score > 10000


def is_noisy_frame(frame: np.array, threshold: int = 125):
    # Check if the frame is very noisy (basic method: check how "white" it is)
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    mean_intensity = cv2.mean(gray)[0]
    if mean_intensity > 125:
        return True
    return False


def buffer_manager_worker():
    """Manages buffers for each stream based on detected frames and triggers saving/API calls."""
    active_buffers: Dict[str, deque] = {}  # stream_id -> deque([(timestamp, frame), ...])
    buffer_start_times: Dict[str, float] = {}  # stream_id -> start_timestamp
    list_task: list[asyncio.Task] = []
    while True:
        now = time.time()
        try:
            stream_id, frame, timestamp = DETECTED_FRAME_QUEUE.get(timeout=1.0)

            if stream_id not in active_buffers:
                # Start a new buffer for this stream
                active_buffers[stream_id] = deque([(timestamp, frame.copy())], maxlen=200)
                buffer_start_times[stream_id] = timestamp
                logging.info(f"[{stream_id}] Started buffering...")
            else:
                # Append to existing buffer
                active_buffers[stream_id].append((timestamp, frame.copy()))

                # Check if buffer duration is met
                buffer_duration = timestamp - buffer_start_times[stream_id]
                if buffer_duration >= BUFFER_DURATION_SECONDS and len(active_buffers[stream_id]) > 50:
                    # Extract frames, save video, send to API
                    frames_to_save = [item[1] for item in active_buffers[stream_id]]
                    # Use current time for filename
                    clip_timestamp = int(time.time())
                    video_path = TEMP_VIDEO_DIR / f"{stream_id}_{clip_timestamp}.mp4"
                    list_task = [i for i in list_task if i.is_alive()]
                    # TODO ignore VLLM to check ram leak
                    if len(list_task) >= MAX_CONCURRENT_TASKS:
                        old_task = list_task.pop(0)
                        old_task.terminate()
                        logging.warning("Cancelled oldest task to free up memory.")

                    # Save and send in a separate thread to avoid blocking buffer management
                    ctx = multiprocessing.get_context("spawn")
                    p = ctx.Process(target=process_and_send_clip, args=(frames_to_save, video_path, stream_id, next(LLM_SERVER_CYCLE)))
                    p.start()
                    list_task.append(p)

                    # Clear the buffer for this stream
                    del active_buffers[stream_id]
                    del buffer_start_times[stream_id]
                    logging.info(f"[{stream_id}] Buffer full. Dispatched clip for saving/sending.")
                elif buffer_duration >= (BUFFER_DURATION_SECONDS * 3):
                    # Clear the buffer for this stream
                    del active_buffers[stream_id]
                    del buffer_start_times[stream_id]
                    logging.info(f"[{stream_id}] Buffer full. Dispatched clip for saving/sending.")
            del stream_id
            del frame
            del timestamp
        except queue.Empty:
            # Check for stale buffers (optional: if no new frames arrive for a long time)
            for stream_id in list(active_buffers.keys()):
                if now - buffer_start_times[stream_id] > (BUFFER_DURATION_SECONDS * 3):
                    logging.warning(f"[{stream_id}] Stale buffer detected, discarding.")
                    del active_buffers[stream_id]
                    del buffer_start_times[stream_id]
            continue
        except asyncio.TimeoutError:
            pass  # Normal case when no frame is available
        except Exception as e:
            logging.error(f"Error in buffer manager: {e}", exc_info=False)


def process_and_send_clip(frames: List[np.ndarray], video_path: Path, stream_id, server_url: str):
    """Saves the video clip and sends it to the LLM API."""
    try:
        save_video_clip(frames, video_path, fps=OUTPUT_FRAME_RATE)
        if video_path.exists():  # Ensure saving was successful before sending
            client = OpenAI(base_url=server_url, api_key="no-key")
            freq_labels = detect_objects_yolo_postprocess(video_path=str(video_path))
            if not freq_labels:
                return

            llm_results = verify_clip_with_llm(
                openai_client=client,
                clip_path=str(video_path),
                event_descriptions=LABEL_PROMPT_DESCRIPTION.copy(),
                clip_duration=10,
                list_detected_label=freq_labels,
            )
            if llm_results:
                for event_label in llm_results.keys():
                    # key = f"{stream_id}_{event_label}"
                    # if cache_client.get(key):
                    #     logging.debug(f"Already send event label for {key} -> continue")
                    #     continue
                    output_clip_dir = os.path.join(OUTPUT_DATASET_DIR, event_label)
                    if not os.path.exists(output_clip_dir):
                        os.mkdir(output_clip_dir)
                    output_clip_name = video_path.stem + "_" + event_label + ".mp4"
                    final_clip_path = os.path.join(output_clip_dir, output_clip_name)
                    shutil.copy2(video_path, final_clip_path)
                    # cache_client.set(key=key, value=1, expire=EXPIRE_TIME)

                logging.info(f"LLM results for {video_path.name}: {llm_results}")

        else:
            logging.warning(f"Skipping API call because video file was not created: {video_path}")

    except Exception as e:
        logging.error(f"Error processing/sending clip {video_path.name}: {e}", exc_info=False)
    finally:
        try:
            del frames
            # os.remove(video_path)
            # TODO ignore remove video for now to record for bigger model testing
            # logging.debug(f"Removed temporary clip: {video_path}")
        except OSError as e:
            logging.warning(f"Could not remove temporary clip {video_path}: {e}")


# --- MAIN EXECUTION ---
def main():
    """Starts all the worker threads."""
    list_streams = []
    file_stream = "./working.streams"
    if not os.path.exists(file_stream):
        with open(file_path, "r") as f:
            list_streams = [s.strip() for i, s in enumerate(f) if s.strip()]
        list_filter_streams = filter_working_streams(list_streams)
        print(f"Actual working streams: {len(list_filter_streams)} vs {len(list_streams)}")
        with open(file_stream, "w") as f:
            f.write("\n".join(list_filter_streams))

    # Start the async worker in a new thread
    worker_thread = threading.Thread(target=buffer_manager_worker, daemon=True)
    worker_thread.start()

    for result in YOLO_MODEL(file_stream, stream=True, vid_stride=3):
        stream_id = result.path  # Or use some identifier per stream
        if len(result.boxes) > 0:  # Check if any detections exist for this frame
            frame = result.orig_img
            timestamp = time.time()
            # Put (stream_id, frame, timestamp) into the detected queue
            DETECTED_FRAME_QUEUE.put((stream_id, frame, timestamp))
            logging.debug(f"[{stream_id}] Frame passed YOLO check.")

    # if stream_id not in frame_counts:
    #     frame_counts[stream_id] = 0
    #     start_times[stream_id] = time.time()

    # frame_counts[stream_id] += 1
    # elapsed = time.time() - start_times[stream_id]

    # Start YOLO inference worker thread
    # yolo_thread = threading.Thread(target=yolo_inference_worker, daemon=True, name="YOLO-Worker")
    # threads.append(yolo_thread)
    # yolo_thread.start()

    # try:
    #     # Keep main thread alive
    #     while True:
    #         # Optional: Monitor queue sizes periodically
    #         # logging.debug(f"Queue sizes: RAW={RAW_FRAME_QUEUE.qsize()}, DETECTED={DETECTED_FRAME_QUEUE.qsize()}")
    #         time.sleep(10)
    # except KeyboardInterrupt:
    #     logging.info("\n🛑 Shutting down requested by user.")
    # except Exception as e:
    #     logging.error(f"An unexpected error occurred in the main loop: {e}", exc_info=True)
    # finally:
    #     logging.info("System shutdown.")
    #     # Threads are daemonized, so they will exit when the main thread ends.
    #     # Add any cleanup logic here if needed (e.g., explicitly releasing resources).


if __name__ == "__main__":
    main()

    # temp_clip_path = (
    #     "/home/<USER>/inference/output_models/illegal_parking_car/cam_17_1745388763_illegal_parking_car.mp4"
    # )
    # temp_clip_path= "/home/<USER>/inference/output_models/pet_detection/cam_14_1745392209_pet_detection.mp4"

    # openai_client = OpenAI(
    #     base_url=LLM_SERVERS[0], api_key="no-key"
    # )

    # llm_results = verify_clip_with_llm(
    #     openai_client=openai_client, clip_path=temp_clip_path, event_descriptions=LABEL_PROMPT_DESCRIPTION.copy(), clip_duration=20
    # )
    # print(llm_results)
