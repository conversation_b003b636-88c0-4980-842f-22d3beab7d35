
# Chatbot Platform README

## Overview

This chatbot platform provides a simple interface for interacting with Large Language Models (LLMs). It supports both streaming and non-streaming LLM responses.

### 1. Prerequisites

*   Python 3.7+
*   `pip` package manager

### 2. Install Dependencies

Navigate to the root directory of the project (where `app.py`, `core.py`, etc., are located) and run:

```bash
pip install -r requirements.txt
```

### 3. Configure Environment Variables

Copy the `example.env` file to `.env` and update the values as needed.

### 4. Run the Application

To run the application, use the following command:

```bash
uvicorn app.main:app --reload
```

This will start the application and make it available at `http://localhost:8000`.

## Usage

The application provides a simple RESTful API for interacting with the chatbot. The API endpoints are:

*   `/chat/stream`: Streams the response from the LLM.

The API uses the following request format:

```json
{
  "message": "Hello, how are you?",
  "session_id": "1234567890",
  "user_id": "user1"
}
```

- The `session_id` field is optional and can be used to maintain context for the chatbot.
- The `user_id` field is optional and can be used to identify the user to apply role-based security.

## Testing URL
To curl to the /chat/stream endpoint with a JSON body, use this command:
```curl -X POST http://localhost:8000/chat/stream \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, how are you?",
    "session_id": "1234567890",
    "user_id": "user1"
  }'
```

## Deployment

To deploy the application, you can use Docker Compose.

```bash
docker-compose up -d
```

This will start the application and make it available at `http://localhost:8000`.