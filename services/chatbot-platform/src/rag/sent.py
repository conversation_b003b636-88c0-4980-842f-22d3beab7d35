from tqdm import tqdm
from typing import List, Optional
from aiohttp_retry import RetryClient
import asyncio
import logging
from src.consts import settings
from src.request_session import request_session

logger = logging.getLogger(__name__)


async def get_bge_m3_vector_async(
    list_query: List[str],
    batch_size: int = 32,
    model_url: str = settings.embedding_url,
) -> Optional[List[List[float]]]:
    """
    Get the BGE-M3 embeddings for a list of queries asynchronously.
    :param list_query: List of queries
    :param batch_size: Batch size for the requests
    :param model_url: URL of the BGE-M3 model
    :return: List of embeddings or None if there's an error
    """

    async def process_batch(session, batch: List[str]) -> List[List[float]]:
        data = {"inputs": batch}
        headers = {"Content-Type": "application/json"}

        async with session.post(model_url, json=data, headers=headers) as response:
            if response.status != 200:
                logger.exception(
                    f"HTTP error {response.status}: {await response.text()}"
                )
                return []
            return await response.json()

    async with RetryClient() as session:
        tasks = []
        for i in range(0, len(list_query), batch_size):
            batch = list_query[i : i + batch_size]
            tasks.append(asyncio.create_task(process_batch(session, batch)))

        results = await asyncio.gather(*tasks)

    combined_responses = [item for sublist in results for item in sublist]
    return combined_responses if combined_responses else None


def get_bge_m3_vector(
    list_query: list[str],
    batch_size: int = 32,
    model_url: str = settings.embedding_url,
    verbose: bool = False,
):
    """
    Get the BGE-M3 embeddings for a list of queries.
    :param list_query: List of queries
    :param batch_size: Batch size for the requests
    :param model_url: URL of the BGE-M3 model
    :return: List of embeddings
    """
    all_responses = []

    for i in tqdm(
        range(0, len(list_query), batch_size),
        desc="Fetching embeddings",
        disable=not verbose,
    ):
        batch = list_query[i : i + batch_size]
        data = {"inputs": batch}

        # Define the headers
        headers = {"Content-Type": "application/json"}

        # Make the POST request
        response = request_session.post(model_url, json=data, headers=headers)
        response.raise_for_status()

        # Collect the response
        all_responses.append(response.json())

    combined_responses = [item for sublist in all_responses for item in sublist]

    return combined_responses


# async def rerank(
#     query: str,
#     texts: List[str],
#     model_url: str = "http://127.0.0.1:7777/rerank",
#     batch_size: int = 32,
# ) -> List[Dict[str, Any]]:
#     """
#     Asynchronously reranks texts by batching requests through an aiohttp_retry.RetryClient.
#     """
#     # 1) Prepare batches
#     batches = [(i, texts[i : i + batch_size]) for i in range(0, len(texts), batch_size)]

#     async def process_batch(
#         session: RetryClient, batch: List[str], offset: int
#     ) -> List[Dict[str, Any]]:
#         payload = {"query": query, "texts": batch}
#         headers = {"Content-Type": "application/json"}

#         # use async context‐manager for the POST
#         async with session.post(model_url, json=payload, headers=headers) as resp:
#             if resp.status != 200:
#                 logging.warning(f"HTTP {resp.status}: {await resp.text()}")
#                 return []
#             data = await resp.json()
#             # adjust indices
#             for item in data:
#                 rel = item.get("index", -1)
#                 item["index"] = offset + rel
#             return data

#     all_results: List[Dict[str, Any]] = []
#     # 2) Open RetryClient in async mode
#     async with RetryClient() as session:
#         # fire off all batches in parallel
#         tasks = [process_batch(session, batch, offset) for offset, batch in batches]
#         for batch_data in await asyncio.gather(*tasks):
#             all_results.extend(batch_data)

#     # 3) Sort by score descending and return
#     all_results.sort(key=lambda x: x.get("score", -float("inf")), reverse=True)
#     return all_results


# def rerank(
#     query: str,
#     texts: List[str],
#     model_url: str = "http://127.0.0.1:7777/rerank",
#     batch_size: int = 32,
# ):
#     all_results = []
#     for i in range(0, len(texts), batch_size):
#         batch = texts[i : i + batch_size]
#         data = {"query": query, "texts": batch}

#         # Define the headers
#         headers = {"Content-Type": "application/json"}

#         # Make the POST request
#         response = requests.post(model_url, json=data, headers=headers)

#         try:
#             # Handle potential errors
#             response.raise_for_status()  # Raises an error for bad HTTP status
#         except requests.exceptions.HTTPError:
#             logger.exception(response.text)
#             return None

#         data = response.json()
#         for item in data:
#             rel = item.get("index", -1)
#             item["index"] = i + rel
#         all_results.extend(data)

#     # final sort
#     all_results.sort(key=lambda x: x.get("score", -float("inf")), reverse=True)
#     return all_results
