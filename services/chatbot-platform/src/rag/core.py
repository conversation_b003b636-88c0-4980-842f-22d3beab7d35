import ast
import asyncio
import heapq
import json
import logging
import os
import re
from collections import defaultdict
from typing import AsyncGenerator, Dict, List, Tuple, Optional, Any
from langdetect import detect
import bm25s
from qdrant_client import models, AsyncQdrantClient
from qdrant_client.models import Filter, FieldCondition, <PERSON>Value, MatchAny, Range
from src.consts import settings, LANGUAGE_MAPPING
from src.core import llm_async_client
from src.data.tree import KnowledgeTree, NodeType
from src.prompts import EXPAND_PROMPT, FILTER_PROMPT, RAG_PROMPT
from src.utils import logger_func
from unidecode import unidecode

from .index import (
    # is_fusion_collection,
    knowledge_tree,
)
from .sent import get_bge_m3_vector_async

logger = logging.getLogger(__name__)


MAX_TOKEN = 5000
MAX_RAG_ITEMS = 10
MAX_SEARCH_ITEMS = 100

BM25_QUERY_WORD_LIMIT = 8  # Renamed from limit_bm25_query
BM25_RETRIEVAL_K = 100  # k for BM25 retriever.retrieve
RRF_K = 60  # k for reciprocal rank fusion


try:
    bm25_folder_fulltext_search = settings.fulltext_search_path
    bm25_retriever = bm25s.BM25.load(bm25_folder_fulltext_search, mmap=True)
    with open(
        os.path.join(bm25_folder_fulltext_search, "mapping_content.json"), "r"
    ) as f:
        bm25_mapping_index = json.load(f)
except FileNotFoundError:
    logger.error(
        f"BM25 model or mapping file not found at {settings.fulltext_search_path}. BM25 search will be disabled."
    )
    bm25_retriever = None  # type: ignore
    bm25_mapping_index = {}  # type: ignore
except Exception as e:
    logger.error(f"Error loading BM25 model: {e}. BM25 search will be disabled.")
    bm25_retriever = None  # type: ignore
    bm25_mapping_index = {}  # type: ignore


def reciprocal_rank_fusion(lexical_results: list, neural_results: list, k: int = 60):
    """
    Combine lexical and neural search results using reciprocal rank fusion.
    :param lexical_results: List of BM25 search results
    :param neural_results: List of neural search results
    :param k: Hyperparameter for the fusion
    :return: List of fused results
    """
    scores = defaultdict(float)
    for rank, (doc, _) in enumerate(lexical_results):
        scores[doc] += 1 / (rank + 1 + k)
    for rank, (doc, _) in enumerate(neural_results):
        scores[doc] += 1 / (rank + 1 + k)
    return sorted(scores.items(), key=lambda x: x[1], reverse=True)


def convert_vietnamese_to_lower_without_accent(text):
    text_without_accent = unidecode(text)
    text_lower = text_without_accent.lower()
    return text_lower


def clean_text_for_llm(text: str) -> str:
    """Cleans text by normalizing whitespace and special characters."""
    text = " ".join(filter(None, text.split()))  # Remove extra whitespace
    text = re.sub(r"\.{2,}", ".", text)
    text = re.sub(r"\-{2,}", "-", text)
    text = re.sub(r"\={2,}", "=" * 5, text)
    return text.strip()


class RagEngine:
    """RAG Engine that handles embedding, retrieval, and answer generation"""

    def __init__(self, knowledge_tree: KnowledgeTree):
        self.knowledge_tree = knowledge_tree

    @staticmethod
    @logger_func
    def rank_points(
        *lists: List[models.ScoredPoint],
        max_items: int = 5,
        alpha: float = 0.7,
    ) -> List[models.ScoredPoint]:
        """
        Aggregate and rank ScoredPoint lists (returns top max_items):
            1) Sum scores per point ID
            2) Count occurrences across lists
            3) Compute blended score: alpha*(sum_score/max_sum) + (1-alpha)*((count-1)/(n-1))
            4) Return top max_items points sorted by final score then raw score.
        """
        total_score: Dict[str, float] = defaultdict(float)
        count: Dict[str, int] = defaultdict(int)
        id_to_point: Dict[str, models.ScoredPoint] = {}

        # Aggregate scores and counts
        for lst in lists:
            seen = set()
            for pt in lst:
                total_score[pt.id] += pt.score
                id_to_point[pt.id] = pt
                if pt.id not in seen:
                    count[pt.id] += 1
                    seen.add(pt.id)

        # Build fixed-size heap for top-k
        heap: List[Tuple[float, float, str]] = []  # (final_score, total_score, pid)
        max_sum = max(total_score.values(), default=1.0)
        n = len(lists) or 1

        for pid, S in total_score.items():
            m = count[pid]
            final = alpha * (S / max_sum) + (1 - alpha) * ((m - 1) / max(n - 1, 1))
            entry = (final, S, pid)
            if len(heap) < max_items:
                heapq.heappush(heap, entry)
            else:
                heapq.heappushpop(heap, entry)

        # Extract and sort results
        top = heapq.nlargest(max_items, heap)
        return [id_to_point[pid] for _, _, pid in top]

    @staticmethod
    @logger_func
    def extract_ref_urls(filtered_points: List[models.ScoredPoint]) -> List[str]:
        """Extracts unique URLs from a list of ScoredPoint objects."""
        ref_urls = set()
        for p in filtered_points:
            url = p.payload and p.payload.get("url_reference")
            if url:
                ref_urls.add(url)
        return list(ref_urls)

    @logger_func
    def construct_context_with_chunk(self, node: models.ScoredPoint):
        """Constructs a passage from a node, including its children's content."""
        payload = node.payload or {}
        node_id = node.id
        node_type = payload.get("type", NodeType.NOT_ELEMENTARY)

        target_node_idx = node_id
        if node_type != NodeType.ELEMENTARY:
            elementary_parent = self.knowledge_tree.nearest_elementary(node_id)
            if (
                elementary_parent and elementary_parent.idx != 0
            ):  # Assuming 0 is a root/invalid elementary
                target_node_idx = elementary_parent.idx

        chunk_texts = self.knowledge_tree.get_children_contents(target_node_idx)

        passage_parts = []
        for chunk_text in chunk_texts:
            cleaned_chunk = clean_text_for_llm(chunk_text)
            if cleaned_chunk:  # Only add non-empty chunks
                passage_parts.append(f"<passage>\n{cleaned_chunk}\n</passage>")

        return "\n".join(passage_parts)

    @staticmethod
    def construct_qdrant_filter(json_string: str) -> Filter:
        from datetime import datetime, timezone, timedelta

        """
        Construct a Qdrant Filter object from a JSON string.

        Args:
            json_string (str): JSON string representing filter conditions

        Returns:
            Filter: Qdrant Filter object

        Note: 'metadata.post_date' fields are automatically converted from DD/MM/YYYY format

        Example JSON format:
        {
            "must": [
                {"key": "color", "match": {"value": "red"}},
                {"key": "price", "range": {"gte": 100, "lt": 500}}
            ],
            "should": [
                {"key": "category", "match": {"any": ["electronics", "gadgets"]}}
            ],
            "must_not": [
                {"key": "status", "match": {"value": "discontinued"}}
            ]
        }
        """

        def local7_to_rfc3339(date_str: str) -> str:
            """Convert DD/MM/YYYY format to RFC3339 with UTC+7 timezone conversion."""
            # Parse as naive datetime
            dt_naive = datetime.strptime(date_str, "%d/%m/%Y")

            # Declare the UTC+7 timezone and make it aware
            tz_utc7 = timezone(timedelta(hours=7))
            dt_local = dt_naive.replace(tzinfo=tz_utc7)

            # Convert to UTC
            dt_utc = dt_local.astimezone(timezone.utc)

            # Format as RFC 3339 with 'Z' suffix (no microseconds)
            return dt_utc.replace(microsecond=0).isoformat().replace("+00:00", "Z")

        def parse_datetime_value(value, field_key):
            """Parse datetime string to timestamp or return original value."""
            if not isinstance(value, str):
                return value

            # Check if this is metadata.post_date field for DD/MM/YYYY format conversion
            if field_key == "metadata.post_date":
                try:
                    # Try DD/MM/YYYY format first (for local7_to_rfc3339)
                    datetime.strptime(value, "%d/%m/%Y")
                    rfc3339_str = local7_to_rfc3339(value)
                    dt = datetime.strptime(rfc3339_str, "%Y-%m-%dT%H:%M:%SZ")
                    return dt
                except ValueError:
                    pass

            # Try to parse as standard ISO datetime formats
            for fmt in [
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d",
            ]:
                try:
                    dt = datetime.strptime(value, fmt)
                    return dt
                except ValueError:
                    continue

            return value

        def build_condition(condition_dict: Dict[str, Any]) -> FieldCondition:
            """Build a FieldCondition from a condition dictionary."""
            if "key" not in condition_dict:
                raise ValueError("Each condition must have a 'key' field")

            key = condition_dict["key"]

            # Handle match conditions
            if "match" in condition_dict:
                match_dict = condition_dict["match"]

                if "value" in match_dict:
                    # Single value match
                    value = parse_datetime_value(match_dict["value"], key)
                    return FieldCondition(key=key, match=MatchValue(value=value))
                elif "any" in match_dict:
                    # Match any of multiple values
                    values = [parse_datetime_value(v, key) for v in match_dict["any"]]
                    return FieldCondition(key=key, match=MatchAny(any=values))
                elif "range" in match_dict:
                    # Handle range inside match (as in your example)
                    range_dict = match_dict["range"]
                    range_params = {}

                    # Map range parameters and parse datetime values
                    for param in ["gte", "gt", "lte", "lt"]:
                        if param in range_dict and range_dict[param] is not None:
                            range_params[param] = parse_datetime_value(
                                range_dict[param], key
                            )

                    if not range_params:
                        raise ValueError(
                            "Range condition must have at least one non-null range parameter (gte, gt, lte, lt)"
                        )

                    return FieldCondition(key=key, range=Range(**range_params))
                else:
                    raise ValueError(
                        "Match condition must have 'value', 'any', or 'range' field"
                    )

            # Handle range conditions (direct range, not inside match)
            elif "range" in condition_dict:
                range_dict = condition_dict["range"]
                range_params = {}

                # Map range parameters and parse datetime values
                for param in ["gte", "gt", "lte", "lt"]:
                    if param in range_dict and range_dict[param] is not None:
                        range_params[param] = parse_datetime_value(
                            range_dict[param], key
                        )

                if not range_params:
                    raise ValueError(
                        "Range condition must have at least one non-null range parameter (gte, gt, lte, lt)"
                    )

                return FieldCondition(key=key, range=Range(**range_params))
            else:
                raise ValueError("Condition must have either 'match' or 'range' field")

        try:
            filter_dict = json.loads(json_string)

            # Build conditions for each type
            must_conditions = [
                build_condition(cond) for cond in filter_dict.get("must", [])
            ]
            should_conditions = [
                build_condition(cond) for cond in filter_dict.get("should", [])
            ]
            must_not_conditions = [
                build_condition(cond) for cond in filter_dict.get("must_not", [])
            ]

            return Filter(
                must=must_conditions if must_conditions else None,
                should=should_conditions if should_conditions else None,
                must_not=must_not_conditions if must_not_conditions else None,
            )

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON string: {e}")
        except Exception as e:
            raise ValueError(f"Error constructing filter: {e}")

    async def _get_semantic_search_results_async(
        self, query_vector: List[float], filters: Dict, max_search_items: int
    ) -> List[models.ScoredPoint]:
        """Performs semantic search using Qdrant."""
        # TODO: Add FAQs search
        search_requests = [
            models.SearchRequest(
                vector=models.NamedVector(name="content", vector=query_vector),
                filter=filters,
                limit=max_search_items,
                with_payload=True,
            )
        ]
        client_async_qdrant = AsyncQdrantClient(url=settings.qdrant_url)
        responses = await client_async_qdrant.search_batch(
            collection_name=settings.qdrant_collection,
            requests=search_requests,
            timeout=6,
        )
        return responses[0] if responses else []

    def _get_bm25_search_results(
        self, query: str, max_items: int = MAX_RAG_ITEMS
    ) -> List[models.ScoredPoint]:
        """Performs BM25 search using BM25 model."""
        query = convert_vietnamese_to_lower_without_accent(query)
        query_tokens = bm25s.tokenize(query)
        results, scores = bm25_retriever.retrieve(query_tokens, k=BM25_RETRIEVAL_K)
        top_point_bm25 = []
        exist_idx = set()
        for i in range(results.shape[1]):
            doc, score = results[0, i], scores[0, i]
            idx = bm25_mapping_index[str(doc)]
            if idx in exist_idx:
                continue
            top_point_bm25.append((idx, score))
            exist_idx.add(idx)
            if len(exist_idx) == max_items:
                break
        return top_point_bm25

    @logger_func
    async def get_similar_fusion_contexts_async(
        self,
        query: str,
        filters: Optional[Dict] = None,
        max_items: int = MAX_RAG_ITEMS,
        max_search_items: int = MAX_SEARCH_ITEMS,
    ) -> List[models.ScoredPoint]:
        """Get semantically similar contexts from multiple vector indices"""
        # Encode query once
        query_emb = await get_bge_m3_vector_async([query])
        if not query_emb:
            return []
        query_vector = query_emb[0]

        semantic_responses = await self._get_semantic_search_results_async(
            query_vector, filters, max_search_items
        )
        top_point_semantic = [(i.id, i.score) for i in semantic_responses]

        if len(query.split()) < BM25_QUERY_WORD_LIMIT:
            top_point_bm25 = self._get_bm25_search_results(query, max_search_items)
            top_points = reciprocal_rank_fusion(
                top_point_semantic, top_point_bm25, k=RRF_K
            )
        else:
            top_points = top_point_semantic
        client_async_qdrant = AsyncQdrantClient(url=settings.qdrant_url)
        points = await client_async_qdrant.retrieve(
            collection_name=settings.qdrant_collection,
            ids=[i[0] for i in top_points[:max_items]],
            with_payload=True,
        )
        return points

    @logger_func
    async def filter_relevant_context_async(
        self, context: str, query: str, debug=False
    ) -> bool:
        """Filter context for relevance to query"""
        if debug:
            return True

        messages = [
            {"role": "system", "content": FILTER_PROMPT},
            {
                "role": "user",
                "content": f"\n**Context Passage:**\n{context}\n\n**Question:**\n{query}",
            },
        ]

        response = await llm_async_client.chat.completions.create(
            model=settings.openai_small_model,
            messages=messages,
            temperature=0.1,
            max_tokens=20,
        )

        # Parse decision (1=relevant, 0=not relevant)
        decision = int(ast.literal_eval(response.choices[0].message.content))
        return bool(decision)

    async def expand_query_async(self, query: str) -> List[str]:
        """ """
        messages = [
            {"role": "system", "content": EXPAND_PROMPT},
            {"role": "user", "content": f"\n#Question: {query}"},
        ]
        resp = await llm_async_client.chat.completions.create(
            model=settings.openai_small_model,
            messages=messages,
            temperature=0.03,
            max_tokens=500,
        )
        # Expect something like: '["What is X?","How does Y relate to Z?"]'
        text = resp.choices[0].message.content
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            # fallback if model spits out Python list literal
            return ast.literal_eval(text)

    def _get_filter_conds(self, filters: str) -> Dict:
        """Parse filters from user query and return them as a dictionary."""
        filters = (
            filters.replace("metadata.venue", "metadata.destination")
            .replace("metadata.time", "metadata.post_date")
            .replace("metadata.type", "metadata.website_type")
        )
        logger.debug(f"Filters STRING: {filters}")
        try:
            filter_dict: dict = self.construct_qdrant_filter(filters)
        except Exception as e:
            filter_dict = None
            logger.error(f"Error parsing filters: {e}. Skipping filters.")
        logger.info(f"Filters: {filter_dict}")
        return filter_dict

    @logger_func
    async def answer_query_async(
        self,
        query: str,
        filters: Optional[str] = None,
        has_multiple_requirements: bool = False,
    ) -> AsyncGenerator[str, None]:
        """Generate streaming answer to query using RAG"""

        if filters:
            filter_conds: dict = await asyncio.to_thread(
                self._get_filter_conds, filters
            )
        else:
            filter_conds = None

        if has_multiple_requirements:
            logger.debug(
                f"THE QUESTION SHOULD BE DECOMPOSED: {has_multiple_requirements}"
            )
            sub_queries = await self.expand_query_async(query)
            get_fusion_points_tasks = [
                self.get_similar_fusion_contexts_async(
                    q, filter_conds, MAX_RAG_ITEMS, MAX_SEARCH_ITEMS
                )
                for q in sub_queries
            ]
            list_fusion_points = await asyncio.gather(*get_fusion_points_tasks)
            # flatten & sort
            ranked_points = [p for pts in list_fusion_points for p in pts]
        else:
            ranked_points = await self.get_similar_fusion_contexts_async(
                query, filter_conds, MAX_RAG_ITEMS, MAX_SEARCH_ITEMS
            )

        # Construct contexts
        contexts = [self.construct_context_with_chunk(p) for p in ranked_points]
        logger.debug(f"Passages built: {len(contexts)}")
        logger.debug(f"Contexts: {contexts}")
        if contexts:
            # Filter relevant contexts
            point_filter_tasks = [
                self.filter_relevant_context_async(
                    ctx, query, False
                )  # NOTE: True for skipping context filtering
                for ctx in contexts
            ]
            decisions = await asyncio.gather(*point_filter_tasks)

            # keep only the contexts & points LLM marked relevant
            filtered_contexts = [ctx for ctx, keep in zip(contexts, decisions) if keep]
            filtered_points = [p for p, keep in zip(ranked_points, decisions) if keep]

            # Build final context
            final_context = (
                "\n".join(filtered_contexts)
                if filtered_contexts
                else "Không có thông tin nào liên quan đến câu hỏi."
            )
            final_context = clean_text_for_llm(final_context)

            logger.debug(f"Decisions: {decisions}")
            logger.debug(f"filtered_contexts: {filtered_contexts}")
            logger.debug(f"Filtered context length: {len(filtered_contexts)}")
        else:
            final_context = (
                "Không có thông tin về vấn đề này trong dữ liệu được cung cấp."
            )
            logger.warning(f"No context found for query: {query}")
        logger.info(f"final_context: {final_context}")
        # prepare LLM prompt & urls
        try:
            response_language = LANGUAGE_MAPPING.get(detect(query), "Vietnamese")
        except Exception as e:
            logger.error(f"Error detecting language: {e}")
            response_language = "Vietnamese"

        system_prompt = f"""{RAG_PROMPT}
        **CONTEXT PASSAGES:**
        {final_context}
        """
        user_msg = f"""**Your response language:** {response_language}
        **USER'S QUESTION:** {query}
        """

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_msg},
        ]
        logger.debug(f"messages: {messages}")

        # Generate streaming response
        stream = await llm_async_client.chat.completions.create(
            model=settings.openai_model_name,
            messages=messages,
            temperature=0.05,
            max_tokens=MAX_TOKEN,
            stream=True,
        )

        if contexts and (ref_urls := self.extract_ref_urls(filtered_points)):
            stream.ref_urls = ref_urls
        return stream


rag_engine = RagEngine(knowledge_tree)
