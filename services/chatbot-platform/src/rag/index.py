import logging
import json
from typing import List
from tqdm import tqdm
from src.data.tree import KnowledgeTree
from qdrant_client import QdrantClient, AsyncQdrantClient
from qdrant_client import models
from src.consts import settings
from collections import defaultdict

logger = logging.getLogger(__name__)

try:
    # Load knowledge_tree from JSON file
    with open(settings.dataset_path, "r") as f:
        data = json.load(f)
    knowledge_tree = KnowledgeTree.from_dict(data)
    node_url = defaultdict(list)
    for node in knowledge_tree.nodes:
        node_url[node.url_reference].append(node.idx)
except Exception as e:
    logger.warning(f"Knowledge tree was not initialized: {e}")
    pass

# Initialize Qdrant client
client_qdrant = QdrantClient(url=settings.qdrant_url)
client_async_qdrant = AsyncQdrantClient(url=settings.qdrant_url)

# Check the collection contains FAQs vectors or not
# Since we're using the same collection for many projects and they might have not have FAQs data
# we need to check if the collection has FAQs vectors or not
# Terminology: `is_fusion_collection` means the collection has three vectors: content, question, answer
is_fusion_collection = False
if client_qdrant.collection_exists(collection_name=settings.qdrant_collection):
    is_fusion_collection = (
        "answer"
        in client_qdrant.get_collection(
            collection_name=settings.qdrant_collection
        ).config.params.vectors
        and "question"
        in client_qdrant.get_collection(
            collection_name=settings.qdrant_collection
        ).config.params.vectors
    )

logger.info(f"COLLECTION IS FUSION: {is_fusion_collection}")


def create_collection(
    collection_name: str = settings.qdrant_collection, vector_dim: int = 1024
):
    if not client_qdrant.collection_exists(collection_name=collection_name):
        client_qdrant.create_collection(
            collection_name=collection_name,
            vectors_config={
                "content": models.VectorParams(
                    size=vector_dim,
                    distance=models.Distance.COSINE,
                ),
            },
        )
        logger.info(f"Created collection {collection_name}")
    else:
        logger.info(f"Collection {collection_name} already exists")


def create_fusion_collection(
    collection_name: str = settings.qdrant_collection, vector_dim: int = 1024
):
    # Create content collection
    if not client_qdrant.collection_exists(collection_name=collection_name):
        client_qdrant.create_collection(
            collection_name=collection_name,
            vectors_config={
                "content": models.VectorParams(
                    size=vector_dim,
                    distance=models.Distance.COSINE,
                ),
                "question": models.VectorParams(
                    size=vector_dim,
                    distance=models.Distance.COSINE,
                    multivector_config=models.MultiVectorConfig(
                        comparator=models.MultiVectorComparator.MAX_SIM
                    ),
                ),
                "answer": models.VectorParams(
                    size=vector_dim,
                    distance=models.Distance.COSINE,
                    multivector_config=models.MultiVectorConfig(
                        comparator=models.MultiVectorComparator.MAX_SIM
                    ),
                ),
            },
        )
        logger.info(f"Created collection {collection_name}")
    else:
        logger.info(f"Collection {collection_name} already exists")


def create_points(knowledge_tree: KnowledgeTree):
    list_points = []

    for node in tqdm(knowledge_tree.nodes, desc="Preparing points"):
        vector = {}
        if node.embedding:
            vector["content"] = node.embedding
        if node.faqs_embedding:
            vector["question"] = node.faqs_embedding["questions"]
            vector["answer"] = node.faqs_embedding["answers"]

        if not vector:
            logger.warning(f"Node {node.idx} has no embedding. Skipping.")
            continue

        from datetime import datetime, timezone, timedelta

        def local7_to_rfc3339(date_str: str) -> str:
            # 1) parse as naive datetime
            dt_naive = datetime.strptime(date_str, "%d/%m/%Y")

            # 2) declare the UTC+7 timezone and make it aware
            tz_utc7 = timezone(timedelta(hours=7))
            dt_local = dt_naive.replace(tzinfo=tz_utc7)

            # 3) convert to UTC
            dt_utc = dt_local.astimezone(timezone.utc)

            # format as RFC 3339 with 'Z' suffix (no microseconds)
            return dt_utc.replace(microsecond=0).isoformat().replace("+00:00", "Z")

        # logger.info(f"node.metadata: {node.metadata}")
        if node.metadata.get("post_date", None):
            try:
                node.metadata["post_date"] = local7_to_rfc3339(
                    node.metadata["post_date"]
                )
                logger.info(
                    f"Node {node.idx} has post_date: {node.metadata['post_date']}"
                )
            except Exception:
                logger.warning(
                    f"Node {node.idx} has invalid post_date: {node.metadata['post_date']}"
                )

        list_points.append(
            models.PointStruct(
                id=node.idx,
                vector=vector,
                payload={
                    "idx": node.idx,
                    "name": node.name,
                    "parent_idx": node.parent_idx,
                    "type": node.type,
                    "text": node.text,
                    "text_path": node.text_path,
                    "summary": node.summary,
                    "summary_path": node.summary_path,
                    "contextual": node.contextual,
                    "children": node.children,
                    "faqs": node.faqs,
                    "url_reference": node.url_reference,
                    "is_leaf": node.is_leaf,
                    "metadata": node.metadata,
                },
            )
        )

    return list_points


def upsert_points(
    points: List[models.PointStruct],
    client: QdrantClient,
    collection_name: str = settings.qdrant_collection,
    batch_size: int = 64,
    verbose: bool = False,
):
    for i in tqdm(
        range(0, len(points), batch_size),
        desc="Upserting points",
        disable=not verbose,
    ):
        batch = points[i : i + batch_size]
        client.upsert(
            collection_name=collection_name,
            points=batch,
        )
