from textwrap import dedent
from omegaconf import OmegaConf
from .consts import settings

if not settings.prompt_file:
    raise ValueError("PROMPT_FILE environment variable is not set")

try:
    prompts = OmegaConf.load(settings.prompt_file)
except Exception as e:
    raise ValueError(f"Error loading prompt file: {e}")


CONDENSE_PROMPT = dedent(prompts.condense_prompt)
SYSTEM_PROMPT = dedent(prompts.system_prompt)
RAG_PROMPT = dedent(prompts.rag_prompt)
EXPAND_PROMPT = dedent(prompts.expand_prompt)
FILTER_PROMPT = dedent(prompts.filter_prompt)
