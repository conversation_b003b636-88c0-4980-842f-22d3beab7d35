import logging
from typing import Any, Dict, List

from app.db.session import get_chat_history
from app.schemas import Chat<PERSON><PERSON><PERSON>
from openai import Async<PERSON><PERSON>A<PERSON>
from sqlalchemy.orm import Session
from datetime import datetime
from .consts import settings, MAX_HISTORY_LENGTH
from .prompts import CONDENSE_PROMPT, SYSTEM_PROMPT
from .utils import ThreadWithReturnValue, preprocess_message

logger = logging.getLogger(__name__)


llm_async_client = AsyncOpenAI(
    api_key=settings.openai_api_key,
    base_url=settings.openai_endpoint,
)


def prepare_messages(request: ChatRequest, db: Session) -> List[Dict[str, Any]]:
    chat_history = get_chat_history(request.session_id, db)
    if not chat_history:
        chat_history = [
            {
                "role": "system",
                "content": SYSTEM_PROMPT.format(
                    today=datetime.now().strftime("%dd/%mm/%yyyy")
                ),
            }
        ]
        logger.debug(f"Chat history is empty, using system prompt: {SYSTEM_PROMPT}")
    message = preprocess_message(request.message)
    chat_history.append(
        {
            "role": "user",
            "content": message,
        }
    )
    return chat_history


def fitler_most_recent_message(chat_history: list[dict]):
    filter_chat_hist = []
    count = 0
    for i in chat_history[::-1]:
        if i["role"] == "system":
            continue
        if i["role"] == "user" and i.get("content"):
            count += 1
        filter_chat_hist.append(i)
        if count >= MAX_HISTORY_LENGTH:
            break
    filter_chat_hist = filter_chat_hist[::-1]
    filter_chat_hist.insert(0, {"role": "system", "content": SYSTEM_PROMPT})
    return filter_chat_hist


async def rewrite_query_message(message: str, list_chat_hist: list[dict]):
    chat_history = [
        i_hist
        for i_hist in list_chat_hist[:-1]  # Ignore the last message
        if i_hist["role"] in ["user", "system"] and i_hist.get("content")
    ]

    resp = await llm_async_client.chat.completions.create(
        model=settings.openai_small_model,
        messages=[
            {
                "role": "system",
                "content": CONDENSE_PROMPT,
            },
            {
                "role": "user",
                "content": f"\n\nChat History:\n{chat_history}\nFollow Up Input: {message}\nStandalone question: ",
            },
        ],
        temperature=0.1,
        max_tokens=1000,
    )
    return resp.choices[0].message.content.strip()
