"""
Configuration settings for the Trip Planner Agent Demo
"""
import os

# Data Configuration
DATA_FOLDER = os.getenv("planning_path", "data")
DATA_PATH = os.path.join(DATA_FOLDER, "planning.json")
DEFAULT_LOCATION = "VinWonders Phú Quốc"

# Agent Configuration
ALLOWED_ACTIVITY_TYPES = {"Nhà hàng", "Cảnh quan", "Trò chơi", "Sự kiện"}
DEFAULT_TYPE_PLAN = "full_plan"
DEFAULT_DAY_OF_WEEK = "Thứ 2"
MEMORY_TURNS = 3

# Function Call Specifications for OpenAI
FUNCTION_SPECS = [
    {
        "name": "select_activities",
        "description": "Choose candidate activities for each type.",
        "parameters": {
            "type": "object",
            "properties": {"top_k": {"type": "integer", "default": 10}},
            "required": [],
        },
    },
    {
        "name": "plan_trip",
        "description": "Arrange selected activities into an optimized timeline using chosen activities and distance constraints.",
        "parameters": {"type": "object", "properties": {}},
    },
    {
        "name": "finish",
        "description": "Signal that planning is done and present the final plan.",
        "parameters": {"type": "object", "properties": {}},
    },
]

# System Prompt Template #Coordinate
SYSTEM_PROMPT_TEMPLATE = """
You are an autonomous **CoordinatorAgent**.
Your job: deliver a concrete, optimized itinerary for a visitor to **{location}**.

### How you reason
* Decide what to do next **silently** by invoking one of the *function calls* below.
* Do **NOT** ask the user follow-up questions unless the request is truly ambiguous.
* When information is missing, assume *reasonable defaults* instead of asking:
  • Trip type → "day trip" (no overnight)
  • Visit window → 09:00-18:00 if user gives none
  • Party → only healthy adults unless stated otherwise
* Always move forward toward producing a full plan.
* If select_activities returns {{"status":"empty"}}, call finish immediately
and tell the user that no activity data is available.

### Available function calls
1. `select_activities(top_k=10)` choose candidate activities for each activity type.
2. `plan_trip()` arrange the selected activities into an optimized timeline.
3. `finish()` return the final answer; use only after you have a complete plan.

> **Respond with a JSON *function call* whenever you need to call a function.**
> After you are satisfied with the plan, respond normally with the finished itinerary.
"""
