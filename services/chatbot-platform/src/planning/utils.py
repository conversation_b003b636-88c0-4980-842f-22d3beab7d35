import re
from .activity import Activities, ActivityDetail
from .models import ActivityResponse
from typing import List, Dict, Tuple, Any, Optional
from pydantic import BaseModel, Field
from textwrap import dedent
import datetime

weekday_map = {
    0: "Thứ 2",
    1: "Thứ 3",
    2: "Thứ 4",
    3: "Thứ 5",
    4: "Thứ 6",
    5: "Thứ 7",
    6: "Chủ Nhật",
}


def slugify(text: str) -> str:
    """'Tr<PERSON> chơi' → 'tro_choi_agent' (tên agent hợp lệ)."""
    text = re.sub(r"[^\w\s-]", "", text.lower().strip())
    return re.sub(r"\s+", "_", text) + "_agent"


def to_int(x):
    """None, '' hay '15' → int; lỗi → 0"""
    try:
        return 0 if x in (None, "") else int(x)
    except (TypeError, ValueError):
        return 0


def get_start(time_range: str) -> str:
    """Extract start time from time range string like '08:00 - 09:00'"""
    if not time_range:
        return ""
    return (
        time_range.split(" - ")[0].strip()
        if " - " in time_range
        else time_range.strip()
    )


class PlanIDs(BaseModel):
    activity_ids: List[str] = Field(description="List of selected activity IDs")

    class Config:
        json_json_schema_extra = {
            "example": {
                "activity_ids": ["act1", "act2"],
            }
        }


def format_plan_as_list(
    activities: Activities, plan_payload: Dict
) -> List[Dict[str, str]]:
    """
    Convert the trip-plan into a list of dictionaries.
    Each entry contains key fields for display or JSON export.
    """
    day_plan = plan_payload.get("plan", [])
    if not day_plan:
        return [{"error": "Không có hoạt động nào được lập kế hoạch"}]

    output: List[Dict[str, str]] = []

    for idx, item in enumerate(day_plan, start=1):
        act = activities.get_activity_by_id(item.activity_id)
        if act is None:
            continue

        time_range = (
            f"{item.start_time} - {item.end_time}"
            if item.start_time and item.end_time
            else str(item.duration)
        )

        output.append(
            {
                "STT": str(idx),
                "POI": act.a_names.get("Tiếng Việt", ""),
                "Thời gian chơi": time_range,
                "Phân khu": item.zone or "",
                "Loại POI": item.type or "",
                "Ghi chú": act.a_descriptions.get("Tiếng Việt", ""),
            }
        )

    return output


def format_plan_text(
    activities: Activities, plan_payload: Dict, user_query: str
) -> str:
    """
    Turn the trip-plan JSON into a simplified Vietnamese fixed-width table.
    Only includes: Activity ID, Time Range, Zone.
    """
    day_plan = plan_payload
    if not day_plan:
        return "(Không có hoạt động nào được lập kế hoạch)"

    lines: List[str] = []
    lines.append(f"Yêu cầu người dùng: {user_query}")
    lines.append(f"{'Thời gian chơi':<20} | {'Phân khu':<20} | {'Activity ID'}")
    lines.append("-" * 70)

    for item in day_plan:
        act: ActivityDetail | None = activities.get_activity_by_id(item.activity_id)
        if act is None:
            continue

        time_range = (
            f"{item.start_time} - {item.end_time}"
            if item.start_time and item.end_time
            else str(item.duration)
        )

        zone = item.zone or act.a_zone or ""

        lines.append(f"{time_range:<20} | {zone:<20} | {item.activity_id}")

    return "\n".join(lines)


def remove_planned_activities_from_payload(
    payload: List[dict], plan: List[ActivityResponse]
) -> List[dict]:
    """
    Removes activities in plan from payload by matching activity_id.

    :param payload: List of activity dicts (original input).
    :param plan: List of ActivityResponse (already planned activities).
    :return: Filtered payload list excluding planned activities.
    """
    # Collect all activity IDs that were already planned
    planned_ids = {act.activity_id for act in plan}

    # Filter payload to exclude these IDs
    filtered_payload = [act for act in payload if act["activity_id"] not in planned_ids]

    return filtered_payload


def compute_zone_distance_matrix(
    activities, site_name: str = "VinWonders Phú Quốc"
) -> Tuple[Dict[str, Dict[str, float]], List[str]]:
    """
    Compute zone-to-zone distance matrix for a given site based on average coordinates.

    Args:
        activities: An object with `.get_activity_by_site(site)` and `.get_activity_by_zone(zone)` methods.
        site_name: The name of the site to extract zones from.

    Returns:
        distance_matrix: Dict mapping zone1 -> zone2 -> distance
        zones_list: Ordered list of unique zones used in the matrix
    """

    # Get unique zones
    unique_zones = set(
        act.a_zone
        for act in activities.get_activity_by_site(site_name)
        if act.a_zone is not None
    )

    # Calculate average coordinates for each zone
    zone_centers = {}
    for zone in unique_zones:
        zone_activities = activities.get_activity_by_zone(zone).activities
        valid_coords = [
            (act.a_location_latitude, act.a_location_longitude)
            for act in zone_activities
            if act.a_location_latitude is not None
            and act.a_location_longitude is not None
        ]
        if valid_coords:
            avg_lat = sum(lat for lat, _ in valid_coords) / len(valid_coords)
            avg_lon = sum(lon for _, lon in valid_coords) / len(valid_coords)
            zone_centers[zone] = (avg_lat, avg_lon)

    # Calculate distance matrix
    distance_matrix = {}
    zones_list = list(zone_centers.keys())

    for i, zone1 in enumerate(zones_list):
        distance_matrix[zone1] = {}
        for j, zone2 in enumerate(zones_list):
            if zone1 == zone2:
                distance_matrix[zone1][zone2] = 0.0
            else:
                lat1, lon1 = zone_centers[zone1]
                lat2, lon2 = zone_centers[zone2]
                distance = ((lat2 - lat1) ** 2 + (lon2 - lon1) ** 2) ** 0.5
                distance_matrix[zone1][zone2] = distance

    return distance_matrix, zones_list


def build_knowledge_distance(distance_matrix, zones_list) -> str:
    """Return a nicely-formatted distance table as one string."""
    lines = []
    for i, zone1 in enumerate(zones_list):
        lines.append(f"{zone1}:")
        for j, zone2 in enumerate(zones_list):
            if i < j:  # only upper-triangle
                dist = distance_matrix[zone1][zone2]
                lines.append(f"  to {zone2}: {dist:.6f}")
        if i < len(zones_list) - 1:  # blank line between blocks
            lines.append("")
    return "\n".join(lines)


def compute_poi(act: ActivityDetail) -> int:
    """
    Sum duration and travel time properties to calculate POI.
    """
    return (
        to_int(act.a_detail_property_duration_overall)
        + to_int(act.a_plan_property_waiting_time)
        + to_int(act.a_plan_property_travel_time)
        + to_int(act.a_plan_property_travel_time_after)
        + 3
    )


def prepare_activity_payload_for_provider(
    activities: List[ActivityDetail], day_of_week: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Convert activity objects into compact dicts for LLM prompts.
    """
    payload: List[Dict[str, Any]] = []
    for a in activities:
        today_weekday = datetime.datetime.today().weekday()
        vietnamese_day = weekday_map[today_weekday]
        time_range = (
            a.a_detail_property_time_range.get(day_of_week, "")
            if day_of_week
            else a.a_detail_property_time_range.get(vietnamese_day, "")
        )
        payload.append(
            {
                "activity_id": a.a_id,
                "name": a.a_names.get("Tiếng Việt", ""),
                "type": a.a_type,
                "a_zone": a.a_zone,
                "a_name": a.a_names.get("Tiếng Việt", ""),
                "suggested": getattr(a, "a_plan_property_suggested", False),
                "a_description": a.a_descriptions.get("Tiếng Việt", ""),
                "level": a.a_detail_property_level,
                "status": a.a_detail_property_status,
                "age_regulation": a.a_detail_property_age_regulation,
                "time_range_available": time_range,
                # "poi": getattr(a, 'a_poi', 60),  # Default 60 minutes
                "poi": compute_poi(a),
            }
        )
    return payload


def prepare_activity_payload_for_trip(
    activities: List[ActivityDetail], day_of_week: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Convert activity objects into compact dicts for LLM prompts.
    """
    payload: List[Dict[str, Any]] = []
    for a in activities:
        time_range = (
            a.a_detail_property_time_range.get(day_of_week, "") if day_of_week else ""
        )
        payload.append(
            {
                "activity_id": a.a_id,
                "activity_type": a.a_type,
                "score": 1 if a.a_plan_property_suggested == "true" else 0,
                "a_zone": a.a_zone,
                "time_range_available": time_range,
                "Expected Time (min)": compute_poi(a),
            }
        )
    return payload


def build_activity_prompt_without_planning(
    activity_type: str,
    activities: Activities,
    user_query: str,
) -> str:
    return dedent(
        f"""
You are an knowledgable AI assistant about Vingroup's hospitality services. Your task is to answer the user's question about hospitality-related services based on the provided activities.

## ACTIVITIES (**{activity_type}**)
{activities}

## USER REQUIREMENTS:
{user_query}

## ANSWER RULES
1. answer in user's question language.
2. From the provided list, analyze the user's requirements and suggest the most suitable activities which satisfy the user's requirements. If there is some contradiction with the user's requirements, let's state it in the answer and explain to the user what is the problem.
3. prioritize activities with `suggested == True` first, then the rest.
4. If no activities match the user's requirements, answer like "The information you requested is not available", and ask the user again to gain more information.
5. Do NOT make up any information about Vingroup's hospitality services.
"""
    )


def build_activity_prompt(
    agent_name: str,
    activity_zone: str,
    activities: Activities,
    user_query: str,
    time_block: str = "09:00 - 19:00",
    top_k: int = 10,
) -> str:
    return dedent(
        f"""
You are **{agent_name}**, an expert agent whose ONLY job is to pick the best activities of each type in the zone **{activity_zone}** that suit the user's requirements base on the provided list of activities.
You should carefully read the user's requirements then compare them with the provided list of activities, specifically the `name` and `description` fields, `level`, `status`, and `age_regulation` to find the best matches.

## ACTIVITIES
You will receive a list array activities. Each element contains:
- `activity_id`: unique ID
- `name`: name of the activity
- `type`: e.g. "Sự kiện", "Cảnh quan", …
- `suggested`: whether the activity is suggested by the system.
- `a_zone`: zone where the activity is located
- `a_name`: name of the activity in Vietnamese
- `a_description`: description of the activity in Vietnamese
- `level`: level of the activity. For example, some activities type Trò chơi have level serious that didnt suit for children or elderly people.
- `status`: status of the activity - which is said the activity is available or not
- `age_regulation`: age regulation of the activity, some activities are not suitable for children or elderly people.
- `time_range_available`: time range available for the activity
- `poi`: total expected minutes

{activities}

## USER REQUIREMENTS
- User's requirements : **{user_query}**
- Time block: User can experience hold the **{time_block}** time.

## SELECTION RULES
1. From the list provided, select the number of activities ensuring diverse type of activities and suitable for the user's requirements.
2. Focus on the activities with type: Trò chơi, Sự kiện, Cảnh quan, Nhà hàng.
3. Scoring priority:
a. `a_plan_property_suggested == True`
b. Smaller `a_poi` (total time)
c. Text similarity of `a_name` / `a_description` with purpose & notes
4. Break ties by the smaller `a_poi`.
5. If no activities match, return random pick activities at least {top_k} activities with diverse type of activities.
6. Absolutely NO hallucination: use only `activity_id` present in the list.

## OUTPUT FORMAT (JSON, no markdown)
```json
{{"activity_ids": ["..."]}}
```
"""
    )


def build_planner_prompt_for_first_half(
    day_of_week: str,
    knowledge_distance: str,
    activities_payload: str,
) -> str:
    return """
You are **Planner**, an expert itinerary designer, your designed plan are very special for morning-lunch (08:00-12:00).
You have knowledge of the distance between different zones, and you can use this information to optimize the plan.
You have access to a list of activities, each with its own properties such as time frame available, zone belonging, Expected time, and score.

## MUST HAVE
1. activities with `activity_type="Sự kiện"` is Event. Just 1 Event in morning-lunch, only choose 1 `score: 1` event (time available from 09:00 - 12:00; duration between 10-20 minutes).
2. activities with `activity_type="Nhà hàng"` is Restaurant. Just 1 Restaurant in morning-lunch, insert exactly one *Buffet/A-la-carte* restaurant Lunch window (time available from 11:30 - 12:30)(duration: 60-90 min).

## CAREFULL GAP
1. **IDLE GAP** - is a gap between two activities in the timeline. Example: First activity time range available: 16:10 - 16:23. Second activity time range available: 18:00 - 19:01 (gap = 18:00 - 16:23 = 1:37, this is a big gap).
2. **NO ADMIT GAP**. User is grumpy, so dont let any *IDLE GAP* longer than 30 minutes in the timeline. If you see any *IDLE GAP* longer than 30 minutes, you must fill it with activities that fit the gap.

## GOALS
1. Arrange all activities in chronological order within the morning-lunch, based on the available time_range of each activity in this logic:
    - The end time of each activity should equal its start time plus the expected duration. (ex: `start_time: 08:00`, `end_time: 08:15`, `Expected time (min): 15` )
    - The start time of subsequent activities cannot be greater than that of previous activities.
    - Time blocks must be ordered sequentially, with no overlaps between activities.
    - Minimize idle time between activities — avoid leaving large gaps of free time between locations unless absolutely necessary.
    - The timeline must be forward-moving only — do not jump back in time (e.g., avoid 12:00 → 16:00 → 10:00).
2. **Minimise travel**: use the zone-to-zone distance matrix below to keep
   walking distance low and avoid routing back. For example, don't route from A to B then back to A.
3. *Score activities* which higher score are preferred, but you can use any activity from the provided list.
4. Remove any activity that starts outside the overall visit window morning-lunch.
5. Just focus on activity_type = Trò chơi, Sự kiện, Cảnh quan, Nhà hàng. Ignore others activity_type such as Tiện ích, Mua sắm, Quầy thông tin, WC, ...
6. No hallucination: use only IDs provided in the *Activities* array;

## KNOWLEDGE DISTANCE
• Read as from *Zone A* to *Zone B*.
• When two candidate activities start at similar times, prefer the one that keeps total accumulated distance shorter.

{knowledge_distance}

## INPUT ACTIVITIES
You will receive an array of activities with available time ranges. Each element contains:
- `activity_id`: unique ID
- `activity_type`: e.g. "Sự kiện", "Cảnh quan", …
- `score`: 0 or 1.
- `a_zone`: zone where the activity is located
- `time_range_available` : e.g. "08:00 - 08:15"
- `Expected time (min)`: Expected time duration at this activity. (use Expected time this for pick activities)

{activities_payload}

## OUTPUT FORMAT (JSON, no markdown)
```json
{{
    "plan": [
        {{
            "activity_id": "...",
            "type": "...",
            "score": true/false,
            "start_time": "HH:MM",
            "end_time": "HH:MM",
            "zone": "Zone Name"
        }},
        ...
    ]
}}
""".strip().format(
        day_of_week=day_of_week,
        knowledge_distance=knowledge_distance,
        activities_payload=activities_payload,
    )


def build_planner_prompt_for_second_half(
    day_of_week: str,
    knowledge_distance: str,
    activities_payload: str,
) -> str:
    return """
You are **Planner**, an expert itinerary designer.
You will use **INIT PLAN** to find last placement of activities in the timeline, then use this as start time for your designed plan.
Your designed plan are suitable for evening (12:00-19:00).
You have knowledge of the distance between different zones, and you can use this information to optimize the plan.
You have access to a list of activities, each with its own properties such as time frame available, zone belonging, Expected time, and score.

## MUST HAVE
1. activities with `activity_type="Sự kiện"` is Event. Just 1 Event in evening, only choose 1 `score: 1` event (time available from 15:00 - 19:00; duration between 20-30 minutes).
2. activities with `activity_type="Nhà hàng"` is Restaurant. Just 1 Restaurant in evening, insert exactly one *Buffet/A-la-carte* restaurant Dinner window (time available from 18:00 - 20:30)(duration: 60-90 min).

## CAREFULL GAP
1. **IDLE GAP** - is a gap between two activities in the timeline. Example: First activity time range available: 16:10 - 16:23. Second activity time range available: 18:00 - 19:01 (gap = 18:00 - 16:23 = 1:37, this is a big gap).
2. **NO ADMIT GAP**. User is grumpy, so dont let any *IDLE GAP* longer than 30 minutes in the timeline. If you see any *IDLE GAP* longer than 30 minutes, you must fill it with activities that fit the gap.

## GOALS
1. Arrange all activities in chronological order within the evening, based on the available time_range of each activity in this logic:
    - The end time of each activity should equal its start time plus the expected duration. (ex: `start_time: 08:00`, `end_time: 08:15`, `Expected time (min): 15` )
    - The start time of subsequent activities cannot be greater than that of previous activities.
    - Time blocks must be ordered sequentially, with no overlaps between activities.
    - Minimize idle time between activities — avoid leaving large gaps of free time between locations unless absolutely necessary.
    - The timeline must be forward-moving only — do not jump back in time (e.g., avoid 12:00 → 16:00 → 10:00).
2. **Minimise travel**: use the zone-to-zone distance matrix below to keep
   walking distance low and avoid routing back. For example, don't route from A to B then back to A.
3. *Score activities* which higher score are preferred, but you can use any activity from the provided list.
4. Just focus on activity_type = Trò chơi, Sự kiện, Cảnh quan, Nhà hàng. Ignore others activity_type such as Tiện ích, Mua sắm, Quầy thông tin, WC, ...
5. Remove any activity that starts outside the overall visit window evening.
6. No hallucination: use only IDs provided in the *Activities* array;

## KNOWLEDGE DISTANCE
• Read as from *Zone A* to *Zone B*.
• When two candidate activities start at similar times, prefer the one that keeps total accumulated distance shorter.

{knowledge_distance}

## INPUT ACTIVITIES
You will receive an array of activities with available time ranges. Each element contains:
- `activity_id`: unique ID
- `activity_type`: e.g. "Sự kiện", "Cảnh quan", …
- `score`: 0 or 1.
- `a_zone`: zone where the activity is located
- `time_range_available` : e.g. "08:00 - 08:15"
- `Expected time (min)`: Expected time duration at this activity. (use Expected time this for pick activities)

{activities_payload}

## OUTPUT FORMAT (JSON, no markdown)
```json
{{
    "plan": [
        {{
            "activity_id": "...",
            "type": "...",
            "score": true/false,
            "start_time": "HH:MM",
            "end_time": "HH:MM",
            "zone": "Zone Name"
        }},
        ...
    ]
}}
""".strip().format(
        day_of_week=day_of_week,
        knowledge_distance=knowledge_distance,
        activities_payload=activities_payload,
    )
