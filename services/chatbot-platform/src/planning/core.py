"""
Base agent classes and utilities for the Trip Planner Agent system
"""

import asyncio
import json
import logging
from enum import Enum
from textwrap import dedent
from typing import Any, Dict, List, Type, TypeVar

from openai import AsyncOpenAI
from pydantic import BaseModel
from src.core import llm_async_client
from src.consts import settings

from .activity import Activities, activities
from .config import DEFAULT_DAY_OF_WEEK, DEFAULT_TYPE_PLAN
from .models import PlanID, PlanResponse
from .utils import (
    build_activity_prompt,
    build_knowledge_distance,
    build_planner_prompt_for_first_half,
    build_planner_prompt_for_second_half,
    compute_zone_distance_matrix,
    format_plan_as_list,
    format_plan_text,
    prepare_activity_payload_for_provider,
    prepare_activity_payload_for_trip,
    remove_planned_activities_from_payload,
    slugify,
)

logger = logging.getLogger(__name__)

# Type aliases
T = TypeVar("T", bound=BaseModel)


class AgentMessage(BaseModel):
    sender: str
    receiver: str
    message_type: str
    payload: Dict[str, Any]


class AgentType(Enum):
    COORDINATOR = "coordinator"
    ACTIVITY_PROVIDER = "activity_provider"
    TRIP_PLANNER = "trip_planner"


class BaseAgent:
    """
    Base class for agents, providing a unified LLM call method.
    """

    def __init__(
        self,
        agent_id: str,
        agent_type: AgentType,
        llm: AsyncOpenAI = llm_async_client,
    ):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.llm = llm

    async def _call_llm(
        self,
        model: str,
        system_prompt: str,
        user_prompt: str,
        response_model: Type[T],
        **kwargs,
    ) -> T:
        """
        Call the LLM in a thread for sync-based clients, returning a typed pydantic model.
        """
        try:
            response = await self.llm.beta.chat.completions.parse(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                **kwargs,
                response_format=response_model,
            )
            return response.choices[0].message.parsed  # type: ignore
        except Exception as e:
            logger.error(f"LLM call failed in {self.agent_id}: {e}")
            raise

    async def process_message(self, message: AgentMessage) -> AgentMessage:
        raise NotImplementedError("Subclasses must implement process_message")


class ActivityProviderAgent(BaseAgent):
    """Agent responsible for selecting activities based on user requirements"""

    def __init__(
        self,
        agent_id: str,
        activity_zone: str,
        activities: Activities,
        location: str,
        llm: AsyncOpenAI = llm_async_client,
    ):
        super().__init__(agent_id, AgentType.ACTIVITY_PROVIDER, llm)
        self.activity_zone = activity_zone
        self.activities = activities
        self.location = location

    async def select_activities(
        self,
        user_query: str,
        time_block: str,
        top_k: int = 10,
        day_of_week: str = "Thứ 2",
    ) -> Dict[str, Any]:
        """Select activities of this agent's type based on user requirements using LLM"""
        zone_activities = self.activities.get_activity_by_zone(self.activity_zone)
        zone_activities = [
            act for act in zone_activities if act.a_zone == self.activity_zone
        ]

        if not zone_activities:
            return {
                "status": "success",
                "activity_ids": [],
                "activity_zone": self.activity_zone,
                "count": 0,
            }

        # Prepare compact data for LLM
        compact = prepare_activity_payload_for_provider(zone_activities, day_of_week)
        act_payload = json.dumps(compact, ensure_ascii=False, indent=2)

        # Build prompt using the same function from utils
        system_prompt = build_activity_prompt(
            agent_name=slugify(self.activity_zone) + "_agent",
            activity_zone=self.activity_zone,
            activities=act_payload,
            user_query=user_query,
            time_block=time_block,
            top_k=top_k,
        )
        try:
            plan_id: PlanID = await self._call_llm(
                model=settings.openai_model_name,
                system_prompt=system_prompt,
                user_prompt="Returns list of activity IDs",
                temperature=0.2,
                max_tokens=400,
                response_model=PlanID,
            )
            ids = plan_id.activity_ids or []
            if not ids:
                sorted_acts = sorted(
                    zone_activities,
                    key=lambda act: getattr(act, "a_plan_property_suggested", False),
                )
                ids = [act.a_id for act in sorted_acts[:top_k]]

            return {
                "status": "success",
                "activity_ids": ids,
                "activity_zone": self.activity_zone,
                "count": len(ids),
            }
        except Exception as e:
            logger.error(f"LLM call failed in {self.agent_id}: {e}")
            return {"status": "error", "error": str(e), "activity_ids": []}

    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """Process incoming message"""
        if message.message_type == "SELECT_ACTIVITIES":
            payload = message.payload
            result = await self.select_activities(
                user_query=payload.get("user_query", ""),
                time_block=payload.get("time_block", ""),
                top_k=payload.get("top_k", 10),
                day_of_week=payload.get(
                    "day_of_week", "Thứ 2"
                ),  # FIXME: get today weekday
            )
            return AgentMessage(
                sender=self.agent_id,
                receiver=message.sender,
                message_type="ACTIVITY_SELECTION_RESULT",
                payload=result,
            )
        else:
            return AgentMessage(
                sender=self.agent_id,
                receiver=message.sender,
                message_type="ERROR",
                payload={"error": f"Unknown message type: {message.message_type}"},
            )


class TripPlannerAgent(BaseAgent):
    """Agent responsible for creating optimized trip itineraries"""

    def __init__(
        self,
        agent_id: str,
        activities: Activities,
        zones: List[str],
        distance_matrix: Dict[str, Dict[str, float]],
        llm: AsyncOpenAI = llm_async_client,
    ):
        super().__init__(agent_id, AgentType.TRIP_PLANNER, llm)
        self.activities = activities
        self.zones_list = zones
        self.distance_matrix = distance_matrix

    async def create_trip_plan(
        self,
        activity_results: Dict[str, List[str]],
        time_block: str = None,
        day_of_week: str = DEFAULT_DAY_OF_WEEK,
    ) -> Dict[str, Any]:
        """Create an optimized trip plan from selected activities"""
        acts = []
        for _, ids in activity_results.items():
            for aid in ids:
                if a := self.activities.get_activity_by_id(aid):
                    acts.append(a)

        if not acts:
            return {"status": "success", "plan": []}

        try:
            if time_block == "full_plan":
                payload = prepare_activity_payload_for_trip(acts, day_of_week)
                knowledge = build_knowledge_distance(
                    self.distance_matrix, self.zones_list
                )
                # First half
                prompt1 = build_planner_prompt_for_first_half(
                    day_of_week=day_of_week,
                    knowledge_distance=knowledge,
                    activities_payload=json.dumps(payload, ensure_ascii=False),
                )

                planner1: PlanResponse = await self._call_llm(
                    model=settings.openai_model_name,
                    system_prompt=prompt1,
                    user_prompt="Optimization plan, focus on timeline and logic",
                    temperature=0.2,
                    max_tokens=4000,
                    response_model=PlanResponse,
                )

                first_plan = format_plan_text(self.activities, planner1.plan, "First")

                # Second half
                text = dedent(
                    f"""\n## INIT PLAN: This is a plan made by experts.
                The activities are arranged in a logical order - `Thời gian chơi` column, with time blocks and activity ID.
                {first_plan}
                ## END INIT PLAN

                """
                ).strip()

                filtered_payload = remove_planned_activities_from_payload(
                    payload, planner1.plan
                )

                prompt2 = text + build_planner_prompt_for_second_half(
                    day_of_week=day_of_week,
                    knowledge_distance=knowledge,
                    activities_payload=json.dumps(filtered_payload, ensure_ascii=False),
                )

                planner2: PlanResponse = await self._call_llm(
                    model=settings.openai_model_name,
                    system_prompt=prompt2,
                    user_prompt=f"Optimization plan, focus on timeline and logic \n ## INIT PLAN{first_plan}",
                    temperature=0.2,
                    max_tokens=4000,
                    response_model=PlanResponse,
                )

                final_plan = planner1.plan + planner2.plan

                return {"status": "success", "plan": final_plan}

            elif time_block == "first_half":
                payload = prepare_activity_payload_for_trip(acts, day_of_week)
                knowledge = build_knowledge_distance(
                    self.distance_matrix, self.zones_list
                )
                prompt = build_planner_prompt_for_first_half(
                    day_of_week=day_of_week,
                    knowledge_distance=knowledge,
                    activities_payload=json.dumps(payload, ensure_ascii=False),
                )
                planner: PlanResponse = await self._call_llm(
                    model=settings.openai_model_name,
                    system_prompt=prompt,
                    user_prompt="Optimization plan, focus on timeline and logic",
                    temperature=0.2,
                    max_tokens=4000,
                    response_model=PlanResponse,
                )
                return {"status": "success", "plan": planner.plan}

            elif time_block == "second_half":
                payload = prepare_activity_payload_for_trip(acts, day_of_week)
                knowledge = build_knowledge_distance(
                    self.distance_matrix, self.zones_list
                )
                prompt = build_planner_prompt_for_second_half(
                    day_of_week=day_of_week,
                    knowledge_distance=knowledge,
                    activities_payload=json.dumps(payload, ensure_ascii=False),
                )
                planner: PlanResponse = await self._call_llm(
                    model=settings.openai_model_name,
                    system_prompt=prompt,
                    user_prompt="Optimization plan, focus on timeline and logic",
                    temperature=0.2,
                    max_tokens=4000,
                    response_model=PlanResponse,
                )
                return {"status": "success", "plan": planner.plan}

        except Exception as e:
            logger.error(f"Error when creating trip plan: {e}")
            return {"status": "error", "error": str(e), "plan": []}

    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """Process incoming message"""
        if message.message_type == "CREATE_TRIP_PLAN":
            payload = message.payload
            result = await self.create_trip_plan(
                activity_results=payload.get("activity_results", {}),
                time_block=payload.get("time_block", ""),
                day_of_week=payload.get("day_of_week", ""),
            )
            return AgentMessage(
                sender=self.agent_id,
                receiver=message.sender,
                message_type="TRIP_PLAN_RESULT",
                payload=result,
            )
        else:
            return AgentMessage(
                sender=self.agent_id,
                receiver=message.sender,
                message_type="ERROR",
                payload={"error": f"Unknown message type: {message.message_type}"},
            )


async def generate_plan(
    query: str,
    location: str,
    time_block: str = DEFAULT_TYPE_PLAN,
    day_of_week: str = "Thứ 2",
    top_k: int = 10,
) -> str:
    """
    Core pipeline: select activities via providers, create a trip plan, and format the itinerary.
    """
    # Provider selection
    unique_zones = {a.a_zone for a in activities.get_activity_by_site(location)}
    providers = {
        t: ActivityProviderAgent(f"{slugify(t)}_agent", t, activities, location)
        for t in unique_zones
    }

    distance_matrix, zones_list = compute_zone_distance_matrix(activities, location)

    async def call_provider(a_zone: str, agent: ActivityProviderAgent):
        res = await agent.select_activities(
            user_query=query,
            time_block=time_block,
            top_k=top_k,
            day_of_week=day_of_week,
        )
        return a_zone, res.get("activity_ids", [])

    tasks = [call_provider(t, ag) for t, ag in providers.items()]
    results = await asyncio.gather(*tasks)

    activity_results = {}
    zones_list = []
    for item in results:
        # print(f"[*] Provider result: {item}")

        if isinstance(item, Exception):
            continue
        activity_results[item[0]] = item[1]
        zones_list.append(item[0])
    if not activity_results:
        raise RuntimeError("Không tìm thấy hoạt động phù hợp.")

    # Trip planning
    planner = TripPlannerAgent(
        agent_id="trip_planner",
        activities=activities,
        zones=zones_list,
        distance_matrix=distance_matrix,
    )
    msg = AgentMessage(
        sender="pipeline",
        receiver=planner.agent_id,
        message_type="CREATE_TRIP_PLAN",
        payload={
            "activity_results": activity_results,
            "user_query": query,
            "time_block": time_block,
            "day_of_week": day_of_week,
        },
    )
    resp = await planner.process_message(msg)
    if resp.message_type != "TRIP_PLAN_RESULT":
        error = resp.payload.get("error", "Trip planner error")
        raise RuntimeError(error)

    # Format plan
    plan_payload = resp.payload
    return format_plan_as_list(activities, plan_payload)
