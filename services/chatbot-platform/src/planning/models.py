from pydantic import BaseModel, Field
from typing import List


class ActivityResponse(BaseModel):
    activity_id: str = Field(..., description="Unique ID of the activity")
    type: str = Field(
        ..., description="Type of the activity (e.g., C<PERSON>nh quan, Sự kiện)"
    )
    start_time: str = Field(
        ..., description="Start time of the activity in HH:MM format"
    )
    end_time: str = Field(..., description="End time of the activity in HH:MM format")
    zone: str = Field(..., description="Zone where the activity is located")

    def to_dict(self) -> dict:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: dict) -> "ActivityResponse":
        return cls(**data)

    class Config:
        json_schema_extra = {
            "example": {
                "activity_id": "12345",
                "type": "Cảnh quan",
                "start_time": "08:00",
                "end_time": "08:30",
                "zone": "Zone A",
            }
        }


class PlanResponse(BaseModel):
    plan: List[ActivityResponse] = Field(
        description="List of planned activities with start and end times"
    )

    def to_dict(self) -> dict:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: dict) -> "PlanResponse":
        return cls(**data)

    class Config:
        json_schema_extra = {
            "example": {
                "plan": [
                    {
                        "activity_id": "12345",
                        "type": "Cảnh quan",
                        "start_time": "08:00",
                        "end_time": "08:30",
                        "zone": "Zone A",
                    },
                    {
                        "activity_id": "67890",
                        "type": "Sự kiện",
                        "start_time": "08:30",
                        "end_time": "09:00",
                        "zone": "Zone B",
                    },
                ]
            }
        }


class PlanID(BaseModel):
    activity_ids: List[str] = Field(description="List of selected activity IDs")

    class Config:
        json_schema_extra = {
            "example": {
                "activity_ids": ["act1", "act2"],
            }
        }
