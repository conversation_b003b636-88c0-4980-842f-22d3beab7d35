import json
import logging
from typing import Dict, List, Optional

import pandas as pd
from pydantic import BaseModel, Field, field_validator
from src.planning.config import DATA_PATH

logger = logging.getLogger(__name__)


LANGUAGES = ["Tiếng Việt", "T<PERSON><PERSON><PERSON> Anh", "Tiếng Trung", "Tiếng Hàn"]


DAY_OF_WEEK = [
    "Thứ 2",
    "Thứ 3",
    "Thứ 4",
    "Thứ 5",
    "Thứ 6",
    "Thứ 7",
    "<PERSON><PERSON> nhật",
    "<PERSON><PERSON><PERSON> lễ",
]

PROVINCE2SITE = {
    "Hội An": ["VinWonders Nam Hội An"],
    "Phú Quốc": [
        "Vinpearl Safari Phú Quốc",
        "Grand World Phú Quốc",
        "VinWonders Phú Quốc",
    ],
    "Hồ Chí Minh": ["Công viên Grand Park"],
    "Nghệ An": ["VinWonders Cửa Hội"],
    "Hà Nội": ["VinWonders Wave Park & Water Park", "VinKE & Aquarium Times City"],
    "Nha Trang": [
        "VinWonders Nha Trang",
        "Vinpearl Harbour Nha Trang",
        "Tắm bùn Hòn Tằm",
    ],
}

ACTIVITY_ID = "Cây thư mục - Mã CMS\n(mã sinh ra trên cms, không thay đổi mã này)"
ACTIVITY_SITE = "Cây thư mục - Cơ sở"
ACTIVITY_ZONE = "Cây thư mục - Phân khu"
ACTIVITY_SUBZONE = "Cây thư mục - Tiểu phân khu"
ACTIVITY_AVAILABLE_ON_APP = "Cây thư mục - Có hiển thị trên app không ?"
ACTIVITY_TYPE = "1. Thông tin chung - Phân loại - Dropdown"
ACTIVITY_NAME = "1. Thông tin chung - Tên - Input text - {language}"
ACTIVITY_DESCRIPTION = (
    "1. Thông tin chung - Mô tả \n( Tối đa 150 ký tự ) - Input text - {language}"
)
ACTIVITY_WORD_COUNTER = "1. Thông tin chung - Đếm số ký tự"
ACTIVITY_IMAGE = "1. Thông tin chung - Link ảnh - Image"
ACTIVITY_DETAIL_PROPERTY_TIME_RANGE = "2. Thông tin chi tiết - Thời gian hoạt đông theo thứ (Checkbox chọn thứ) - Timepicker - {dayofweek} - Khung giờ"
ACTIVITY_DETAIL_PROPERTY_DENSITY = "2. Thông tin chi tiết - Thời gian hoạt đông theo thứ (Checkbox chọn thứ) - Timepicker - {dayofweek} - Mật độ"
ACTIVITY_DETAIL_PROPERTY_TIME_WAITING = "2. Thông tin chi tiết - Thời gian hoạt đông theo thứ (Checkbox chọn thứ) - Timepicker - {dayofweek} - Thời gian chờ"
ACTIVITY_DETAIL_PROPERTY_PEOPLE_LIMITATION_BY_TIME_RANGE = "2. Thông tin chi tiết - Thời gian hoạt đông theo thứ (Checkbox chọn thứ) - Timepicker - {dayofweek} - Giới hạn người đăng ký/khung giờ"
ACTIVITY_DETAIL_PROPERTY_DURATION_OVERALL = (
    "2. Thông tin chi tiết - Thời lượng \n( Phút ) - Number"
)
ACTIVITY_DETAIL_PROPERTY_TIME_WAITING_OVERALL = (
    "2. Thông tin chi tiết - Thời gian chờ\n( Phút ) - Number"
)
ACTIVITY_DETAIL_PROPERTY_STATUS = (
    "2. Thông tin chi tiết - Trạng thái hoạt động - Dropdown"
)
ACTIVITY_DETAIL_PROPERTY_SHOW_PAID = (
    "2. Thông tin chi tiết - Show trả phí hay không - Dropdown"
)
ACTIVITY_DETAIL_PROPERTY_AGE_REGULATION = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Quy định tuổi - Check box"
)
ACTIVITY_DETAIL_PROPERTY_LEVEL = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Mức độ - Checkbox"
)
ACTIVITY_DETAIL_PROPERTY_WEIGHT_REGULATION = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Quy định cân nặng - Checkbox"
)
ACTIVITY_DETAIL_PROPERTY_HEIGHT_REGULATION = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Quy định chiều cao - Checkbox"
)
ACTIVITY_DETAIL_PROPERTY_NOTE = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Lưu ý - Điền thông tin"
)
ACTIVITY_DETAIL_PROPERTY_STOP_BAD_WEATHER = "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Dừng hoạt động khi thời thiết xấu - Dropdown"
ACTIVITY_DETAIL_PROPERTY_RESTAURANT_TYPE = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Loại nhà hàng - Dropdown"
)
ACTIVITY_DETAIL_PROPERTY_CUISINE_TYPE = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Loại Ẩm thực - Dropdown"
)
ACTIVITY_DETAIL_PROPERTY_SHOPPING = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Shopping - Checkbox"
)
ACTIVITY_DETAIL_PROPERTY_OTHER_UTILITY = (
    "2. Thông tin chi tiết - Thuộc tính ( Nếu có ) - Tiện ích khác - Checkbox"
)
ACTIVITY_LOCATION_LONGITUDE = "Vị trí - Kinh độ\nLongtitude"
ACTIVITY_LOCATION_LATITUDE = "Vị trí - Vĩ độ\nLatitude"
ACTIVITY_LOCATION_NOTE = "Vị trí - Lưu ý - Điền thông tin ({language})"
ACTIVITY_QUEUE_PROPERTY = "3. Thông tin hàng chờ ảo - Hàng chờ ảo (bật/tắt)"
ACTIVITY_QUEUE_PROPERTY_PLAYER_COUNT = "3. Thông tin hàng chờ ảo - Số người chơi/lượt"
ACTIVITY_PLAN_PROPERTY_SUGGESTED = "4. Thông tin kế hoạch - Được gợi ý - Checkbox"
ACTIVITY_PLAN_PROPERTY_WAITING_TIME = (
    "4. Thông tin kế hoạch - Thời gian chờ dự kiến (phút) - Number"
)
ACTIVITY_PLAN_PROPERTY_TRAVEL_TIME = (
    "4. Thông tin kế hoạch - Thời gian di chuyển trước (phút) - Number"
)
ACTIVITY_PLAN_PROPERTY_TRAVEL_TIME_AFTER = (
    "4. Thông tin kế hoạch - Thời gian di chuyển sau (phút) - Number"
)
ACTIVITY_CONTACT_PHONE_NUMBER = "Thông tin liên hệ - Số điện thoại - Số điện thoại"
ACTIVITY_MENU_UPLOAD_IMAGE = "Menu - Upload ảnh / Input link website - {language}"


class ActivityDetail(BaseModel):
    a_id: Optional[str] = Field(default=None)
    a_site: Optional[str] = Field(default=None)
    a_zone: Optional[str] = Field(default=None)
    a_subzone: Optional[str] = Field(default=None)
    a_available_on_app: Optional[str] = Field(default=None)
    a_names: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_descriptions: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_type: Optional[str] = Field(default=None)
    a_image: Optional[str] = Field(default=None)
    a_word_counter: Optional[int] = Field(default=None)
    a_menu_upload_image: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_contact_phone_number: Optional[str] = Field(default=None)
    a_detail_property_time_range: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_detail_property_density: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_detail_property_time_waiting: Optional[Dict[str, str]] = Field(
        default_factory=dict
    )
    a_detail_property_people_limitation_by_time_range: Optional[Dict[str, str]] = Field(
        default_factory=dict
    )
    a_detail_property_duration_overall: Optional[int] = Field(default=None)
    a_detail_property_time_waiting_overall: Optional[int] = Field(default=None)
    a_detail_property_status: Optional[str] = Field(default=None)
    a_detail_property_show_paid: Optional[str] = Field(default=None)
    a_detail_property_age_regulation: Optional[bool] = Field(default=None)
    a_detail_property_level: Optional[bool] = Field(default=None)
    a_detail_property_weight_regulation: Optional[bool] = Field(default=None)
    a_detail_property_height_regulation: Optional[bool] = Field(default=None)
    a_detail_property_note: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_detail_property_stop_bad_weather: Optional[str] = Field(default=None)
    a_detail_property_restaurant_type: Optional[str] = Field(default=None)
    a_detail_property_cuisine_type: Optional[str] = Field(default=None)
    a_detail_property_shopping: Optional[bool] = Field(default=None)
    a_detail_property_other_utility: Optional[bool] = Field(default=None)
    a_location_longitude: Optional[float] = Field(default=None)
    a_location_latitude: Optional[float] = Field(default=None)
    a_location_note: Optional[Dict[str, str]] = Field(default_factory=dict)
    a_queue_property: Optional[bool] = Field(default=None)
    a_queue_property_player_count: Optional[int] = Field(default=None)
    a_plan_property_suggested: Optional[bool] = Field(default=None)
    a_plan_property_waiting_time: Optional[int] = Field(default=None)
    a_plan_property_travel_time: Optional[int] = Field(default=None)
    a_plan_property_travel_time_after: Optional[int] = Field(default=None)
    a_poi: int = Field(default=0, description="Tổng thời gian dự kiến (POI)")

    @classmethod
    def from_dict(cls, data: dict) -> "ActivityDetail":
        return cls(**data)

    @classmethod
    def from_series(cls, series: pd.Series) -> "ActivityDetail":
        def get_val(key, cast=None):
            val = series.get(key)
            if pd.isna(val):
                return None
            try:
                return cast(val) if cast else val
            except Exception:
                return None

        def get_val_by_dayofweek(val_template, cast=str, default_value=None):
            if default_value is None:
                default_value = "" if cast == str else 0

            result = {}
            for day in DAY_OF_WEEK:
                field_name = val_template.format(dayofweek=day)
                value = get_val(field_name, cast)
                result[day] = value if value is not None else default_value
            return result

        def get_val_by_language(val_template, cast=str, default_value=None):
            if default_value is None:
                default_value = "" if cast == str else 0

            result = {}
            for lang in LANGUAGES:
                field_name = val_template.format(language=lang)
                value = get_val(field_name, cast)
                result[lang] = value if value is not None else default_value
            return result

        suggested = get_val(ACTIVITY_PLAN_PROPERTY_SUGGESTED, str)
        suggested = True if suggested and suggested.lower() == "có" else False

        return cls(
            a_id=get_val(ACTIVITY_ID, str),
            a_site=get_val(ACTIVITY_SITE, str),
            a_zone=get_val(ACTIVITY_ZONE, str),
            a_subzone=get_val(ACTIVITY_SUBZONE, str),
            a_available_on_app=get_val(ACTIVITY_AVAILABLE_ON_APP, str),
            a_names=get_val_by_language(ACTIVITY_NAME, str, ""),
            a_descriptions=get_val_by_language(ACTIVITY_DESCRIPTION, str, ""),
            a_type=get_val(ACTIVITY_TYPE, str),
            a_image=get_val(ACTIVITY_IMAGE, str),
            a_word_counter=get_val(ACTIVITY_WORD_COUNTER, int),
            a_menu_upload_image=get_val_by_language(
                ACTIVITY_MENU_UPLOAD_IMAGE, str, ""
            ),
            a_contact_phone_number=get_val(ACTIVITY_CONTACT_PHONE_NUMBER, str),
            a_detail_property_time_range=get_val_by_dayofweek(
                ACTIVITY_DETAIL_PROPERTY_TIME_RANGE, str, ""
            ),
            a_detail_property_density=get_val_by_dayofweek(
                ACTIVITY_DETAIL_PROPERTY_DENSITY, str, ""
            ),
            a_detail_property_time_waiting=get_val_by_dayofweek(
                ACTIVITY_DETAIL_PROPERTY_TIME_WAITING, str, ""
            ),
            a_detail_property_people_limitation_by_time_range=get_val_by_dayofweek(
                ACTIVITY_DETAIL_PROPERTY_PEOPLE_LIMITATION_BY_TIME_RANGE, str, ""
            ),
            a_detail_property_duration_overall=get_val(
                ACTIVITY_DETAIL_PROPERTY_DURATION_OVERALL, int
            ),
            a_detail_property_time_waiting_overall=get_val(
                ACTIVITY_DETAIL_PROPERTY_TIME_WAITING_OVERALL, int
            ),
            a_detail_property_status=get_val(ACTIVITY_DETAIL_PROPERTY_STATUS, str),
            a_detail_property_show_paid=get_val(
                ACTIVITY_DETAIL_PROPERTY_SHOW_PAID, str
            ),
            a_detail_property_age_regulation=get_val(
                ACTIVITY_DETAIL_PROPERTY_AGE_REGULATION, bool
            ),
            a_detail_property_level=get_val(ACTIVITY_DETAIL_PROPERTY_LEVEL, bool),
            a_detail_property_weight_regulation=get_val(
                ACTIVITY_DETAIL_PROPERTY_WEIGHT_REGULATION, bool
            ),
            a_detail_property_height_regulation=get_val(
                ACTIVITY_DETAIL_PROPERTY_HEIGHT_REGULATION, bool
            ),
            a_detail_property_note=get_val_by_language(
                ACTIVITY_DETAIL_PROPERTY_NOTE, str, ""
            ),
            a_detail_property_stop_bad_weather=get_val(
                ACTIVITY_DETAIL_PROPERTY_STOP_BAD_WEATHER, str
            ),
            a_detail_property_restaurant_type=get_val(
                ACTIVITY_DETAIL_PROPERTY_RESTAURANT_TYPE, str
            ),
            a_detail_property_cuisine_type=get_val(
                ACTIVITY_DETAIL_PROPERTY_CUISINE_TYPE, str
            ),
            a_detail_property_shopping=get_val(ACTIVITY_DETAIL_PROPERTY_SHOPPING, bool),
            a_detail_property_other_utility=get_val(
                ACTIVITY_DETAIL_PROPERTY_OTHER_UTILITY, bool
            ),
            a_location_longitude=get_val(ACTIVITY_LOCATION_LONGITUDE, float),
            a_location_latitude=get_val(ACTIVITY_LOCATION_LATITUDE, float),
            a_location_note=get_val_by_language(ACTIVITY_LOCATION_NOTE, str, ""),
            a_queue_property=get_val(ACTIVITY_QUEUE_PROPERTY, bool),
            a_queue_property_player_count=get_val(
                ACTIVITY_QUEUE_PROPERTY_PLAYER_COUNT, int
            ),
            a_plan_property_suggested=suggested,
            a_plan_property_waiting_time=get_val(
                ACTIVITY_PLAN_PROPERTY_WAITING_TIME, int
            ),
            a_plan_property_travel_time=get_val(
                ACTIVITY_PLAN_PROPERTY_TRAVEL_TIME, int
            ),
            a_plan_property_travel_time_after=get_val(
                ACTIVITY_PLAN_PROPERTY_TRAVEL_TIME_AFTER, int
            ),
        )

    def to_dict(self) -> dict:
        return self.dict()

    def get_activity_details(self, language: str, date_of_week: str) -> dict:
        return {
            "a_id": self.a_id,
            "a_site": self.a_site,
            "a_zone": self.a_zone,
            "a_subzone": self.a_subzone,
            "a_available_on_app": self.a_available_on_app,
            "a_name": self.a_names.get(language, ""),
            "a_description": self.a_descriptions.get(language, ""),
            "a_type": self.a_type,
            "a_image": self.a_image,
            "a_word_counter": self.a_word_counter,
            "a_menu_upload_image": self.a_menu_upload_image.get(language, ""),
            "a_contact_phone_number": self.a_contact_phone_number,
            "a_detail_property_time_range": self.a_detail_property_time_range.get(
                date_of_week, ""
            ),
            "a_detail_property_density": self.a_detail_property_density.get(
                date_of_week, ""
            ),
            "a_detail_property_time_waiting": self.a_detail_property_time_waiting.get(
                date_of_week, ""
            ),
            "a_detail_property_people_limitation_by_time_range": self.a_detail_property_people_limitation_by_time_range.get(
                date_of_week, ""
            ),
            "a_detail_property_duration_overall": self.a_detail_property_duration_overall,
            "a_detail_property_time_waiting_overall": self.a_detail_property_time_waiting_overall,
            "a_detail_property_status": self.a_detail_property_status,
            "a_detail_property_show_paid": self.a_detail_property_show_paid,
            "a_detail_property_age_regulation": self.a_detail_property_age_regulation,
            "a_detail_property_level": self.a_detail_property_level,
            "a_detail_property_weight_regulation": self.a_detail_property_weight_regulation,
            "a_detail_property_height_regulation": self.a_detail_property_height_regulation,
            "a_detail_property_note": self.a_detail_property_note.get(language, ""),
            "a_detail_property_stop_bad_weather": self.a_detail_property_stop_bad_weather,
            "a_detail_property_restaurant_type": self.a_detail_property_restaurant_type,
            "a_detail_property_cuisine_type": self.a_detail_property_cuisine_type,
            "a_detail_property_shopping": self.a_detail_property_shopping,
            "a_detail_property_other_utility": self.a_detail_property_other_utility,
            "a_location_longitude": self.a_location_longitude,
            "a_location_latitude": self.a_location_latitude,
            "a_location_note": self.a_location_note.get(language, ""),
            "a_queue_property": self.a_queue_property,
            "a_queue_property_player_count": self.a_queue_property_player_count,
            "a_plan_property_suggested": self.a_plan_property_suggested,
            "a_plan_property_waiting_time": self.a_plan_property_waiting_time,
            "a_plan_property_travel_time": self.a_plan_property_travel_time,
            "a_plan_property_travel_time_after": self.a_plan_property_travel_time_after,
        }

    # Tính POI tự động
    @field_validator("a_poi", mode="before")
    @classmethod
    def _compute_poi(cls, v, values):
        if v is not None:  # cho phép gán tay nếu muốn
            return v
        duration = values.get("a_detail_property_duration_overall") or 0
        waiting = values.get("a_plan_property_waiting_time") or 0
        travel = values.get("a_plan_property_travel_time") or 0
        travel_af = values.get("a_plan_property_travel_time_after") or 0
        print(f"Computed POI: {duration} + {waiting} + {travel} + {travel_af}")
        return duration + waiting + travel + travel_af


class Activities(BaseModel):
    language: str = Field(
        default="Tiếng Việt", description="Language for activity details"
    )
    activities: List[ActivityDetail] = Field(
        default_factory=list, description="List of activity details"
    )
    activity_map: Dict[str, ActivityDetail] = Field(
        default_factory=dict, description="Map of activity ID to ActivityDetail"
    )
    activity_num: int = Field(default=0, description="Number of activities")

    def __iter__(self):
        return iter(self.activities)

    def __getitem__(self, index):
        return self.activities[index]

    @classmethod
    def from_dataframe(cls, df: pd.DataFrame) -> "Activities":
        activity_list = [ActivityDetail.from_series(row) for _, row in df.iterrows()]
        activity_map = {
            activity.a_id: activity for activity in activity_list if activity.a_id
        }
        return cls(activities=activity_list, activity_num=len(activity_list))

    def add_activity(self, activity: ActivityDetail):
        if not isinstance(activity, ActivityDetail):
            raise ValueError("Expected an instance of ActivityDetail")
        self.activities.append(activity)
        if activity.a_id:  # tránh None key
            self.activity_map[activity.a_id] = activity
        self.activity_num += 1

    def get_activity(self, activity_id: str) -> Optional[ActivityDetail]:
        return self.activity_map.get(activity_id)

    def get_activities(self) -> List[ActivityDetail]:
        return self.activities

    def to_dict(self) -> List[dict]:
        return {
            "activities": [activity.model_dump() for activity in self.activities],
            "activity_map": {k: v.model_dump() for k, v in self.activity_map.items()},
            "activity_num": self.activity_num,
        }

    # --- tiny helper ---
    def _wrap(self, items: List[ActivityDetail]) -> "Activities":
        return Activities(
            activities=items,
            activity_map={a.a_id: a for a in items if a.a_id},
            activity_num=len(items),
        )

    # ---------- filters ----------
    def get_activity_by_site(self, site: str) -> "Activities":
        return self._wrap([a for a in self.activities if a.a_site == site])

    def get_activity_by_zone(self, zone: str) -> "Activities":
        return self._wrap([a for a in self.activities if a.a_zone == zone])

    def get_activity_by_sub_zone(self, sub_zone: str) -> "Activities":
        return self._wrap([a for a in self.activities if a.a_sub_zone == sub_zone])

    def get_activity_by_type(self, activity_type: str) -> "Activities":
        return self._wrap([a for a in self.activities if a.a_type == activity_type])

    def get_activity_by_suggested(self, suggested: bool) -> "Activities":
        return self._wrap(
            [a for a in self.activities if a.a_plan_property_suggested == suggested]
        )

    def get_activity_by_name(
        self, name: str, language: str = "Tiếng Việt"
    ) -> "Activities":
        return self._wrap(
            [
                a
                for a in self.activities
                if a.a_names.get(language, "").lower() == name.lower()
            ]
        )

    def get_activity_by_id(self, activity_id: str) -> Optional[ActivityDetail]:
        for activity in self.activities:
            if activity.a_id == activity_id:
                return activity
        return None

    def get_activity_by_nearest_coordinates(
        self, longitude: float, latitude: float
    ) -> "Activities":
        # Tính toán khoảng cách và tìm hoạt động gần nhất
        # TODO: cần có hàm distance_to() trong ActivityLocation
        """return self._wrap(
            [
                a
                for a in self.activities
                if a.a_location and a.a_location.distance_to(longitude, latitude) < 1000  # 1000m
            ]
        )"""

    @classmethod
    def from_dict(cls, data: dict) -> "Activities":
        activities = [
            ActivityDetail.from_dict(activity)
            for activity in data.get("activities", [])
        ]
        activity_map = {
            k: ActivityDetail.from_dict(v)
            for k, v in data.get("activity_map", {}).items()
        }
        return cls(
            activities=activities,
            activity_map=activity_map,
            activity_num=data.get("activity_num", 0),
        )



try:
    with open(DATA_PATH, "r", encoding="utf-8") as f:
        data = json.load(f)
    activities = Activities.from_dict(data)
except Exception as e:
    logger.warning(f"Activities was not initialized: {e}")
    activities = {}
