import re
import logging
import time
from datetime import datetime
from functools import wraps as _wraps
from typing import List, Dict
from threading import Thread
import pandas as pd
import numpy as np
import json
import decimal


logger = logging.getLogger(__name__)


def logger_func(task_name=None):
    def decorator(fn):
        @_wraps(fn)
        def wrapper(*args, **kwargs):
            name = task_name or fn.__name__
            logger.info(f"------ {name}: start AT {datetime.now()} -----")
            start = time.perf_counter()
            try:
                return fn(*args, **kwargs)
            finally:
                end = time.perf_counter()
                logger.info(
                    f"----- {name}: end took {end - start:.6f}s ----- | {datetime.now()} -----"
                )

        return wrapper

    return decorator(task_name)


def mapping_synom(text: str) -> str:
    synom_mapping = {
        "bđs": "bất động sản",
        "BĐS": "bất động sản",
        "bds": "bất động sản",
        "BDS": "bất động sản",
        "cty": "công ty",
        "CTY": "công ty",
        "phth": "Phối hợp thực hiện",
        "PHTH": "Phối hợp thực hiện",
        "cblđ": "Cán bộ lãnh đạo",
        "CBLĐ": "Cán bộ lãnh đạo",
        "cblđtt": "Cán bộ lãnh đạo trực tiếp",
        "CBLĐTT": "Cán bộ lãnh đạo trực tiếp",
    }

    for key, value in synom_mapping.items():
        text = text.replace(key, value)

    return text


def add_question_mark_if_needed(string: str, end_marks=(".", "!", "?")) -> str:
    if not string.endswith(tuple(end_marks)):
        return string + "?"
    return string


def preprocess_message(message: str) -> str:
    message = mapping_synom(message)
    message = add_question_mark_if_needed(message)
    return message


def extract_image_paths(text: str) -> List[str]:
    """
    Extract all image paths of the form './image_crawled/...<ext>' from the given text.

    Looks for lines like:
      Image Path: ./image_crawled/Danh sách Email chức danh – Vinhomes Wiki_html_img_1.png

    Returns a list of the paths (without the 'Image Path:' prefix).
    """
    pattern = re.compile(
        r"Image\s+Path:\s*"  # literal 'Image Path:' (allowing extra spaces)
        r"(\./image_crawled/"  # start of the path
        r"[^\n]*?"  # any chars except newline, non-greedy
        r"\.(?:png|jpe?g|gif))",  # file extension
        flags=re.IGNORECASE,
    )
    return pattern.findall(text)  # return empty list if no match


def format_big_number(num: float) -> str:
    """
    Round big number to K, M or B.
    Ex: 123456789 -> 12.3M
    """
    if np.isnan(num):
        return "N/A"
    abs_num = abs(num)
    if abs_num >= 1e9:
        scaled = num / 1e9
        suffix = "B"
    elif abs_num >= 1e6:
        scaled = num / 1e6
        suffix = "M"
    elif abs_num >= 1e3:
        scaled = num / 1e3
        suffix = "K"
    else:
        # Format numbers below 1,000 to remove .0 if applicable
        return f"{num:.0f}" if num == int(num) else str(num)

    # Format to 1 decimal place, then remove .0 if needed
    formatted = f"{scaled:.1f}".rstrip("0").rstrip(".")
    return f"{formatted}{suffix}"


def convert_data_into_str(data):
    result = "Không có kết quả"
    if data is None:
        result = "Không có kết quả"
    if isinstance(data, pd.DataFrame):
        formatted_df = data.copy()
        for col in formatted_df.columns:
            if pd.api.types.is_numeric_dtype(formatted_df[col]):
                formatted_df[col] = formatted_df[col].apply(
                    lambda x: format_big_number(x)
                )
        result = formatted_df.to_string()
    elif isinstance(data, Dict):
        result = json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
    elif isinstance(data, str):
        result = data
    elif not data:
        result = "Không có kết quả"
    else:
        result = str(data)

    return result.strip()


class ThreadWithReturnValue(Thread):
    def __init__(
        self, group=None, target=None, name=None, args=(), kwargs={}, Verbose=None
    ):
        Thread.__init__(self, group, target, name, args, kwargs)
        self._return = None

    def run(self):
        if self._target is not None:
            self._return = self._target(*self._args, **self._kwargs)

    def join(self, *args):
        Thread.join(self, *args)
        return self._return


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        elif isinstance(obj, complex):
            return [obj.real, obj.imag]
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, bytes):
            return obj.decode("utf-8")
        elif hasattr(obj, "__dict__"):
            return obj.__dict__
        return super().default(obj)
