import logging

import requests
from aiohttp_retry import <PERSON>try<PERSON><PERSON>
from requests.adapters import HTTPA<PERSON>pter, Retry

logger = logging.getLogger(__name__)

request_session = requests.Session()
retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[500, 502, 503, 504])
request_session.mount("http://", HTTPAdapter(max_retries=retries))
request_session.mount("https://", HTTPAdapter(max_retries=retries))


async def async_request_call(*args, **kwargs):
    async with RetryClient() as session:
        async with session.request(*args, **kwargs) as response:
            if not response.ok:
                logger.error(f"Can not get data {await response.text()}")
                return None
            data = await response.json(content_type=None)
            return data
