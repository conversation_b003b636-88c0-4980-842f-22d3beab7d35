# -*- coding: utf-8 -*-
"""
This file contains the Tree and Node classes for representing a tree structure.
"""


from typing import List, Dict, Optional, Union, Tuple, Iterator, Any
from pydantic import BaseModel, Field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class NodeType(str, Enum):
    ELEMENTARY = "elementary-node"
    NOT_ELEMENTARY = "not-elementary-node"


class Node(BaseModel):
    idx: int
    parent_idx: int = -1
    name: str
    type: NodeType
    text: str
    text_path: str = ""
    summary: str = ""
    summary_path: str = ""
    contextual: str = ""
    children: List[int] = Field(default_factory=list)
    faqs: List[Dict[str, str]] = Field(default_factory=list)
    url_reference: str = ""
    embedding: List[float] = Field(
        default_factory=list, description="Embedding vector for node content"
    )
    faqs_embedding: Dict[str, List[float]] = Field(
        default_factory=dict, description="Embedding vectors for node FAQs"
    )
    metadata: Dict[str, Any] = Field(
        default={
            "location": [],
            "destination": [],
            "post_date": "",
            "deadline_voucher": "",
            "website_type": "general",
            "views": 0,
            "rating": "0/5 (0 votes)",
            "crawled_at": "",
        },
        description="Metadata associated with the node",
    )

    @property
    def is_leaf(self) -> bool:
        return not self.children

    @property
    def content(self) -> str:
        return self.text


class KnowledgeTree(BaseModel):
    """The knowledge tree represent data structure of knowledge base for chatbot system.
    Each document is represented by this tree and each node in the tree can be a chunk of document or be a headline/summary of children nodes.

    Attributes:
        nodes (List[Node]): List of nodes in the tree.
        root (int): Index of the root node.
        threshold_elem (int): Threshold for elementary nodes.
        threshold_chunk (int): Threshold for chunk nodes.

    Terminologies:
        - Elementary node: A node that is small enough to be capture the meaning of chunk nodes below it.
        - Not elementary node: A node that has long content (over `threshold_elem`) and will be chunked into smaller nodes (over `threshold_chunk`).
    """

    nodes: List[Node] = Field(default_factory=list)
    root: int = 0
    threshold_elem: int = 8000
    threshold_chunk: int = 1000

    def __init__(self, **data):
        super().__init__(**data)
        # build index for fast lookup
        self._idx_map: Dict[int, Node] = {node.idx: node for node in self.nodes}

    def add_node(self, node: Node) -> None:
        node.idx = len(self.nodes)
        self.nodes.append(node)
        self._idx_map[node.idx] = node

    def get_node(self, idx: int) -> Node:
        try:
            return self._idx_map[idx]
        except KeyError:
            raise IndexError(f"Node index {idx} out of range.")

    def children_indices(self, idx: int) -> List[int]:
        return self.get_node(idx).children

    def children(self, idx: int) -> Iterator[Node]:
        for i in self.children_indices(idx):
            yield self.get_node(i)

    def parent_index(self, idx: int) -> Optional[int]:
        p = self.get_node(idx).parent_idx
        return p if p >= 0 else None

    def parent(self, idx: int) -> Optional[Node]:
        pi = self.parent_index(idx)
        return self.get_node(pi) if pi is not None else None

    def siblings(self, idx: int) -> Iterator[Node]:
        parent = self.parent_index(idx)
        if parent is None:
            return iter(())
        return (self.get_node(i) for i in self.children_indices(parent) if i != idx)

    def nearest_elementary(self, idx: int) -> Node:
        """Climb to nearest elementary ancestor (includes self)."""
        cur = idx
        while True:
            node = self.get_node(cur)
            if node.type == NodeType.ELEMENTARY or node.parent_idx < 0:
                return node
            cur = node.parent_idx

    def get_node_content(self, idx):
        node = self.get_node(idx)
        return node.text

    def get_embedding_content(self, idx):
        node = self.get_node(idx)
        if node.is_leaf:
            return node.name + " " + node.contextual
        if node.type == NodeType.ELEMENTARY:
            return node.name + " " + node.summary
        return node.name + " " + node.text

    def get_all_embedding_contents(
        self, return_indices: bool = False
    ) -> Union[List[str], Tuple[List[int], List[str]]]:
        contents = [self.get_embedding_content(node.idx) for node in self.nodes]
        if return_indices:
            return [node.idx for node in self.nodes], contents
        return contents

    def get_children_contents(self, node_idx: int) -> List[str]:
        """Return content strings for a node and all its descendants in DFS order."""
        stack = [node_idx]
        contents: List[str] = []
        visited = set()
        while stack:
            cur = stack.pop()
            if cur in visited:
                continue
            visited.add(cur)
            node = self.get_node(cur)
            block = f"# {node.name}\n"
            block += self.get_node_content(node.idx) + "\n"
            contents.append(block)
            # push children
            stack.extend(reversed(node.children))
        return contents

    def get_faqs(self, idx: int = 0) -> Dict[int, List[Dict[str, str]]]:
        """Collect FAQs for a subtree, mapping node idx to its faqs."""
        faqs_map: Dict[int, List[Dict[str, str]]] = {}
        stack = [idx]
        visited = set()
        while stack:
            current = stack.pop()
            if current in visited:
                continue
            visited.add(current)
            node = self.get_node(current)
            if node.faqs:
                faqs_map[current] = node.faqs
            stack.extend(node.children)
        return faqs_map

    def get_elementary_descendants(self, idx: int) -> List[int]:
        """DFS for elementary descendants."""
        stack, result = [idx], []
        while stack:
            cur = stack.pop()
            node = self.get_node(cur)
            if node.type == NodeType.ELEMENTARY:
                result.append(cur)
            stack.extend(node.children)
        return result

    def to_dict(self) -> Dict:
        return {
            "nodes": [n.model_dump() for n in self.nodes],
            "root": self.root,
            "threshold_elem": self.threshold_elem,
            "threshold_chunk": self.threshold_chunk,
        }

    @classmethod
    def from_dict(cls, data: Dict) -> "KnowledgeTree":
        nodes = [Node(**nd) for nd in data.get("nodes", [])]
        tree = cls(
            nodes=nodes,
            root=data.get("root", 0),
            threshold_elem=data.get("threshold_elem", 8000),
            threshold_chunk=data.get("threshold_chunk", 1000),
        )
        return tree
