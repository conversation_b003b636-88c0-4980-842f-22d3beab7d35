import asyncio
import logging
import uuid
from typing import As<PERSON><PERSON>enerator, Union

from openai import Async<PERSON>penA<PERSON>, AsyncStream
from openai.types.chat.chat_completion_message_tool_call import (
    ChatCompletionMessageToolCall,
    Function,
)
from sqlalchemy.orm import Session
from src.consts import settings
from src.core import (
    fitler_most_recent_message,
    prepare_messages,
    rewrite_query_message,
)

from app.config import settings as app_settings

from .db.session import upsert_chat_history
from .schemas import ChatRequest
from .tools import (
    generate_response_from_function_call_async,
    get_available_tools,
)

logger = logging.getLogger(__name__)


# --- Configuration ---
openai_client = AsyncOpenAI(
    base_url=settings.openai_endpoint,
    api_key=settings.openai_api_key,
)

MODEL_NAME = settings.openai_model_name


async def stream_response_async(
    request: ChatRequest, db: Session
) -> AsyncGenerator[Union[str, AsyncStream], None]:
    user_msg = request.message
    session_id = request.session_id if request.session_id else str(uuid.uuid4())
    rewrite_task = None
    tools = get_available_tools(app_name=app_settings.app_name)
    list_chat_hist = prepare_messages(request, db)
    list_chat_context = fitler_most_recent_message(list_chat_hist)

    while True:
        response = await openai_client.chat.completions.create(
            model=MODEL_NAME,
            messages=list_chat_context,
            tools=tools,
            temperature=0.1,
            max_tokens=5000,
            stream=True,
        )
        tool_calls = []
        first_char = ""
        async for chunk in response:
            if not chunk.choices:
                continue
            delta = chunk.choices[0].delta
            if not delta or (not delta.tool_calls and not delta.content):
                continue
            if delta and delta.tool_calls:
                if rewrite_task is None and len(list_chat_context) > 2:
                    rewrite_task = rewrite_query_message(user_msg, list_chat_context)
                tool_chunks = delta.tool_calls
                for tool in tool_chunks:
                    if len(tool_calls) <= tool.index:
                        tool_calls.append(
                            {
                                "id": "",
                                "type": "function",
                                "function": {"name": "", "arguments": ""},
                            }
                        )
                    tc = tool_calls[tool.index]

                    if tool.id:
                        tc["id"] += tool.id
                    if tool.function.name:
                        tc["function"]["name"] += tool.function.name
                    if tool.function.arguments:
                        tc["function"]["arguments"] += tool.function.arguments
            else:
                # Missing first char when check if delta.content is empty
                first_char += delta.content
                # Insert latest history before end process
                upsert_chat_history(
                    session_id=session_id,
                    db=db,
                    messages=list_chat_hist,
                )
                return response, first_char
        function_to_call = [
            ChatCompletionMessageToolCall(
                id=i["id"],
                function=Function(**i["function"]),
                type="function",
                index=ix,
            )
            for ix, i in enumerate(tool_calls)
        ]
        list_chat_hist.append(
            {
                "role": "assistant",
                "tool_calls": [i.model_dump() for i in function_to_call],
                "content": "",
                "function_call": None,
            }
        )
        list_chat_context.append(
            {
                "role": "assistant",
                "tool_calls": [i.model_dump() for i in function_to_call],
                "content": "",
                "function_call": None,
            }
        )
        if rewrite_task is not None:
            rewrite_prompt = await rewrite_task
        else:
            rewrite_prompt = user_msg
        list_function_response = await generate_response_from_function_call_async(
            list_function_call=function_to_call, user_prompt=rewrite_prompt
        )
        if isinstance(list_function_response, AsyncStream):
            # Cover case function return stream response
            for i in function_to_call:
                # Disable content for function is openAI
                # Don't need to add list chat context here
                list_chat_hist.append(
                    {"role": "tool", "tool_call_id": i.id, "content": ""}
                )
            upsert_chat_history(session_id=session_id, db=db, messages=list_chat_hist)
            return list_function_response, ""

        # Get the response of function and continue feed to chat
        list_chat_hist.extend([i.model_dump() for i in list_function_response])
        list_chat_context.extend([i.model_dump() for i in list_function_response])
        upsert_chat_history(session_id=session_id, db=db, messages=list_chat_hist)


async def stream_response_async_test(
    request: ChatRequest, db: Session
) -> AsyncGenerator[str, None]:
    """
    Calls the LLM backend, gets a full response, and then simulates streaming.
    Use this if your LLM backend DOES NOT support streaming.
    """
    temp_text = "# Header 1\n**Lorem ipsum** _dolor sit amet_, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n\n* List item 1\n* List item 2\n\n[Link to Google](https://www.google.com)"
    for chunk in temp_text.split():
        yield chunk + " "
        await asyncio.sleep(0.05)  # Simulate processing time
