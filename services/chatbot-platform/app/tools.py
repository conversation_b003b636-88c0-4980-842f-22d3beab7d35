import ast
import asyncio
import json
import logging
import re
from datetime import datetime
from textwrap import dedent
from typing import Any, Dict, List, Optional, Union
from openai import AsyncStream
from openai.types.chat.chat_completion_message_tool_call import (
    ChatCompletionMessageToolCall,
)
from pydantic import BaseModel
from src.consts import settings
from src.core import llm_async_client
from src.planning.activity import activities
from src.planning.config import DEFAULT_TYPE_PLAN
from src.planning.core import ActivityProviderAgent, TripPlannerAgent, AgentMessage
from src.planning.utils import (
    slugify,
    compute_zone_distance_matrix,
    to_int,
    format_plan_as_list,
    build_activity_prompt_without_planning,
)
from src.rag.core import rag_engine
from src.rag.index import knowledge_tree, node_url
from src.request_session import async_request_call
from src.utils import convert_data_into_str

from .config import app_config
from .config import settings as app_settings

logger = logging.getLogger(__name__)
timezone = "Asia/Ho_Chi_Minh"


class FunctionCall(BaseModel):
    arguments: str
    name: str


class FunctionResponse(BaseModel):
    tool_call_id: str
    content: str
    role: str = "tool"


class InternalFunctionResponse(BaseModel):
    tool_call_id: str
    fn_name: str
    fn_args: Dict
    fn_response: Any


async def temp_async(query: str):
    text = "Hello, how are you?" + query

    for chunk in text.split():
        yield chunk + " "
        await asyncio.sleep(0.05)  # Simulate processing time


def extract_urls(text):
    url_pattern = r'https?://[^\s<>"]+|www\.[^\s<>"]+'
    return re.findall(url_pattern, text)


async def summary_content_async(query: str):
    url = extract_urls(query)
    if not url:
        return "Không có URL nào trong câu hỏi"
    url = url[0]
    if url not in node_url:
        return "Không có thông tin về vấn đề này trong dữ liệu được cung cấp"
    node_idx = node_url[url]
    node = knowledge_tree.get_node(node_idx[0])
    prompt_context = f"""Given a specific question and relevant context, provide a detailed answer and in-depth analysis.
                Please answer in **ONLY Vietnamese language**.
                ----------------------------
                > Question: {query}
                > Context:
                >>>
                {node.text}
                >>>
                Question:"""
    response = await llm_async_client.chat.completions.create(
        model=settings.openai_small_model,
        messages=[
            {
                "role": "system",
                "content": "You are a helpful assistant. Your working language is Vietnamese.",
            },
            {"role": "user", "content": prompt_context},
        ],
        max_tokens=1000,
    )
    return response.choices[0].message.content.strip()


async def get_weather_async(
    location: str = "Đà Nẵng, Việt Nam", current_time: Optional[str] = None
) -> str:
    if not current_time:
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")

    logger.debug(f"Extracted location: {location}, time: {current_time}")

    headers = {"User-Agent": "Mozilla/5.0"}
    latitude = "20.9370037"  # Default to Đà Nẵng
    longitude = "106.3295009"
    location_data = await async_request_call(
        method="GET",
        url=f"https://nominatim.openstreetmap.org/search?q={location}&format=json",
        headers=headers,
    )
    if location_data is None:
        return "Rất tiếc, không thể lấy thông tin về vấn đề này."
    if location_data:
        latitude = location_data[0]["lat"]
        longitude = location_data[0]["lon"]

    logger.debug(f"Location coordinates: latitude={latitude}, longitude={longitude}")
    params = {
        "latitude": latitude,
        "longitude": longitude,
        "daily": "temperature_2m_mean,uv_index_max,precipitation_sum,precipitation_probability_max",
        "current": "temperature_2m,relative_humidity_2m,precipitation,cloud_cover,wind_speed_10m",
        "timezone": timezone,
        "past_days": 1,
        "forecast_days": 7,
    }
    weather_api_url = "https://api.open-meteo.com/v1/forecast"
    weather_data = await async_request_call(
        method="GET", url=weather_api_url, headers=headers, params=params, timeout=10
    )
    if not weather_data:
        return "Rất tiếc, không thể lấy thông tin thời tiết tại thời điểm này."
    if "timezone" in weather_data:
        del weather_data["timezone"]

    return dedent(
        f"""Information about the weather:
        > Location: {location}
        > Date: {current_time}
        {weather_data}"""
    )


async def answer_hospitality_info_async(
    query: str,
    location: str,
    category: Optional[str] = None,
) -> List[Dict]:
    """
    Get hospitality information based on the query, location, and categories.
    Args:
        query (str): User query or question.
        location (str): The location for which to get hospitality information.
        category (str, optional): The category of hospitality activities.
        language (str): The language for the response. Default is "Tiếng Việt".
        top_k (int, optional): The number of top activities to return. Default is 10.

    Returns:
        List[Dict]: A list of dictionaries containing hospitality information.

    Example:
    get_hospitality_info(
        query="Tôi muốn nhà hàng ngon lúc 6h tối vẫn hoạt động",
        location="Grand World Phú Quốc",
        category="Nhà hàng",
    )
    """
    site = activities.get_activity_by_site(location)
    categories = {a.a_type for a in site if a.a_type}
    if not category or category not in categories:
        return []

    filtered_acts = site.get_activity_by_type(category)
    if getattr(filtered_acts, "activity_num", len(filtered_acts.activities)) == 0:
        return []

    compact = [
        {
            "activity_id": a.a_id,
            "a_zone": a.a_zone,
            "name": a.a_names.get("Tiếng Việt", ""),
            "suggested": a.a_plan_property_suggested,
            "accept_level": a.a_detail_property_level,
            "status": a.a_detail_property_status,
            "age_require": a.a_detail_property_age_regulation,
            "a_description": a.a_descriptions.get("Tiếng Việt", ""),
            "a_note": a.a_detail_property_note.get("Tiếng Việt", ""),
            "time_range_mon": a.a_detail_property_time_range.get("Thứ 2", ""),
            "poi": (
                to_int(a.a_detail_property_duration_overall)
                + to_int(a.a_plan_property_waiting_time)
                + to_int(a.a_plan_property_travel_time)
                + to_int(a.a_plan_property_travel_time_after)
            ),
        }
        for a in filtered_acts
    ]

    data_payload = json.dumps(compact, ensure_ascii=False, indent=2)

    system_prompt = build_activity_prompt_without_planning(
        activity_type=category,
        user_query=query,
        activities=data_payload,
    )

    response = await llm_async_client.chat.completions.create(
        model=settings.openai_small_model,
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": query},
        ],
        temperature=0.1,
        max_tokens=2000,
        stream=True,
    )

    return response


# async def make_plan_async(
#     query: str,
#     location: str,
#     time_block: str = DEFAULT_TYPE_PLAN,
#     day_of_week: str = "Thứ 2",
# ) -> asyncio.Future:
#     """
#     Asynchronous entrypoint for trip planning. Returns an awaitable.
#     """
#     return await generate_plan(query, location, time_block, day_of_week)


async def make_plan_async(
    query: str,
    location: str,
    type_plan: str = DEFAULT_TYPE_PLAN,
    day_of_week: str = "Thứ 2",
):
    top_k: int = 10

    unique_zones = {a.a_zone for a in activities.get_activity_by_site(location)}

    providers = {
        t: ActivityProviderAgent(f"{slugify(t)}_agent", t, activities, location)
        for t in unique_zones
    }

    distance_matrix, zones_list = compute_zone_distance_matrix(activities, location)

    # 2-b. Gọi bất đồng bộ từng provider
    async def _call_provider(a_zone, agent):
        res = await agent.select_activities(
            user_query=query,
            time_block=type_plan,
            top_k=top_k,
            day_of_week=day_of_week,
        )
        return a_zone, res.get("activity_ids", [])

    tasks = [asyncio.create_task(_call_provider(t, ag)) for t, ag in providers.items()]
    gathered = await asyncio.gather(*tasks, return_exceptions=True)

    activity_results = {}
    for item in gathered:
        if isinstance(item, Exception):
            continue
        else:
            activity_results[item[0]] = item[1]
    if not activity_results:
        raise RuntimeError("Không tìm thấy hoạt động phù hợp.")

    # 2-c. Gọi TripPlannerAgent
    planner = TripPlannerAgent("trip_planner", activities, zones_list, distance_matrix)
    msg = AgentMessage(
        sender="pipeline",
        receiver="trip_planner",
        message_type="CREATE_TRIP_PLAN",
        payload={
            "activity_results": activity_results,
            "user_query": query,
            "time_block": type_plan,
            "day_of_week": day_of_week,
        },
    )
    resp = await planner.process_message(msg)
    if resp.message_type != "TRIP_PLAN_RESULT":
        raise RuntimeError(resp.payload.get("error", "Trip planner error"))

    plan_json = resp.payload
    table_txt = format_plan_as_list(activities, plan_json)
    logger.info(f"Plan:\n{table_txt}")
    return table_txt


tools_llm = [
    {
        "type": "function",
        "function": {
            "name": "rag_answer",
            "description": dedent(
                """Searches and extracts insights from Vingroup details (Vinhomes, Vinpearl, Vinwonders, VinUni, VinFast, etc.).
                    Use this function for most of general query or when the user asks general about Vingroup-related questions."""
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "User query or question.",
                    },
                    "has_multiple_requirements": {
                        "type": "boolean",
                        "description": dedent(
                            """If the user query/question has multiple requirements, set `has_multiple_requirements` to `True`.
                            Otherwise, set `has_multiple_requirements` to `False`.

                            Example 1:
                                `query` = "Địa chỉ của Vinhomes West Point ở đâu và có quy định gì đặc biệt về nhân sự ở đây liên quan đến nghỉ việc?"
                                `has_multiple_requirements` = True

                                **Explanation**:
                                    - requirement 1 is "Địa chỉ của Vinhomes West Point ở đâu"
                                    - requirement 2 is "có quy định gì đặc biệt về nhân sự ở đây liên quan đến nghỉ việc?"
                            Example 2:
                                `query` = "Các chỉ tiêu KPI trong bộ phận Vườn ươm và Khối Công nghệ của Vinhomes có điểm gì giống và khác nhau?"
                                `has_multiple_requirements` = True

                                **Explanation**:
                                    - requirement 1 is "Các chỉ tiêu KPI trong bộ phận Vườn ươm"
                                    - requirement 2 is "Các chỉ tiêu KPI trong bộ phận Khối Công nghệ của Vinhomes"
                            Example 3:
                                `query` = "Tôi là đại lý Leasing, trong trường hợp cần hỗ trợ kỹ thuật dịp Tết (ví dụ lỗi VPN, email) thì liên hệ ai và phạm vi hỗ trợ gồm những gì?"
                                `has_multiple_requirements` = False

                                **Explanation**: User only needs to provide the information of contact information about VPN, email, etc. issues.
                            ."""
                        ),
                    },
                    "filters": {
                        "type": "string",
                        "description": dedent(
                            """Use this parameter when user mentions specific Vingroup venues (e.g. Vinwonders Nam Hội An, Vinpearl Aquarium, etc.). (Default is None)

                            + The filter type: `should`
                            + Filters contain following keys:
                                - `metadata.location`: List of location ["Hà Nội", "Hồ Chí Minh", "Nghệ An", "Hội An", "Nha Trang", "Phú Quốc"] which might be mentioned in the user query.
                                - `metadata.venue`: List of venue mentioned in the user query, eg. ['VinWonders Nam Hội An', 'Vinpearl Aquarium'].
                                - `metadata.type`:
                                    * Atleast one of the following types: ["promotion", "general"].
                                    * Default are ["general"] for those questions if user doesn't mention any type.
                                    * Promotion-related questions can be used both `general` and `promotion`, eg., ["general", "promotion"].

                            Example 1 (User mentions “Vinpearl Nam Hội An”):
                            The filter should include:
                            - `metadata.location`: ['Nam Hội An']
                            - `metadata.venue`: ['Vinpearl Nam Hội An']
                            {
                                "filter": {
                                    "should": [
                                        {"key": "metadata.location", "match": {"any": ['Hội An']}},
                                        {"key": "metadata.venue", "match": {"any": ['Vinpearl Nam Hội An']}},
                                        {"key": "metadata.type", "match": {"any": ['general']}}
                                    ],
                                }
                            }

                            Example 2 (User mentions promotion, coupon at Vinpearl Nam Hội An, Vinwonders Nha Trang, etc.):
                            {
                                "filter": {
                                    "should": [
                                        {"key": "metadata.location", "match": {"any": ['Hội An', 'Nha Trang']}},
                                        {"key": "metadata.venue", "match": {"any": ['Vinpearl Nam Hội An', 'Vinwonders Nha Trang']}},
                                        {"key": "metadata.type", "match": {"any": ['general', 'promotion']}},
                                    ]
                                }
                            }
                            """
                        ),
                    },
                },
                "required": ["query", "has_multiple_requirements", "filters"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "answer_hospitality_info",
            "description": dedent(
                """Use this function to lookup hospitality-ralated information about Vingroup venues (e.g. restaurant, shopping, entertainment, etc.) when the user EXPLICITLY mention location and category of Vingroup venue.

Use this when the user want to get information about on-site dining, shopping, entertainment or other services at a specific Vingroup venue.
• location must exactly match one of our sites (e.g. “Công viên Grand Park”, “Grand World Phú Quốc”, “VinWonders Phú Quốc”, etc.).
• category must be one valid type at that location (e.g. “Nhà hàng”, “Trò chơi”, “Mua sắm”, etc.).

**Examples**
User: Tôi muốn nhà hàng ngon lúc 6h tối vẫn hoạt động ở Vinpearl Nha Trang
User: Liệt kê những trò chơi cảm giác mạnh ở Grand World Phú Quốc
User: Có những đặc sản hoặc quà lưu niệm nào ở VinWonders Nam Hội An
"""
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "User query or question related to Vingroup hospitality services.",
                    },
                    "location": {
                        "type": "string",
                        "description": dedent(
                            """Location from user query, must be exactly the same as the locations in the following list:
                                - Công viên Grand Park
                                - Grand World Phú Quốc
                                - Tắm bùn Hòn Tằm
                                - VinKE & Aquarium Times City
                                - VinWonders Cửa Hội
                                - VinWonders Nam Hội An
                                - VinWonders Nha Trang
                                - VinWonders Phú Quốc
                                - VinWonders Wave Park & Water Park
                                - Vinpearl Harbour Nha Trang
                                - Vinpearl Safari Phú Quốc
                            """
                        ),
                    },
                    "category": {
                        "type": "string",
                        "description": dedent(
                            """(Optional, default is None) Extract the category from the user's query intent. Just pick one category and it must be exactly the same as the categories in this dict with location:.
                            {'VinKE & Aquarium Times City': ['Trò chơi', 'Mua sắm', 'Cảnh quan', 'Nhà hàng', 'Sự kiện', 'Tiện ích'],
                            'Tắm bùn Hòn Tằm': ['Nhà hàng'],
                            'VinWonders Wave Park & Water Park': ['Trò chơi', 'Nhà hàng', 'Tiện ích'],
                            'VinWonders Nha Trang': ['Trò chơi', 'Mua sắm', 'WC', 'Cảnh quan', 'Y tế', 'Nhà hàng', 'Sự kiện', 'Tiện ích'],
                            'Công viên Grand Park': ['Trò chơi', 'WC', 'Quầy thông tin', 'Quầy giữ đồ', 'Cảnh quan', 'Nhà hàng', 'Sự kiện'],
                            'VinWonders Cửa Hội': ['Trò chơi', 'Mua sắm', 'Cảnh quan', 'Nhà hàng', 'Sự kiện'],
                            'VinWonders Phú Quốc': ['Trò chơi', 'Điểm checkin', 'Mua sắm', 'Quầy thông tin', 'Cảnh quan', 'Nhà hàng', 'Sự kiện', 'Tiện ích'],
                            'VinWonders Nam Hội An': ['Trò chơi', 'Điểm checkin', 'Trạm xe bus nội khu', 'Mua sắm', 'WC', 'Quầy thông tin', 'Quầy giữ đồ', 'Khu đỗ xe (của khách)', 'Quầy chụp ảnh', 'Cảnh quan', 'ATM', 'Y tế', 'Nhà hàng', 'Sự kiện', 'Trạm xe buggy', 'Tiện ích'],
                            'Vinpearl Harbour Nha Trang': ['Trò chơi', 'Điểm checkin', 'Mua sắm', 'Quầy thông tin', 'WC', 'Y tế', 'Nhà hàng', 'Sự kiện', 'Trạm xe buggy'],
                            'Grand World Phú Quốc': ['Trò chơi', 'Trạm xe bus nội khu', 'Mua sắm', 'WC', 'Quầy thông tin', 'Khu đỗ xe (của khách)', 'Cảnh quan', 'Nhà hàng', 'Sự kiện'],
                            'Vinpearl Safari Phú Quốc': ['Trạm xe bus nội khu', 'Mua sắm', 'Cảnh quan', 'Y tế', 'Nhà hàng', 'Sự kiện', 'Trạm xe buggy', 'Tiện ích']}
                            """
                        ),
                    },
                },
                "required": ["query", "location", "category"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "summary_content",
            "description": dedent(
                """Searches and extracts insights from Vinhomes wiki knowlege (policy, wiki, news). Require user provide specific URL.
                        Use this function for most of general query or when the user asks general about specific Vinhome url."""
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "User query or question that contains URL.",
                    },
                },
                "required": ["query"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": dedent(
                """Retrieves weather information for a specific location and time.
                Use this function when the user asks about the weather."""
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": dedent(
                            """Your task is to extract the **location** from the user query.
                            Instructions:
                            1. If the location is **not provided**, return: `"Đà Nẵng, Việt Nam"`.
                            2. If the location **is provided**, return it in the format: `"[Location], [Country]"`.
                            - Example:
                            + "Hà Nội" → "Hà Nội, Việt Nam"
                            + "Texas" → "Texas, United States"
                            """
                        ),
                    },
                    "current_time": {
                        "type": "string",
                        "description": dedent(
                            """Your task is to extract the **time** information from the user query.
                            Instructions:
                            1. If the time **is provided**, return it in ISO 8601 format, for example: `"2025-05-01T12:00:00Z"`.
                            2. If the time **is not provided** or the user mentions phrases like "hôm nay" (today), "bây giờ" (now), or similar, use None for the default value.
                            """
                        ),
                    },
                },
                "required": ["location", "current_time"],
                "additionalProperties": False,
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "make_plan",
            "description": dedent(
                """Use this function when the user intent to make a trip plan or itinerary.

Generates a trip plan based on the user's query, location, time block, and day of the week.
Use this function when the user asks for a trip plan or itinerary.

**Examples**
User: Gợi ý lịch trình tham quan Vinwonders Nam Hội An
User: Lập kế hoạch đi chơi Vinpearl Nha Trang trong buổi sáng
"""
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "User query or question for trip planning.",
                    },
                    "location": {
                        "type": "string",
                        "description": dedent(
                            """Your task is to extract the **location** from the user query.
                            **location** must be exactly the same as the locations in this list
                            Unique locations:
                                - Công viên Grand Park
                                - Grand World Phú Quốc
                                - Tắm bùn Hòn Tằm
                                - VinKE & Aquarium Times City
                                - VinWonders Cửa Hội
                                - VinWonders Nam Hội An
                                - VinWonders Nha Trang
                                - VinWonders Phú Quốc
                                - VinWonders Wave Park & Water Park
                                - Vinpearl Harbour Nha Trang
                                - Vinpearl Safari Phú Quốc
                            """
                        ),
                    },
                    "type_plan": {
                        "type": "string",
                        "enum": ["full_plan", "first_half", "second_half"],
                        "description": dedent(
                            """Your task is to extract the **type plan** from the user query.
                            Instructions:
                            - If the type plan is **not provided**, return: `"full_plan"`.
                            - `full_plan` is time between 09:00 - 19:00.
                            - `first_half` is time between 09:00 - 12:00.
                            - `second_half` is time between 12:00 - 19:00.
                            - If user out of time range, return `full_plan`.
                            Example:
                            + "Tôi muốn đi du lịch một ngày từ 9h sáng đến 6h chiều" → `"full_plan"`
                            + "Tôi muốn đi du lịch từ 8h sáng đến 12h" → `"first_half"`
                            """
                        ),
                    },
                    "day_of_week": {
                        "type": "string",
                        "description": dedent(
                            """Your task is to extract the **day of week** from the user query.
                            Instructions:
                            1. If the day of week is **not provided**, return: `"Thứ 2"`.
                            2. If the day of week **is provided**, return it in the format: `"[day_of_week]"`.
                            - Example:
                            + "Tôi muốn đi du lịch vào thứ 3" → `"Thứ 3"`
                            + "Tôi muốn đi du lịch vào thứ 7" → `"Thứ 7"`
                            """
                        ),
                    },
                },
            },
        },
    },
]


mapping_async_tools = {
    "rag_answer": rag_engine.answer_query_async,
    "summary_content": summary_content_async,
    "get_weather": get_weather_async,
    "answer_hospitality_info": answer_hospitality_info_async,
    "make_plan": make_plan_async,
}

nested_llm_function = [
    "rag_answer",
    "answer_hospitality_info",
]

function_require_query = [
    "rag_answer",
    "summary_content",
    "answer_hospitality_info",
    "make_plan",
]


def get_available_tools(app_name: str) -> List[str]:
    tool_names = app_config[app_name].tools
    available_tools = []
    for tool in tools_llm:
        if tool["function"]["name"] in tool_names:
            available_tools.append(tool)
    return available_tools


async def process_list_function_call(
    tool_calls: List[ChatCompletionMessageToolCall],
    user_prompt,
) -> List[InternalFunctionResponse]:
    list_function_response = []
    cache_function_call = {}
    for tool_call in tool_calls:
        function_name = tool_call.function.name
        logger.info(f"FUNCTION_ARGS_FROM_TOOL_CALL: {tool_call.function.arguments}")
        function_args = json.loads(tool_call.function.arguments)
        # Nested json
        if isinstance(function_args, str):
            function_args = ast.literal_eval(function_args)
        if function_name in function_require_query:
            function_args["query"] = user_prompt
            # check rag_answer disable filtering
            if (
                function_name == "rag_answer"
                and app_config[app_settings.app_name].disable_rag_filter
            ):
                function_args["filters"] = None

        logger.info(f"FUNCTION_ARGS: {function_args}")
        key = f"{function_name}_{function_args}"
        selected_tool = mapping_async_tools[function_name]
        if key in cache_function_call:
            function_response = cache_function_call[key]
        else:
            function_response = selected_tool(**function_args)

        list_function_response.append(
            InternalFunctionResponse(
                fn_name=function_name,
                fn_args=function_args,
                fn_response=function_response,
                tool_call_id=tool_call.id,
            )
        )
        cache_function_call[key] = function_response
    return list_function_response


async def process_function_result_async(
    list_function_response: List[InternalFunctionResponse],
):
    responses = await asyncio.gather(
        *[i.fn_response for i in list_function_response], return_exceptions=True
    )

    # Create result list, handling None responses and potential exceptions
    result: List[InternalFunctionResponse] = []
    for fn_resp, response in zip(list_function_response, responses):
        if isinstance(response, Exception):
            # Handle exceptions during the await
            logger.exception(f"Error in function {fn_resp.fn_name}: {response}")
            result.append(
                InternalFunctionResponse(
                    tool_call_id=fn_resp.tool_call_id,
                    fn_name=fn_resp.fn_name,
                    fn_args=fn_resp.fn_args,
                    fn_response=None,  # Set response to None on error
                )
            )
        elif response is None:
            result.append(
                InternalFunctionResponse(
                    tool_call_id=fn_resp.tool_call_id,
                    fn_name=fn_resp.fn_name,
                    fn_args=fn_resp.fn_args,
                    fn_response=None,  # Keep response as None
                )
            )
        else:
            result.append(
                InternalFunctionResponse(
                    tool_call_id=fn_resp.tool_call_id,
                    fn_name=fn_resp.fn_name,
                    fn_args=fn_resp.fn_args,
                    fn_response=response,
                )
            )

    return result


async def generate_response_from_function_call_async(
    list_function_call: List[ChatCompletionMessageToolCall],
    user_prompt: str,
) -> Union[List[FunctionResponse], AsyncStream]:
    list_function_response = await process_list_function_call(
        list_function_call, user_prompt
    )

    # Check if the function is nested llm function
    for i in list_function_response:
        if i.fn_name in nested_llm_function:
            return await i.fn_response

    list_function_response = await process_function_result_async(
        list_function_response=list_function_response
    )
    result: List[FunctionResponse] = []
    for item in list_function_response:
        function_response = item.fn_response
        result.append(
            FunctionResponse(
                tool_call_id=item.tool_call_id,
                content=convert_data_into_str(function_response),
            )
        )
    return result
