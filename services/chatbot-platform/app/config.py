from pydantic_settings import BaseSettings
from omegaconf import OmegaConf
import os


class Settings(BaseSettings):
    app_name: str  # Name of the application, must be as defined in the app config file
    admin_username: str
    admin_password: str


settings = Settings()

try:
    app_config = OmegaConf.load(
        os.path.join(os.path.dirname(__file__), "cfgs", "config.yaml")
    )
except Exception as e:
    raise ValueError(f"Error loading app config file: {e}")
