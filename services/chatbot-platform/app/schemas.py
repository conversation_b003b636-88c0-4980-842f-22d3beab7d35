from pydantic import BaseModel
from typing import Optional


class ChatMessage(BaseModel):
    role: str  # "user" or "assistant"
    content: str


class ChatRequest(BaseModel):
    message: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None  # For maintaining context if needed


class ChatHistory(BaseModel):
    id: int
    session_id: str
    sender: str  # "user" or "assistant"
    message: str
    timestamp: int


class ChatHistoryResponse(BaseModel):
    messages: list[ChatHistory]


class LLMStreamRequest(BaseModel):  # Example schema for your LLM backend
    prompt: str
    # Add other parameters your LLM backend expects, e.g., max_tokens, temperature
    stream: bool = True
