import os
from sqlalchemy import (
    create_engine,
    event,
)
import json
import uuid
from sqlalchemy import Column, DateTime, Integer, String, Text, ForeignKey

from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import List, Dict, Any
from datetime import datetime, timezone
from src.consts import settings


database_url = settings.database_url
engine = create_engine(database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def lazy_utc_now():
    return datetime.now(tz=timezone.utc)


@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    cursor = dbapi_connection.cursor()
    if not os.getenv("DEVELOPMENT"):
        cursor.execute("PRAGMA journal_mode = WAL;")
        cursor.execute("PRAGMA wal_autocheckpoint = 50;")
        cursor.execute("PRAGMA busy_timeout = 5000;")
        cursor.execute("PRAGMA synchronous = NORMAL;")
        cursor.execute("PRAGMA temp_store = MEMORY;")
        cursor.execute("PRAGMA foreign_keys=ON;")
    cursor.close()


class User(Base):
    __tablename__ = "user"

    id = Column(
        Integer, primary_key=True, autoincrement=False
    )  # User id as primary key
    chat_id = Column(Integer, nullable=False)
    username = Column(String, default="")
    first_name = Column(String, default="")
    last_name = Column(String, default="")
    last_interaction = Column(DateTime, default=lazy_utc_now)
    first_seen = Column(DateTime, default=lazy_utc_now)


class ChatHistory(Base):
    __tablename__ = "chat_history"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, index=True, unique=True)
    create_date = Column(DateTime, default=lazy_utc_now)
    last_update = Column(DateTime, default=lazy_utc_now)

    content = Column(Text, default="[]")


class Dialog(Base):
    __tablename__ = "dialog"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    start_time = Column(DateTime, default=lazy_utc_now)
    messages = Column(Text, default="[]")


# Create tables
Base.metadata.create_all(bind=engine)


def get_chat_history(session_id: str, db: Session) -> List[Dict[str, Any]]:
    record = db.query(ChatHistory).filter_by(session_id=session_id).first()
    return [] if not record else json.loads(record.content)


def upsert_chat_history(
    session_id: str, messages: List[Dict[str, Any]], db: Session, existing_update=False
) -> None:
    msgs = [m for m in messages if m.get("role") != "visualize"]
    content = json.dumps(msgs)
    record = db.query(ChatHistory).filter_by(session_id=session_id).first()
    if record:
        if existing_update:
            exist_content = json.loads(record.content)
            exist_content.extend(msgs)
        else:
            exist_content = msgs
        record.content = json.dumps(exist_content)
        record.last_update = lazy_utc_now()
    else:
        record = ChatHistory(session_id=session_id, content=content)
        db.add(record)
    db.commit()
