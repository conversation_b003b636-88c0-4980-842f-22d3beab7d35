import json
import logging
import os
import secrets
from typing import Any
from openai import OpenAIError
import openai

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON>PBasic, HTTPBasicCredentials
from pydantic import BaseModel
from sqlalchemy.orm import Session

from .config import settings, app_config
from .core import stream_response_async, stream_response_async_test
from .db.session import ChatHistory as ChatHistoryModel
from .db.session import SessionLocal, upsert_chat_history
from .schemas import ChatHistory, ChatHistoryResponse, ChatRequest

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()
security = HTTPBasic()
origins = ["http://localhost", "http://127.0.0.1", "http://localhost:3001"]


app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class SSEEventMessage(BaseModel):
    content: Any


class SSEEvent(BaseModel):
    event: str
    data: SSEEventMessage

    def serialize(self):
        return f"data: {self.data.model_dump_json()}\n\n"


# Dependency to get the database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def verify_credentials(creds: HTTPBasicCredentials = Depends(security)) -> str:
    if not (
        secrets.compare_digest(creds.username, settings.admin_username)
        and secrets.compare_digest(creds.password, settings.admin_password)
    ):
        raise HTTPException(
            status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return creds.username


async def event_stream(request: ChatRequest, db: Session):
    # async for token in stream_response_async_test(request, db):
    #     yield f"data: {token}"
    buffer_lenght = 20
    buffer = ""
    if os.getenv("FAKE_MESSAGES"):
        async for token in stream_response_async_test(request, db):
            buffer += token
            if len(buffer) > buffer_lenght:
                event = SSEEvent(event="message", data=SSEEventMessage(content=buffer))
                yield event.serialize()
                buffer = ""
        if buffer:
            event = SSEEvent(event="message", data=SSEEventMessage(content=buffer))
            yield event.serialize()
        yield "data: [DONE]"
        return
    total_msg = ""
    no_answer = False

    try:
        r_gen, first_char = await stream_response_async(request, db)
        if first_char.strip():
            event = SSEEvent(event="message", data=SSEEventMessage(content=first_char))
            yield event.serialize()
        async for token in r_gen:
            if not token.choices:
                continue
            if not (delta_content := token.choices[0].delta.content):
                continue
            total_msg += delta_content
            buffer += delta_content
            if "<NOANS>" in total_msg:
                no_answer = True
                buffer = buffer.replace("<NOANS>", "")
                total_msg = total_msg.replace("<NOANS>", "")

            if len(buffer) > buffer_lenght:
                event = SSEEvent(event="message", data=SSEEventMessage(content=buffer))
                yield event.serialize()
                buffer = ""
        if buffer.strip():
            event = SSEEvent(event="message", data=SSEEventMessage(content=buffer))
            yield event.serialize()
        if (
            hasattr(r_gen, "ref_urls")
            and not no_answer
            and not app_config[settings.app_name].disable_ref_urls
        ):
            remain_msg = "\n**Tham khảo:**"
            for url in r_gen.ref_urls:
                remain_msg += f"- {url}"
            total_msg += remain_msg
            event = SSEEvent(event="message", data=SSEEventMessage(content=remain_msg))
            yield event.serialize()

    except OpenAIError as e:
        logger.error(f"OpenAIError: {e}")
        if "content filtering policies" in str(e):
            content = "Câu hỏi của bạn vi phạm policy, hãy thử lại câu hỏi khác"
        else:
            content = "Đã xảy ra lỗi xảy ra, hãy thử lại sau."
        event = SSEEvent(event="message", data=SSEEventMessage(content=content))
        total_msg = content
        yield event.serialize()
    except openai.ContentFilterFinishReasonError as e:
        logger.error(f"ContentFilterFinishReasonError: {e}")
        content = "Câu hỏi của bạn vi phạm policy, hãy thử lại câu hỏi khác"
        event = SSEEvent(event="message", data=SSEEventMessage(content=str(e)))
        total_msg = content
        yield event.serialize()

    assistant = {"role": "assistant", "content": total_msg}
    upsert_chat_history(request.session_id, [assistant], db, existing_update=True)

    yield "data: [DONE]"


@app.get("/")
async def health_check():
    return {"status": "ok"}


@app.post("/chat/stream")
async def chat_stream(
    req: ChatRequest,
    db: Session = Depends(get_db),
    username: str = Depends(verify_credentials),
):
    return StreamingResponse(
        event_stream(req, db),
        media_type="text/event-stream",
    )


@app.get("/chat/history")
async def chat_history(
    sessionId: str,
    db: Session = Depends(get_db),
    username: str = Depends(verify_credentials),
):
    record = db.query(ChatHistoryModel).filter_by(session_id=sessionId).first()
    if not record:
        return []
    list_chat_history = json.loads(record.content)
    result: list[ChatHistory] = []
    idx = 0
    timestamp = int(record.last_update.timestamp())
    list_prefix_user_msg = ["**Question:**", "**Context:**"]
    for i in list_chat_history:
        if i["role"] == "user":
            content = i["content"]
            for prefix in list_prefix_user_msg:
                content = content.replace(prefix, "").strip()

            result.append(
                ChatHistory(
                    sender="user",
                    message=content,
                    session_id=sessionId,
                    id=idx,
                    timestamp=timestamp,
                )
            )
            idx += 1
        elif i["role"] == "assistant":
            if "tool_calls" in i:
                continue
            if not i.get("content"):
                continue
            result.append(
                ChatHistory(
                    sender="bot",
                    message=i["content"],
                    session_id=sessionId,
                    id=idx,
                    timestamp=timestamp,
                )
            )
            idx += 1
    return ChatHistoryResponse(messages=result)


if __name__ == "__main__":
    # This is for running directly, e.g., python -m app.main
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
