{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bf02713e", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b052738cec54fbe8099c625e5a73c13", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 30 files:   0%|          | 0/30 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import sys\n", "sys.path.append(\"../\")\n", "sys.path.append(\"../../\")\n", "\n", "import json\n", "import numpy as np\n", "from datetime import datetime\n", "import torch\n", "from pprint import pprint\n", "from tqdm import tqdm\n", "from src.data.tree import KnowledgeTree, Node, NodeType\n", "from qdrant_client import QdrantClient\n", "from FlagEmbedding import BGEM3FlagModel\n", "import logging\n", "import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "\n", "load_dotenv(find_dotenv())\n", "\n", "logging.basicConfig(level=logging.CRITICAL)\n", "\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/src/tree_structure_15051305_with_summary.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/src/tree_structure_15051305_with_summary_faqs_v2_adding_url.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_10271505.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_11431505.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_09081705.json\"\n", "DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_09081905.json\"\n", "\n", "\n", "client_qdrant = QdrantClient(url=\"http://10.248.128.196:6333\")\n", "\n", "embedding_model = BGEM3FlagModel(\n", "    'BAAI/bge-m3',\n", "    use_fp16=True,\n", "    cache_dir='/mnt/disk1/duc_workspace/hf_models',\n", "    devices=['cuda:1']\n", ")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "65e6a04d", "metadata": {}, "outputs": [], "source": ["knowledge_tree = KnowledgeTree.from_dict(json.load(open(DATA_PATH, \"r\")))"]}, {"cell_type": "markdown", "id": "85e6dec2", "metadata": {}, "source": ["tree nodes' contents collection"]}, {"cell_type": "code", "execution_count": 3, "id": "31595843", "metadata": {}, "outputs": [], "source": ["from qdrant_client import models\n", "QDRANT_COLLECTION = \"vinhomes-wiki-dev-4000\"\n", "# client_qdrant.delete_collection(collection_name=QDRANT_COLLECTION)\n", "\n", "if not client_qdrant.collection_exists(collection_name=QDRANT_COLLECTION):\n", "    client_qdrant.create_collection(\n", "        collection_name=QDRANT_COLLECTION,\n", "        vectors_config={\n", "            \"content\": models.VectorParams(\n", "                size=1024,\n", "                distance=models.Distance.COSINE,\n", "            ),\n", "            \"question\": models.VectorParams(\n", "                size=1024,\n", "                distance=models.Distance.COSINE,\n", "                multivector_config=models.MultiVectorConfig(\n", "                    comparator=models.MultiVectorComparator.MAX_SIM\n", "                ),\n", "            ),\n", "            \"answer\": models.VectorParams(\n", "                size=1024,\n", "                distance=models.Distance.COSINE,\n", "                multivector_config=models.MultiVectorConfig(\n", "                    comparator=models.MultiVectorComparator.MAX_SIM\n", "                ),\n", "            ),\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": 4, "id": "cbdfe223", "metadata": {}, "outputs": [], "source": ["node_idxs, node_ctns = knowledge_tree.get_all_contents(return_indices=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "36a259f5", "metadata": {}, "outputs": [], "source": ["filtered = [(i, v) for i, v in zip(node_idxs, node_ctns) if v]\n", "node_indices, node_contents = zip(*filtered)"]}, {"cell_type": "code", "execution_count": 6, "id": "bf2a0c1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0, 1, 2, 3, 4)\n", "('Root Summary',\n", " '<PERSON><PERSON><PERSON> s<PERSON><PERSON> vệ Dữ liệu Cá nhân của Công ty cổ phần Đ<PERSON>u tư Xây dựng Thái '\n", " '<PERSON><PERSON><PERSON> quy định các khái niệm cơ bản về dữ liệu cá nhân, chủ thể dữ liệu và xử '\n", " 'lý dữ liệu, bao gồ<PERSON> thu thập, l<PERSON><PERSON> trữ, chỉnh sửa và bảo vệ dữ liệu theo pháp '\n", " 'luật Việt Nam. Công ty thu thập dữ liệu cá nhân qua nhiều nguồn như trang '\n", " 'web, h<PERSON><PERSON> đồ<PERSON>, trao đ<PERSON>i tr<PERSON><PERSON> tiế<PERSON>, m<PERSON><PERSON> x<PERSON> h<PERSON>, thi<PERSON><PERSON> bị <PERSON>, công ngh<PERSON> '\n", " 'tự động và các nguồn công khai, đồng thời lưu trữ dữ liệu tại Việt Nam với '\n", " 'thời gian phù hợp mục đích sử dụng. <PERSON><PERSON> liệu cá nhân được sử dụng cho nhiều '\n", " 'mụ<PERSON> đích như cung cấp s<PERSON>n ph<PERSON>m, d<PERSON><PERSON> v<PERSON>, hỗ trợ khách hàng, nâng cao chất '\n", " '<PERSON><PERSON><PERSON><PERSON>, ph<PERSON><PERSON> v<PERSON>, ti<PERSON><PERSON> thị, phò<PERSON> chống tội phạm và tuân thủ pháp '\n", " 'luật. <PERSON><PERSON><PERSON> ty cam kết bảo mật dữ liệu bằng các biện pháp kỹ thuật như mã '\n", " '<PERSON><PERSON><PERSON>, tư<PERSON><PERSON> lử<PERSON>, ki<PERSON><PERSON> soát truy cập và bảo vệ dữ liệu thẻ thanh toán theo '\n", " 'nguyên tắc không lưu trữ thông tin nhạy cảm. Dữ liệu cá nhân trẻ em chỉ được '\n", " 'xử lý khi có sự đồng ý của cha mẹ hoặc người giám hộ và tuân thủ quy định '\n", " 'ph<PERSON><PERSON> luật. <PERSON><PERSON><PERSON> ty không bán dữ liệu cá nhân, chỉ chia sẻ với các tổ chức '\n", " 'liên quan và cơ quan nhà nước theo quy định, đ<PERSON><PERSON> bảo an toàn khi chuyển dữ '\n", " 'liệu ra nước ngoài. Ch<PERSON> thể dữ liệu có quyền truy cập, chỉnh sửa, x<PERSON><PERSON>, phản '\n", " 'đối xử lý dữ liệu và các quyền kh<PERSON>c, đồng thời có nghĩa vụ bảo vệ dữ liệu cá '\n", " 'nhân và thông báo khi phát hiện sai sót. Công ty có thể cập nhật ch<PERSON>h s<PERSON>ch '\n", " 'và khuyến nghị người dùng kiểm tra thường xuyên. <PERSON><PERSON><PERSON> thắc mắc về bảo vệ dữ '\n", " 'li<PERSON><PERSON> có thể liên hệ <NAME_EMAIL>.',\n", " '<PERSON><PERSON><PERSON> s<PERSON><PERSON> vệ Dữ liệu Cá nhân của Công Ty quy định các khái niệm cơ bản '\n", " '<PERSON><PERSON><PERSON>, <PERSON><PERSON>, và <PERSON><PERSON>, bao '\n", " 'g<PERSON><PERSON> c<PERSON> ho<PERSON>t động như thu thập, l<PERSON><PERSON> trữ, chỉnh sửa và bảo vệ dữ liệu. Chủ '\n", " '<PERSON><PERSON><PERSON> Liệu C<PERSON>ân và người liên quan chịu trách nhiệm cung cấp thông tin '\n", " 'đ<PERSON><PERSON> đủ, hợ<PERSON> pháp và đồng ý cho việc xử lý dữ liệu theo quy định của chính '\n", " 'sách. <PERSON><PERSON> sử dụng sản ph<PERSON>m, dịch vụ hoặc ký kết hợp đồng với <PERSON>, Chủ '\n", " 'Thể Dữ Liệu Cá Nhân đồng ý với toàn bộ chính sách và các thay đổi có thể xảy '\n", " 'ra. <PERSON><PERSON><PERSON> có quyền cậ<PERSON>, sửa đổi ch<PERSON>h sách và đăng tải trên trang web '\n", " 'ch<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> nghị người dùng thường xuyên kiểm tra. Công Ty cam kết '\n", " 'tuân thủ pháp luật Việt Nam trong việc xử lý và bảo vệ dữ liệu, thu thập dữ '\n", " 'li<PERSON><PERSON> với mục đích rõ rà<PERSON>, áp dụng các biện pháp kỹ thuật để đảm bảo an toàn '\n", " 'dữ liệu, lưu trữ dữ liệu phù hợp và tuân thủ các quy định bảo vệ dữ liệu của '\n", " 'trẻ em.',\n", " '\\n'\n", " '- 1.1 <PERSON><PERSON> Liệu <PERSON> Nhân: là thông tin dưới dạng ký hiệu, chữ viế<PERSON>, chữ số, '\n", " '<PERSON><PERSON><PERSON>, âm thanh hoặc dạng tương tự trên môi trường điện tử gắn liền với '\n", " 'con người cụ thể hoặc gi<PERSON>p xác định con người cụ thể. <PERSON><PERSON> Liệu <PERSON> bao '\n", " 'gồ<PERSON>ân cơ bản và Dữ Liệu C<PERSON>ân nhạy cảm.\\n'\n", " '- Personal Data : means information in the form of symbols, letters, digits, '\n", " 'images, sounds, or similar forms in the electronic environment that are '\n", " 'associated with a specific natural person or help identify a specific '\n", " 'natural person. Personal Data includes basic Personal Data and sensitive '\n", " 'Personal Data.\\n'\n", " '- 1.2 <PERSON><PERSON> Thể Dữ Liệu Cá Nhân : là cá nhân được Dữ Liệu Cá N<PERSON>ân phản ánh, '\n", " 'bao gồm tất cả các khách hàng cá nhân đang sử dụng sản phẩm, dị<PERSON> vụ của '\n", " '<PERSON><PERSON><PERSON>, người lao động của Công Ty, cổ đông và/hoặc các cá nhân khác có '\n", " 'ph<PERSON>t sinh quan hệ pháp lý với Công Ty.\\n'\n", " '- Personal Data Subject : means the individual reflected by the Personal '\n", " \"Data, including all individual  customers  who  are  using  the  Company's  \"\n", " \"products  and  services,  the  Company's employees,  shareholders  and/or  \"\n", " 'other  individuals  who  have  a  legal  relationship  with  the Company.\\n'\n", " '- 1.3 <PERSON>ử Lý Dữ liệu Cá Nhân : là một hoặc nhiều hoạt động tác động tới Dữ '\n", " '<PERSON><PERSON><PERSON>, nh<PERSON>: thu thập, ghi, ph<PERSON> tích, <PERSON><PERSON><PERSON>, l<PERSON><PERSON> tr<PERSON>, chỉnh sửa, '\n", " 'c<PERSON><PERSON> k<PERSON>, k<PERSON><PERSON> h<PERSON>, tru<PERSON> c<PERSON><PERSON>, tru<PERSON> xu<PERSON>, thu hồ<PERSON>, mã hóa, gi<PERSON><PERSON> mã, sao ch<PERSON>p, '\n", " 'chia sẻ, t<PERSON><PERSON><PERSON><PERSON> đư<PERSON>, cung c<PERSON><PERSON>, chuyể<PERSON> giao, x<PERSON><PERSON>, h<PERSON><PERSON> <PERSON><PERSON> hoặc '\n", " 'các hành động khác có liên quan.\\n'\n", " '\\n'\n", " 'Personal Data Processing : means one or more activities affecting Personal '\n", " 'Data, such as: collection,  recording,  analysis,  confirmation,  storage,  '\n", " 'correction,  disclosure,  combination, access, retrieval, recalling, '\n", " 'encryption, decryption, copying, sharing, transmission, provision, transfer, '\n", " 'deletion, destruction of Personal Data or other related actions.\\n'\n", " '\\n'\n", " \"- 1.4 <PERSON><PERSON><PERSON>, hay '<PERSON><PERSON>h sách này' : là toàn bộ \"\n", " 'nội dung của ch<PERSON>h sách này do Công Ty soạn thảo và ban hành, bao gồm 11 Mục '\n", " 'và 61 tiểu mục một cách đầy đủ và toàn vẹn.\\n'\n", " '- Personal Data Protection Policy, or \"this Policy\": means the entire '\n", " 'content of this policy drafted and issued by the Company, including 11 '\n", " 'Sections and 61 sub-sections in full and complete.\\n'\n", " '- 1.5 <PERSON>hi <PERSON> Liệu C<PERSON>ân của người liên quan của Chủ Thể Dữ Liệu C<PERSON>hân '\n", " '(bao gồm nhưng không giới hạn ở thông tin của người phụ thuộc, người có liên '\n", " 'quan theo quy định ph<PERSON> lu<PERSON>, vợ/chồng, con và/hoặc cha mẹ và/hoặc người '\n", " '<PERSON><PERSON><PERSON><PERSON> h<PERSON>, b<PERSON><PERSON> b<PERSON>, bê<PERSON> thụ hưởng, ngườ<PERSON> đư<PERSON><PERSON> <PERSON>uy<PERSON>, đ<PERSON><PERSON> t<PERSON>, người liên hệ '\n", " 'trong các trường hợp khẩn cấp hoặc cá nhân khác của Chủ Thể Dữ Liệu Cá Nhân) '\n", " 'đ<PERSON><PERSON><PERSON> cung cấp cho <PERSON>, <PERSON><PERSON>ân và người liên quan của '\n", " 'Ch<PERSON> Thể Dữ Li<PERSON>u <PERSON>ân cam đoan, bảo đảm và chịu trách nhiệm rằng thông tin '\n", " 'đã được cung cấp đầy đủ và đã được Chủ Thể Dữ Liệu Cá Nhân đồng ý/chấp thuận '\n", " 'hợp pháp để được xử lý cho các mục đích được nêu tại Ch<PERSON>h sách này. Chủ Thể '\n", " 'Dữ Liệu Cá Nhân và người liên quan của Chủ Thể Dữ Liệu Cá Nhân đồng ý rằng '\n", " 'Công Ty không có trách nhiệm phải thẩm định về tính hợp pháp, hợ<PERSON> lệ của sự '\n", " 'đồng ý/chấp thuận này và việc lưu trữ bằng chứng chứng minh thuộc trách '\n", " 'nhiệm của người liên quan của Chủ Thể Dữ Liệu Cá Nhân và Chủ Thể Dữ Liệu Cá '\n", " 'Nhân. Công Ty được miễn trách và được yêu cầu bồi thường các thiệt hại, chi '\n", " 'phí liên quan khi Chủ Thể Dữ Liệu Cá Nhân hoặc/và người liên quan của Chủ '\n", " '<PERSON>h<PERSON> <PERSON> Liệu <PERSON>ân không thực hiện đúng nội dung quy định tại đây.\\n'\n", " '\\n'\n", " 'Whenever the Personal Data of any related person of the Personal Data '\n", " 'Subject (including but  not  limited  to  information  of  dependents,  '\n", " 'related  persons  in  accordance  with  the  law, spouse, children and/or '\n", " 'parents and/or guardians, friends, beneficiaries, authorized persons, '\n", " 'partners, emergency contacts or other individuals of the Personal Data '\n", " 'Subject) is provided to  the  Company, the  Personal Data Subject and the '\n", " 'related persons of the Personal Data Subject represent, warrant and take '\n", " 'responsibility that the information has been fully provided and has been '\n", " 'legally agreed/approved by the Personal Data Subject to be processed for the '\n", " 'purposes set out in this Policy. The Personal Data Subject and the related '\n", " 'persons of the Personal Data Subject agree that the Company is not '\n", " 'responsible for verifying the legality and validity of this consent/approval '\n", " 'and the storage of evidence proving that is the responsibility of  the  '\n", " 'related  persons  of  the  Personal  Data  Subject  and  the  Personal  '\n", " 'Data  Subject.  The Company is exempt from liability and entitled to request '\n", " 'compensation for related damages and expenses when the Personal Data Subject '\n", " 'or/and related persons of the Personal Data Subject fail to comply with the '\n", " 'contents specified herein.\\n'\n", " '\\n'\n", " '- 1.6 Bằng vi<PERSON><PERSON> đ<PERSON>, sử dụng các sản <PERSON>, dịch v<PERSON> c<PERSON><PERSON>, gia<PERSON> '\n", " 'hợp đồng và/hoặc cho phép Công Ty <PERSON>ử Lý <PERSON>, <PERSON><PERSON>ể <PERSON> '\n", " '<PERSON><PERSON><PERSON> chấp nhận toàn bộ và không kèm theo bất kỳ điều kiện nào đối với các '\n", " 'ch<PERSON><PERSON> sách được đề cập tại đây và các thay đổi (nếu có) trong từng thời kỳ.\\n'\n", " \"- By registering, using the Company's products and services, entering into \"\n", " 'contracts and/or allowing  the  Company  to  conduct  Personal  Data  '\n", " 'Processing,  the  Personal  Data  Subject\\n'\n", " '\\n'\n", " 'accepts in full and without any conditions to the policies mentioned herein '\n", " 'and the changes (if any) from time to time.',\n", " '- 1.7 <PERSON><PERSON><PERSON> s<PERSON>ch này có thể đư<PERSON><PERSON> cậ<PERSON>h<PERSON>, sử<PERSON> đổi, b<PERSON> sung hoặc '\n", " 'thay thế trong từng thời kỳ và đư<PERSON><PERSON> Công Ty đăng tải trên trang thông tin '\n", " 'điện tử ch<PERSON>h thức của Công Ty. Quý vị nên truy cập và kiểm tra trang web '\n", " 'thư<PERSON><PERSON> xuyên để cập nhật những thay đổi gần nhất.\\n'\n", " '- This Policy may be updated, modified, supplemented or replaced by the '\n", " \"Company from time to time and posted by the Company on the Company's \"\n", " 'official website. You should access and check our official website regularly '\n", " 'for the latest changes.\\n'\n", " '- 1.8\\n'\n", " '- Công Ty cam kết tuân thủ các nguyên tắc sau khi Xử Lý Dữ Liệu Cá Nhân:\\n'\n", " '- The Company undertakes to comply with the following principles when '\n", " 'conducting Personal Data Processing:\\n'\n", " '- (i) Công Ty xử lý và bảo vệ Dữ Liệu Cá <PERSON>hân phù hợp với quy định của pháp '\n", " 'luật Việt Nam; tuân thủ đầy đủ theo các hợp đồng, thỏa thuận, văn kiện khác '\n", " 'x<PERSON><PERSON> lập với <PERSON>;\\n'\n", " '- The Company processes and protects Personal Data in accordance with the '\n", " 'provisions of  Vietnamese laws; fully complies with contracts, agreements, '\n", " 'and other documents established with the Personal Data Subject;\\n'\n", " '- (ii) <PERSON><PERSON>ng Ty thu thập Dữ <PERSON>u <PERSON>ân với mục đích cụ thể, r<PERSON> ràng, hợp '\n", " 'ph<PERSON><PERSON>, trong phạm vi các mục đích nêu tại <PERSON>ụ<PERSON> 3 của <PERSON>h sách này và phù hợp '\n", " 'với quy định của pháp luật Việt Nam;\\n'\n", " '- The Company collects Personal Data for specific, explicit and lawful '\n", " 'purposes, within the scope of the purposes stated in Section 3 of this '\n", " 'Policy and in accordance with the provisions of Vietnamese laws;\\n'\n", " '- (iii) Công Ty luôn áp dụng và cập nhật các biện pháp kỹ thuật phù hợp với '\n", " 'quy định của pháp luật Việt Nam nhằm đảm bảo tính an toàn của Dữ Liệu C<PERSON> '\n", " '<PERSON><PERSON><PERSON>, bao gồm cả các biện pháp bảo vệ khỏi sự truy cập trái phép và/hoặc sự '\n", " 'phá hủy, m<PERSON><PERSON>, thi<PERSON><PERSON> hại cho <PERSON>;\\n'\n", " '- The Company always applies and updates technical measures in accordance '\n", " 'with the provisions of Vietnamese laws to ensure the safety of Personal '\n", " 'Data, including measures to protect against unauthorized access and/or '\n", " 'destruction, loss or damage to Personal Data;\\n'\n", " '- (iv) Công Ty lưu trữ Dữ Liệu C<PERSON>ân một cách thích hợp và trong phạm vi '\n", " 'cần thiết nhằm xử lý phù hợp với quy định của pháp luật Việt Nam; The  '\n", " 'Company  stores  Personal  Data  appropriately  and  to  the  extent  '\n", " 'necessary  for\\n'\n", " '- processinng in accordance with the provisions of Vietnamese laws;\\n'\n", " '- (v)\\n'\n", " '- Công Ty cam kết tuân thủ các quy định liên quan đến bảo vệ dữ liệu của trẻ '\n", " 'em. The  Company  undertakes  to  comply  with  provisions  related  to  '\n", " 'the  protection  of\\n'\n", " \"- children's data.\\n\")\n"]}], "source": ["pprint(node_indices[:5])\n", "pprint(node_contents[:5])"]}, {"cell_type": "code", "execution_count": 7, "id": "2fab743d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["pre tokenize: 100%|██████████| 5/5 [00:00<00:00, 30.41it/s]\n", "You're using a XLMRobertaTokenizerFast tokenizer. Please note that with a fast tokenizer, using the `__call__` method is faster than using a method to encode the text followed by a call to the `pad` method to get a padded encoding.\n", "Inference Embeddings: 100%|██████████| 5/5 [00:00<00:00,  5.72it/s]\n"]}], "source": ["node_ctn_embeddings = embedding_model.encode(node_contents, verbose=True)[\"dense_vecs\"]"]}, {"cell_type": "code", "execution_count": 8, "id": "0a1a8861", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1216, 1024)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["node_ctn_embeddings.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "b7b0db99", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.0379  ,  0.007275, -0.02313 , ..., -0.001784,  0.000647,\n", "         0.03027 ],\n", "       [-0.02377 , -0.02672 , -0.0767  , ...,  0.00918 ,  0.011925,\n", "        -0.04462 ],\n", "       [-0.02043 , -0.01814 , -0.0513  , ...,  0.0316  ,  0.001046,\n", "        -0.03026 ],\n", "       ...,\n", "       [-0.01859 ,  0.01391 , -0.03336 , ...,  0.0237  ,  0.011375,\n", "        -0.02008 ],\n", "       [-0.00418 , -0.0343  , -0.0204  , ...,  0.01656 ,  0.0157  ,\n", "        -0.02121 ],\n", "       [-0.00666 ,  0.00895 , -0.03635 , ..., -0.002676, -0.0183  ,\n", "        -0.02318 ]], shape=(1216, 1024), dtype=float16)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["node_ctn_embeddings"]}, {"cell_type": "code", "execution_count": 10, "id": "122fd8ef", "metadata": {}, "outputs": [], "source": ["# add node_ctn_embeddings to knowledge_tree\n", "for node_idx, node_ctn_emb in zip(node_indices, node_ctn_embeddings):\n", "    knowledge_tree.nodes[node_idx].embedding = node_ctn_emb.tolist()"]}, {"cell_type": "markdown", "id": "92ba923d", "metadata": {}, "source": ["FAQs points"]}, {"cell_type": "code", "execution_count": 11, "id": "acde2906", "metadata": {}, "outputs": [], "source": ["nid2faqs: dict = knowledge_tree.get_faqs()"]}, {"cell_type": "code", "execution_count": 12, "id": "81be67fb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Embedding FAQs: 100%|██████████| 424/424 [00:17<00:00, 23.83it/s]\n"]}], "source": ["from tqdm import tqdm\n", "\n", "nid2faq_embs = {}\n", "\n", "for nid, faqs in tqdm(nid2faqs.items(), desc=\"Embedding FAQs\"):\n", "    questions = []\n", "    answers = []\n", "    for qa in faqs:\n", "        questions.append(qa[\"question\"])\n", "        answers.append(qa[\"answer\"])\n", "    q_embs = embedding_model.encode(questions, verbose=False)[\"dense_vecs\"]\n", "    a_embs = embedding_model.encode(answers, verbose=False)[\"dense_vecs\"]\n", "    nid2faq_embs[nid] = {\"questions\": q_embs.tolist(), \"answers\": a_embs.tolist()}\n"]}, {"cell_type": "code", "execution_count": 13, "id": "80f39a6b", "metadata": {}, "outputs": [], "source": ["for nid, faq_embs in nid2faq_embs.items():\n", "    knowledge_tree.nodes[nid].faqs_embedding = {\n", "        \"questions\": faq_embs[\"questions\"], \n", "        \"answers\": faq_embs[\"answers\"]\n", "    }"]}, {"cell_type": "code", "execution_count": 14, "id": "d09a5f07", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Preparing points: 100%|██████████| 1505/1505 [00:00<00:00, 11190.88it/s]\n"]}], "source": ["points = []\n", "\n", "for node in tqdm(knowledge_tree.nodes, desc=\"Preparing points\"):\n", "    vector = {}\n", "    if node.embedding:\n", "        vector[\"content\"] = node.embedding\n", "    \n", "    if node.faqs_embedding:\n", "        vector[\"question\"] = node.faqs_embedding[\"questions\"]\n", "        vector[\"answer\"] = node.faqs_embedding[\"answers\"]\n", "    \n", "    if not vector:\n", "        continue\n", "\n", "    point = models.PointStruct(\n", "            id=node.idx,\n", "            vector=vector,\n", "            payload={\n", "                \"idx\": node.idx,\n", "                \"name\": node.name,\n", "                \"parent_idx\": node.parent_idx,\n", "                \"type\": node.type,\n", "                \"text\": node.text,\n", "                \"text_path\": node.text_path,\n", "                \"summary\": node.summary,\n", "                \"summary_path\": node.summary_path,\n", "                \"contextual\": node.contextual,\n", "                \"children\": node.children,\n", "                \"faqs\": node.faqs,\n", "                \"url_reference\": node.url_reference,\n", "                \"is_leaf\": node.is_leaf,\n", "            },\n", "        )\n", "    points.append(point)\n"]}, {"cell_type": "code", "execution_count": 15, "id": "86515ed6", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from tqdm import tqdm\n", "\n", "def insert_points(\n", "    points: List[models.PointStruct], \n", "    client: QdrantClient, \n", "    collection_name: str, \n", "    batch_size: int = 64, \n", "    verbose: bool = False\n", "):\n", "    for i in tqdm(\n", "        range(0, len(points), batch_size),\n", "        desc=\"Upserting points\",\n", "        disable=not verbose,\n", "    ):\n", "        batch = points[i : i + batch_size]\n", "        client.upsert(\n", "            collection_name=collection_name,\n", "            points=batch,\n", "        )"]}, {"cell_type": "code", "execution_count": 16, "id": "83ac9a0e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Upserting points: 100%|██████████| 19/19 [00:02<00:00,  8.03it/s]\n"]}], "source": ["insert_points(points, client=client_qdrant, collection_name=QDRANT_COLLECTION, verbose=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5c4acce0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "chatbot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}