{"cells": [{"cell_type": "code", "execution_count": null, "id": "17a1a158", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "\n", "sys.path.append(\"../\")\n", "sys.path.append(\"../../\")\n", "\n", "import json\n", "from datetime import datetime\n", "import asyncio\n", "import ast\n", "from textwrap import dedent\n", "import time\n", "from typing import Generator, AsyncGenerator\n", "from src.data.tree import KnowledgeTree, NodeType\n", "from typing import List, Dict, Tuple\n", "from qdrant_client import QdrantClient, AsyncQdrantClient, QdrantClient, models\n", "from src.utils import logger_func\n", "from FlagEmbedding import BGEM3FlagModel\n", "import os\n", "from typing import List\n", "\n", "from dotenv import load_dotenv, find_dotenv\n", "from openai import AzureOpenAI, AsyncAzureOpenAI\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "load_dotenv(find_dotenv())\n", "\n", "# import logging\n", "# logging.basicConfig(level=logging.INFO)\n", "\n", "\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/src/tree_structure_15051305_with_summary.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/src/tree_structure_15051305_with_summary_faqs_v2_adding_url.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_10271505.json\"\n", "# DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_11431505.json\"\n", "DATA_PATH = \"/mnt/disk1/data-chatbot/data_structure/data/tree_09081705.json\"\n", "\n", "MODEL_NAME = \"gpt-4.1\"\n", "DEPLOYMENT = \"gpt-4.1\"\n", "API_VERSION = \"2024-12-01-preview\"\n", "MAX_NEW_TOKENS= 1000\n", "\n", "ENDPOINT = os.getenv(\"openai_endpoint\")\n", "SUBSCRIPTION_KEY = os.getenv(\"openai_api_key\")\n", "\n", "MAX_RAG_ITEM = 5\n", "MAX_SEARCH_ITEMS = 50\n", "\n", "# QDRANT_COLLECTION = \"duclh11_dev_tree_collection\"\n", "# QDRANT_COLLECTION = \"vinhomes-faqs-dev\"\n", "QDRANT_COLLECTION = \"vinhomes-wiki-dev\"\n", "# QDRANT_COLLECTION = \"vinhomes-content-dev\"\n", "# QDRANT_COLLECTION = \"vinhomes-faqs-dev\"\n", "\n", "\n", "llm_client = AzureOpenAI(\n", "    api_version=API_VERSION,\n", "    azure_endpoint=ENDPOINT,\n", "    api_key=SUBSCRIPTION_KEY,\n", ")\n", "\n", "llm_async_client = AsyncAzureOpenAI(\n", "    api_version=API_VERSION,\n", "    azure_endpoint=ENDPOINT,\n", "    api_key=SUBSCRIPTION_KEY,\n", ")\n", "\n", "client_qdrant = QdrantClient(url=\"http://**************:6333\")\n", "client_async_qdrant = AsyncQdrantClient(url=\"http://**************:6333\")\n", "\n", "embedding_model = BGEM3FlagModel(\n", "    'BAAI/bge-m3',\n", "    use_fp16=True,\n", "    cache_dir='/mnt/disk1/duc_workspace/hf_models',\n", "    devices=['cuda:0']\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "6064deb8", "metadata": {}, "outputs": [], "source": ["knowledge_tree = KnowledgeTree.from_dict(json.load(open(DATA_PATH, \"r\")))"]}, {"cell_type": "code", "execution_count": null, "id": "c3577870", "metadata": {}, "outputs": [], "source": ["RAG_PROMPT = dedent(\"\"\"**Ngôn ngữ làm việc của bạn là Tiếng Việt**\n", "\n", "Bạn là trợ lý AI của Tập đoàn Vingroup, với nhiệm vụ trả lời câu hỏi dựa trên đoạn văn được cung cấp.\n", "\n", "**QUY TẮC:**\n", "1. CHỈ sử dụng thông tin từ đoạn văn được cung cấp. Không dùng kiến thức có sẵn hoặc quan điểm cá nhân. Nếu không có thông tin liên quan, tr<PERSON> lời: \"Không có thông tin liên quan đến câu hỏi của bạn. Vui lòng cung cấp thêm ngữ cảnh.\"\n", "\n", "2. <PERSON><PERSON><PERSON> nguyên mọi dữ liệu số mang tính quan trọng (s<PERSON> liệ<PERSON>, phầ<PERSON> trăm, ng<PERSON><PERSON>án<PERSON>, URL, địa chỉ, số thiện thoại, email, v.v.). G<PERSON><PERSON> thứ tự gốc của hướng dẫn, quy trình, c<PERSON><PERSON> bước hoặc lưu ý. Không được tự ý thêm thông tin không liên quan hoặc tạo ra thông tin không có trong đoạn văn.\n", "\n", "3. <PERSON><PERSON> dụng markdown khi cần thiết. <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> rõ ràng, c<PERSON> cấu trúc.\n", "\n", "4. <PERSON><PERSON> tích thông tin theo từng bước để đảm bảo câu trả lời chính xác và đầy đủ với những nội dung quan trọng.\n", "\n", "5. <PERSON><PERSON><PERSON> kết thúc bằng phần tóm tắt ngắn gọn cho câu hỏi chính với tiêu để **Kết luận:**.\n", "\n", "6. CHỈ sử dụng tiế<PERSON>, trả lời trực tiếp và đúng trọng tâm.\n", "\n", "7. Hạn chế nhữn câu dẫn dắt như: \"Dựa vào đoạn văn được cung cấp\", \"<PERSON><PERSON> tích từ đoạn văn trên\", v.v.\n", "\n", "**THÔNG TIN BỔ SUNG:**\n", "- <PERSON><PERSON><PERSON> nay: {today}\n", "- <PERSON><PERSON> tịch hiện nay và kiêm <PERSON> sáng lập Vingroup: Ông <PERSON><PERSON><PERSON>ợng\n", "- Quần đ<PERSON><PERSON> và Trường Sa thuộc chủ quyền của Việt Nam\n", "\n", "**TRÍCH XUẤT THÔNG TIN:**\n", "<PERSON><PERSON><PERSON> định các thông tin liên quan trong đoạn văn. <PERSON><PERSON><PERSON> không chắc chắn về thông tin, KHÔNG suy luận, hã<PERSON> cho biết là không có đủ thông tin.\n", "\"\"\"\n", ")\n", "\n", "FILTER_PROMPT = \"\"\"Can we use this passage to answer the question or part of the question? Return 1 or 0 only. No explanation needed.\"\"\"\n", "\n"]}, {"cell_type": "markdown", "id": "c5cf3212", "metadata": {}, "source": ["# Revamp Synchronous Functions"]}, {"cell_type": "code", "execution_count": null, "id": "71c80129", "metadata": {}, "outputs": [], "source": ["def rank_points(\n", "    *lists: List[models.ScoredPoint],\n", "    max_items: int = MAX_RAG_ITEM,\n", "    alpha: float = 0.7\n", ") -> List[models.ScoredPoint]:\n", "    \"\"\"\n", "    Aggregate and rank ScoredPoint lists (returns top max_items):\n", "      1) Sum scores per point ID\n", "      2) Count occurrences across lists\n", "      3) Compute blended score: alpha*(sum_score/max_sum) + (1-alpha)*((count-1)/(n-1))\n", "      4) Return top max_items points sorted by final score then raw score.\n", "    \"\"\"\n", "    from collections import defaultdict\n", "    import heapq\n", "\n", "    total_score: Dict[str, float] = defaultdict(float)\n", "    count: Dict[str, int] = defaultdict(int)\n", "    id_to_point: Dict[str, models.ScoredPoint] = {}\n", "\n", "    # Aggregate scores and counts\n", "    for lst in lists:\n", "        seen = set()\n", "        for pt in lst:\n", "            total_score[pt.id] += pt.score\n", "            id_to_point[pt.id] = pt\n", "            if pt.id not in seen:\n", "                count[pt.id] += 1\n", "                seen.add(pt.id)\n", "\n", "    # Build fixed-size heap for top-k\n", "    heap: List[Tuple[float, float, str]] = []  # (final_score, total_score, pid)\n", "    max_sum = max(total_score.values(), default=1.0)\n", "    n = len(lists) or 1\n", "\n", "    for pid, S in total_score.items():\n", "        m = count[pid]\n", "        final = alpha * (S / max_sum) + (1 - alpha) * ((m - 1) / max(n - 1, 1))\n", "        entry = (final, S, pid)\n", "        if len(heap) < max_items:\n", "            heapq.heappush(heap, entry)\n", "        else:\n", "            heapq.heappushpop(heap, entry)\n", "\n", "    # Extract and sort results\n", "    top = heapq.nlargest(max_items, heap)\n", "    return [id_to_point[pid] for _, _, pid in top]\n", "\n", "\n", "@logger_func\n", "def construct_context(knowledge_tree: KnowledgeTree, list_nodes: List[models.ScoredPoint]):\n", "    # list_nodes = sorted(list_nodes, key=lambda n: n.id, reverse=False)\n", "    context = \"\"\"\"\"\"\n", "    for idx, node in enumerate(list_nodes):\n", "        if node.payload:\n", "            n_name = node.payload.get(\"name\", \"\")\n", "            n_text = node.payload.get(\"text\", \"\")\n", "            n_type = node.payload.get(\"type\", NodeType.NOT_ELEMENTARY)\n", "            n_url  = node.payload.get(\"url_reference\", \"\")\n", "        else:\n", "            n_name = \"\"\n", "            n_text = \"\"\n", "            n_type = NodeType.NOT_ELEMENTARY\n", "            n_url = \"\"\n", "\n", "        if n_type != NodeType.ELEMENTARY:\n", "            # retrieve nearest elementary parent\n", "            target_node_idx = knowledge_tree.nearest_elementary(node.id).idx\n", "        else:\n", "            target_node_idx = node.id\n", "\n", "        # get all texts below target_node_idx\n", "        content_texts = knowledge_tree.get_children_contents(target_node_idx)\n", "\n", "        if content_texts:\n", "            n_text += \"\\n\".join(content_texts)\n", "\n", "        context += (\n", "            f\"\\n#Passage ID: {idx}\\n\"\n", "            f\"*Url*: {n_url}\\n\"\n", "            f\"*Title/Headline*: {n_name}\\n\"\n", "            f\"*Content*: {n_text}\\n\"\n", "        )\n", "    return context\n", "\n", "\n", "@logger_func\n", "def get_similar_fusion_contexts(\n", "    query: str,\n", "    model: BGEM3FlagModel,\n", "    client: QdrantClient,\n", "    max_items: int = MAX_RAG_ITEM,\n", "    max_search_items: int = MAX_SEARCH_ITEMS,\n", ") -> List[models.ScoredPoint]:\n", "    \"\"\"Retrieve and merge similar points from content and FAQ collections synchronously.\"\"\"\n", "    # 1) Encode\n", "    orig_fp16 = getattr(model, 'use_fp16', False)\n", "    model.use_fp16 = False\n", "    try:\n", "        embed = model.encode([query], batch_size=1, max_length=100)\n", "    finally:\n", "        model.use_fp16 = orig_fp16\n", "    vector = embed['dense_vecs'][0].astype(float)\n", "\n", "    requests = [\n", "        models.SearchRequest(vector=models.NamedVector(name=\"content\", vector=vector), limit=max_search_items, with_payload=True),\n", "        models.SearchRequest(vector=models.NamedVector(name=\"question\", vector=vector), limit=max_search_items, with_payload=True),\n", "        models.SearchRequest(vector=models.NamedVector(name=\"answer\", vector=vector), limit=max_search_items, with_payload=True),\n", "    ]\n", "    # Execute batch search with timeout\n", "    responses = client.search_batch(\n", "        collection_name=QDRANT_COLLECTION, requests=requests, timeout=6\n", "    )\n", "\n", "    # Extract results\n", "    content_pts, question_pts, answer_pts = responses\n", "\n", "    # Rank and return top points\n", "    return rank_points(content_pts, question_pts, answer_pts, max_items=max_items)\n", "\n", "\n", "@logger_func\n", "def filter_point(context, query: str) -> bool:\n", "    system_msg = FILTER_PROMPT\n", "    user_msg   = f\"\\n#Context\\n{context}\\n========\\n#Question\\n{query}\\n========:\"\n", "    \n", "    resp = llm_client.chat.completions.create(\n", "        model=\"gpt-4.1-nano\",\n", "        messages=[{\"role\": \"system\", \"content\": system_msg}, {\"role\": \"user\", \"content\": user_msg}],\n", "        temperature=0.1,\n", "        max_tokens=10,\n", "    )\n", "\n", "    decision = int(ast.literal_eval(resp.choices[0].message.content))\n", "    return decision\n", "\n", "\n", "@logger_func\n", "def rag_answer(\n", "    query: str,\n", ") -> Generator[str, None, None]:\n", "    \"\"\"Perform RAG synchronously, yielding LLM tokens as they arrive.\"\"\"\n", "    top_points = get_similar_fusion_contexts(\n", "        query, embedding_model, client_qdrant,\n", "        max_items=MAX_RAG_ITEM, max_search_items=MAX_SEARCH_ITEMS\n", "    )\n", "\n", "    top_points = sorted(top_points, key=lambda n: n.id, reverse=False)\n", "    contexts = [construct_context(knowledge_tree, [point]) for point in top_points]\n", "    decisions = [filter_point(context, query) for context in contexts]\n", "\n", "    filtered_points = [point for point, decision in zip(top_points, decisions) if decision]\n", "    context = construct_context(knowledge_tree, filtered_points)\n", "\n", "    ref_urls = []\n", "    for point in filtered_points:\n", "        if point.payload:\n", "            url = point.payload.get(\"url_reference\", None)\n", "            if url and url not in ref_urls:\n", "                ref_urls.append(url)\n", "\n", "    system_msg = RAG_PROMPT.format(today=datetime.now().strftime(\"%d/%m/%Y\"))\n", "    user_msg   = f\"Context: {context}\\n========\\nQuestion: {query}\"\n", "\n", "    def stream_response() -> Generator[str, None, None]:\n", "        start = time.perf_counter()\n", "        stream = llm_client.chat.completions.create(\n", "            model=MODEL_NAME,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_msg},\n", "                {\"role\": \"user\", \"content\": user_msg},\n", "            ],\n", "            temperature=0.1,\n", "            max_tokens=MAX_NEW_TOKENS,\n", "            stream=True,\n", "        )\n", "\n", "        first = True\n", "        for chunk in stream:\n", "            if not chunk.choices:\n", "                continue\n", "            text = chunk.choices[0].delta.content\n", "\n", "            if not text:\n", "                continue\n", "\n", "            if first and text:\n", "                ttf = time.perf_counter() - start\n", "                print(f\"⏱️ Time to first token: {ttf:.3f}s\")\n", "                first = False\n", "\n", "            yield text\n", "\n", "        if ref_urls:\n", "            ref = \"\\n\\n**Tham khảo:**\\n\"\n", "            for url in ref_urls:\n", "                ref += f\"- {url}\\n\"\n", "            yield ref\n", "\n", "    return stream_response()\n"]}, {"cell_type": "markdown", "id": "38808dfb", "metadata": {}, "source": ["# Async version"]}, {"cell_type": "code", "execution_count": null, "id": "54984f2d", "metadata": {}, "outputs": [], "source": ["@logger_func\n", "async def get_similar_fusion_contexts_async(\n", "    query: str,\n", "    model,\n", "    client: AsyncQdrantClient,\n", "    max_items: int = 5,\n", "    max_search_items: int = 10,\n", ") -> List[models.ScoredPoint]:\n", "    \"\"\"\n", "    Great use of async def here — this lets you run Qdrant searches concurrently without blocking the event loop, \n", "    which is a big improvement over your original synchronous ThreadPoolExecutor approach.\n", "    \"\"\"\n", "    # 1) Encode query in thread pool, disable fp16 to avoid dtype mismatch\n", "    def encode_query():\n", "        orig_fp16 = getattr(model, 'use_fp16', False)\n", "        model.use_fp16 = False\n", "        try:\n", "            return model.encode([query], batch_size=1, max_length=100)\n", "        finally:\n", "            model.use_fp16 = orig_fp16\n", "\n", "    # Offloading the blocking model.encode call into a thread via asyncio.to_thread prevents the embedder’s CPU/GPU \n", "    # work from stalling other async tasks, improving overall throughput.\n", "    embed_result = await asyncio.to_thread(encode_query)\n", "    vector = embed_result['dense_vecs'][0].astype(float)\n", "\n", "    # Search\n", "    requests = [\n", "        models.SearchRequest(vector=models.NamedVector(name=\"content\", vector=vector), limit=max_search_items, with_payload=True),\n", "        models.SearchRequest(vector=models.NamedVector(name=\"question\", vector=vector), limit=max_search_items, with_payload=True),\n", "        models.SearchRequest(vector=models.NamedVector(name=\"answer\", vector=vector), limit=max_search_items, with_payload=True),\n", "    ]\n", "    # Batched search_batch calls to Qdrant are now non‑blocking and handled by the async client, \n", "    # which avoids Python threads and leverages asyncio for better I/O performance compared to multiple synchronous calls.\n", "    responses = await client.search_batch(\n", "        collection_name=QDRANT_COLLECTION,\n", "        requests=requests,\n", "        timeout=6\n", "    )\n", "    content_pts, question_pts, answer_pts = responses\n", "    return rank_points(content_pts, question_pts, answer_pts, max_items=max_items)\n", "\n", "\n", "@logger_func\n", "async def filter_point_async(context, query: str) -> bool:\n", "    system_msg = FILTER_PROMPT\n", "    user_msg   = f\"\\n#Context\\n{context}\\n========\\n#Question\\n{query}\\n========:\"\n", "    \n", "    resp = await llm_async_client.chat.completions.create(\n", "        model=\"gpt-4.1-nano\",\n", "        messages=[{\"role\": \"system\", \"content\": system_msg}, {\"role\": \"user\", \"content\": user_msg}],\n", "        temperature=0.1,\n", "        max_tokens=10,\n", "    )\n", "\n", "    decision = int(ast.literal_eval(resp.choices[0].message.content))\n", "    return decision\n", "\n", "\n", "@logger_func\n", "async def rag_answer_async(\n", "    query: str,\n", ") -> str:\n", "    start_time = time.perf_counter()\n", "\n", "    top_points = await get_similar_fusion_contexts_async(\n", "        query, embedding_model, client_async_qdrant, 5, 50\n", "    )\n", "\n", "    top_points = sorted(top_points, key=lambda n: n.id, reverse=False)\n", "    contexts = [construct_context(knowledge_tree, [point]) for point in top_points]\n", "    point_filter_tasks = [filter_point_async(context, query) for context in contexts]   \n", "    decisions = await asyncio.gather(*point_filter_tasks)\n", "\n", "    filtered_points = [point for point, decision in zip(top_points, decisions) if decision]\n", "    context = construct_context(knowledge_tree, filtered_points)\n", "\n", "    ref_urls = []\n", "    for point in filtered_points:\n", "        if point.payload:\n", "            url = point.payload.get(\"url_reference\", None)\n", "            if url and url not in ref_urls:\n", "                ref_urls.append(url)\n", "    \n", "    system_msg = RAG_PROMPT.format(today=datetime.now().strftime(\"%d/%m/%Y\"))\n", "    user_msg = f\"Context: {context}\\n========\\nQuestion: {query}\"\n", "\n", "    async def stream_response() -> AsyncGenerator[str, None]:\n", "        stream = await llm_async_client.chat.completions.create(\n", "            model=MODEL_NAME,\n", "            messages=[{\"role\": \"system\", \"content\": system_msg},\n", "                      {\"role\": \"user\",   \"content\": user_msg}],\n", "            temperature=0.1,\n", "            max_tokens=5000,\n", "            stream=True,\n", "        )\n", "        \n", "        first = True\n", "        async for chunk in stream:\n", "            if not chunk.choices:\n", "                continue\n", "            \n", "            text = chunk.choices[0].delta.content\n", "            if not text:\n", "                continue\n", "\n", "            if first:\n", "                ttf = time.perf_counter() - start_time\n", "                print(f\"\\nSTART TO ANSWER(⏱️ Time to first token: {ttf:.3f}s)\")\n", "                first = False\n", "            text = chunk.choices[0].delta.content\n", "            if text:\n", "                yield text\n", "\n", "        if ref_urls:\n", "            ref = \"\\n\\n**Tham khảo:**\\n\"\n", "            for url in ref_urls:\n", "                ref += f\"- {url}\\n\"\n", "            yield ref\n", "\n", "    return stream_response()"]}, {"cell_type": "code", "execution_count": null, "id": "4af87d39", "metadata": {}, "outputs": [], "source": ["# query = \"Địa chỉ của Vinhomes West Point ở đâu và có quy định gì đặc biệt về nhân sự ở đây liên quan đến nghỉ việc?\"\n", "# query = \"Các chỉ tiêu KPI trong bộ phận Vườn ươm và Khối Công nghệ của Vinhomes có điểm gì giống và khác nhau?\"\n", "# query = \"Ai là chủ tịch của Vingroup hiện nay?\"\n", "# query = \"Quy trình phát lệnh cho tự động hóa AR1 đ<PERSON><PERSON><PERSON> thực hiện thế nào?\"\n", "# query = \"Bể bơi bốn mùa tại Vinhomes Metropolis là gì?\"\n", "# query = \"KPI trong Khối Thủ tục được định nghĩa như thế nào?\"\n", "# query = \"<PERSON><PERSON>u tôi thích môi trường sống gần biển hoặc sông, khu đô thị nào phù hợp hơn và tại sao?\"\n", "# query = \"cấp T4 đi trễ có sao không?\"\n", "# query = \"đánh lãnh dạo có bị sao không?\"\n", "# query = \"Email hỗ trợ phân quyền SAP thế nào?\"\n", "\n", "query = \"quy trình xin phân quyền vpn?\"\n", "tokens = await rag_answer_async(query)\n", "\n", "async for tok in tokens:\n", "    print(tok, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "e12da5c7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "chatbot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}