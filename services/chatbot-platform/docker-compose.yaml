services:
  vinhomes-wiki-chatbot:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .vinhomes_wiki.env
    volumes:
      - "./data/vinhomes_wiki:/app/data"
    environment:
      - MAX_WORKERS=4
    restart: always

  vinhome-wiki-litestream:
    image: litestream/litestream:0.3
    entrypoint: ["/bin/sh", "-c"]
    healthcheck:
      test: /usr/local/bin/litestream restore -if-db-not-exists -if-replica-exists -o /mnt/data/database.sqlite abs://${AZURE_STORAGEACCOUNT}@${AZURE_CONTAINER}/vinhomes-wiki/database.sqlite
      timeout: 10m
      retries: 1
    command:
      - /usr/local/bin/litestream replicate /mnt/data/database.sqlite abs://${AZURE_STORAGEACCOUNT}@${AZURE_CONTAINER}/vinhomes-wiki/database.sqlite
    volumes:
      - ./data/vinhomes_wiki:/mnt/data
    env_file:
      - .vinhomes_wiki.env
    environment:
      - AZURE_STORAGEACCOUNT=${AZURE_STORAGEACCOUNT}
      - AZURE_CONTAINER=${AZURE_CONTAINER}
    restart: always

  vinwonder-cs-chatbot:
    build: .
    ports:
      - "9000:8000"
    env_file:
      - .vinwonder_cs.env
    environment:
      - MAX_WORKERS=6
    volumes:
      - "./data/vinwonder-cs:/app/data"

  vinwonder-cs-litestream:
    image: litestream/litestream:0.3
    entrypoint: ["/bin/sh", "-c"]
    healthcheck:
      test: /usr/local/bin/litestream restore -if-db-not-exists -if-replica-exists -o /mnt/data/database.sqlite abs://${AZURE_STORAGEACCOUNT}@${AZURE_CONTAINER}/vinwonder-cs/database.sqlite
      timeout: 10m
      retries: 1
    command:
      - /usr/local/bin/litestream replicate /mnt/data/database.sqlite abs://${AZURE_STORAGEACCOUNT}@${AZURE_CONTAINER}/vinwonder-cs/database.sqlite
    volumes:
      - ./data/vinwonder-cs:/mnt/data
    env_file:
      - .vinwonder_cs.env
    environment:
      - AZURE_STORAGEACCOUNT=${AZURE_STORAGEACCOUNT}
      - AZURE_CONTAINER=${AZURE_CONTAINER}
    restart: always