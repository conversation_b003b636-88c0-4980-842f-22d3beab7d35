import logging
import json
from argparse import Argument<PERSON><PERSON><PERSON>

from tqdm import tqdm
from FlagEmbedding import BGEM3FlagModel
from src.data.tree import KnowledgeTree
from src.rag.index import (
    client_qdrant,
    create_collection,
    create_fusion_collection,
    create_points,
    upsert_points,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def parse_arguments():
    parser = ArgumentParser()
    parser.add_argument("--dataset", type=str, required=True, help="Dataset path")
    parser.add_argument(
        "--collection_name", type=str, required=True, help="Collection name"
    )
    parser.add_argument("--vector_dim", type=int, default=1024, help="Vector dimension")
    parser.add_argument("--batch_size", type=int, default=32, help="Batch size")
    parser.add_argument(
        "--model_name", type=str, default="BAAI/bge-m3", help="Embedding model"
    )
    parser.add_argument("--has_faqs", action="store_true", help="Enable FAQ embedding")
    parser.add_argument("--verbose", action="store_true", help="Verbose mode")
    return parser.parse_args()


def load_knowledge_tree(path: str) -> KnowledgeTree:
    with open(path, "r") as f:
        data = json.load(f)
    return KnowledgeTree.from_dict(data)


def embed_nodes(
    tree: KnowledgeTree, batch_size: int, model: BGEM3FlagModel, verbose: bool
):
    node_idxs, node_ctns = tree.get_all_embedding_contents(return_indices=True)
    filtered = [(i, v) for i, v in zip(node_idxs, node_ctns) if v]
    node_indices, node_contents = zip(*filtered)

    embeddings = model.encode(
        node_contents,
        batch_size=batch_size,
        verbose=verbose,
    )["dense_vecs"]

    for node_idx, emb in zip(node_indices, embeddings):
        tree.nodes[node_idx].embedding = emb.tolist()


def embed_faqs(
    tree: KnowledgeTree, batch_size: int, model: BGEM3FlagModel, verbose: bool
):
    nid2faqs = tree.get_faqs()

    for nid, faqs in tqdm(nid2faqs.items(), desc="Embedding FAQs"):
        questions = [qa["question"] for qa in faqs]
        answers = [qa["answer"] for qa in faqs]

        q_embs = model.encode(questions, batch_size=batch_size, verbose=verbose)[
            "dense_vecs"
        ].tolist()
        a_embs = model.encode(answers, batch_size=batch_size, verbose=verbose)[
            "dense_vecs"
        ].tolist()

        tree.nodes[nid].faqs_embedding = {
            "questions": q_embs,
            "answers": a_embs,
        }


def main():
    args = parse_arguments()

    model = BGEM3FlagModel(
        args.model_name,
        use_fp16=True,
        cache_dir="/mnt/disk1/duc_workspace/hf_models",
        devices=["cuda:0"],
    )

    # Step 1: Create collection
    if args.has_faqs:
        create_fusion_collection(args.collection_name, args.vector_dim)
    else:
        create_collection(args.collection_name, args.vector_dim)

    # Step 2: Load and embed nodes
    tree = load_knowledge_tree(args.dataset)
    embed_nodes(tree, args.batch_size, model, args.verbose)

    # Step 3: Embed FAQs if needed
    if args.has_faqs:
        embed_faqs(tree, args.batch_size, model, args.verbose)

    # Step 4: Upsert into Qdrant
    points = create_points(tree)
    upsert_points(
        points=points,
        client=client_qdrant,
        collection_name=args.collection_name,
        batch_size=args.batch_size,
        verbose=args.verbose,
    )


if __name__ == "__main__":
    """
    Example usage:
    ```
    python scripts/create_qdrant_index.py \
        --dataset data_structure/data/tree_10271505.json \
        --collection_name vinhomes-wiki-dev-4000 \
        --vector_dim 1024 \
        --batch_size 32 \
        --model_name BAAI/bge-m3 \
        --has_faqs \
        --verbose
    ```
    """
    main()
