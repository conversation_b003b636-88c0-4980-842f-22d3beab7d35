"""
This script creates a BM25 index from a dataset of knowledge tree.
"""

import json
import bm25s
import spacy
from argparse import Argument<PERSON><PERSON><PERSON>
from unidecode import unidecode
from src.data.tree import KnowledgeTree

nlp = spacy.load("xx_sent_ud_sm")


def convert_vietnamese_to_lower_without_accent(text):
    text_without_accent = unidecode(text)
    text_lower = text_without_accent.lower()
    return text_lower


def main():
    parser = ArgumentParser()
    parser.add_argument(
        "--dataset", type=str, required=True, help="Path to the dataset JSON file"
    )
    parser.add_argument(
        "--output_folder",
        type=str,
        required=True,
        help="P&L name/Directory to save BM25 index and mapping",
    )
    args = parser.parse_args()

    with open(args.dataset) as f:
        data = json.load(f)
    tree = KnowledgeTree.from_dict(data)

    list_idx = []
    list_content = []

    for node in tree.nodes:
        list_idx.append(node.idx)
        list_content.append(convert_vietnamese_to_lower_without_accent(node.text))

    list_text_chunks = []
    list_index_map = []
    for idx, content in enumerate(list_content):
        doc = nlp(content)
        for sent in doc.sents:
            text = sent.text
            if "|" in text:
                list_sent = text.split("|")
                for sent in list_sent:
                    if sent:
                        text = " ".join([i for i in sent.split() if i])
                        list_text_chunks.append(text)
                        list_index_map.append(idx)
            else:
                if text:
                    text = " ".join([i for i in text.split() if i])
                    list_text_chunks.append(text)
                    list_index_map.append(idx)

    mapping_index = {idx: list_idx[v] for idx, v in enumerate(list_index_map)}

    corpus_tokens = bm25s.tokenize(list_text_chunks)

    retriever = bm25s.BM25()
    retriever.index(corpus_tokens)
    retriever.save(f"{args.output_folder}", corpus=list_text_chunks)

    with open(f"{args.output_folder}/mapping_content.json", "w") as f:
        json.dump(mapping_index, f)


if __name__ == "__main__":
    """
    Example usage:
    ```
    PYTHONPATH=. python scripts/create_bm25_index.py \
        --dataset /mnt/disk1/data-chatbot/data_structure/data/tree_vinwonders_vinpearl_00402705.json \
        --output_folder /mnt/disk1/thu_workspace/vinit-central-ai/services/chatbot-platform/vinwonders_fulltext_search
    ```
    """
    main()
