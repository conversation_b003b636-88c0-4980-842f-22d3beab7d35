"""This script implements a chatbot web application using Streamlit and OpenAI's Azure API.
It includes user authentication, message handling, and response generation with function calls.

To use this script:
```
streamlit run demo.py -- --config demo/vinhomes.yaml
```
"""

import argparse
import asyncio
import logging
import uuid

import streamlit as st
import streamlit_authenticator as stauth
import yaml
from app.config import app_config
from app.config import settings as app_settings
from app.core import stream_response_async
from app.db.session import SessionLocal, get_chat_history, upsert_chat_history
from app.schemas import ChatRequest
from omegaconf import OmegaConf
from openai import ContentFilterFinishReasonError, OpenAIError
from src.utils import preprocess_message
from yaml.loader import SafeLoader


# Add argument parsing
def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Chatbot Web Application")
    parser.add_argument(
        "--config",
        type=str,
        default="demo/vinhomes.yaml",
        help="Path to the authentication configuration file",
    )
    return parser.parse_args()


# Parse arguments
args = parse_args()

# Load configuration from YAML file
try:
    config = OmegaConf.load(args.config)
except Exception as e:
    raise ValueError(f"Error loading prompt file: {e}")


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(f"logs/demo-{config.app_name}.log", mode="a")],
)

logger = logging.getLogger(__name__)


async def response_generator(user_msg: str, session_id: str):
    total_msg = ""
    no_answer = False
    buffer_lenght = 20
    buffer = ""
    chat_request = ChatRequest(message=user_msg, session_id=session_id)
    try:
        with SessionLocal() as db:
            r_gen, first_char = await stream_response_async(request=chat_request, db=db)
        yield f"{first_char}"

        async for token in r_gen:
            if not token.choices:
                continue
            if not (delta_content := token.choices[0].delta.content):
                continue
            total_msg += delta_content
            buffer += delta_content
            if "<NOANS>" in total_msg:
                no_answer = True
                buffer = buffer.replace("<NOANS>", "")
                total_msg = total_msg.replace("<NOANS>", "")

            if len(buffer) > buffer_lenght:
                yield buffer
                buffer = ""

        if buffer.strip():
            yield buffer
            buffer = ""

        if (
            hasattr(r_gen, "ref_urls")
            and not no_answer
            and not app_config[app_settings.app_name].disable_ref_urls
        ):
            remain_msg = "\n\n**Tham khảo:**\n"
            for url in r_gen.ref_urls:
                remain_msg += f"- {url}\n"
            yield remain_msg
    except OpenAIError as e:
        logger.error(f"OpenAIError: {e}")
        if "content filtering policies" in str(e):
            content = "Câu hỏi của bạn vi phạm policy, hãy thử lại câu hỏi khác"
        else:
            content = "Đã xảy ra lỗi xảy ra, hãy thử lại sau."
        yield content
    except ContentFilterFinishReasonError as e:
        logger.error(f"ContentFilterFinishReasonError: {e}")
        "Câu hỏi của bạn vi phạm policy, hãy thử lại câu hỏi khác"
        yield content


def to_sync_generator(async_gen):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        while True:
            try:
                yield loop.run_until_complete(anext(async_gen))
            except StopAsyncIteration:
                break
    finally:
        loop.close()


with open(config.streamlit_config_path) as file:
    st_config = yaml.load(file, Loader=SafeLoader)

st.set_page_config(page_title=config.page_title)

authenticator = stauth.Authenticate(
    st_config["credentials"],
    st_config["cookie"]["name"],
    st_config["cookie"]["key"],
    st_config["cookie"]["expiry_days"],
)
try:
    authenticator.login()
except Exception as e:
    st.error(e)

if __name__ == "__main__":
    if st.session_state.get("authentication_status"):
        authenticator.logout()

        st.title(config.assistant_name)
        st.write(
            config.welcome_message.format(
                user_name=st.session_state.get("name", "khách hàng")
            )
        )

        with st.chat_message("assistant", avatar=config.assistant_avatar):
            st.write(
                config.chatbot_init_message.format(assistant_name=config.assistant_name)
            )

        if "session_id" not in st.session_state:
            st.session_state.session_id = str(uuid.uuid4())
        if "messages" not in st.session_state:
            with SessionLocal() as db:
                chat_history = get_chat_history(st.session_state.session_id, db)
            st.session_state.messages = []
            for i in chat_history:
                if i["role"] == "user":
                    st.session_state.messages.append(
                        {"role": "user", "content": i["content"]}
                    )
                elif i["role"] == "assistant" and i.get("content"):
                    st.session_state.messages.append(
                        {"role": "assistant", "content": i["content"]}
                    )

        for msg_idx, message in enumerate(st.session_state.messages):
            with st.chat_message(message["role"], avatar=config.assistant_avatar):
                st.markdown(message["content"])

        prompt = st.chat_input("Nhập câu hỏi của bạn")

        if prompt:
            st.session_state.messages.append(
                {"role": "user", "content": preprocess_message(prompt)}
            )
            with st.chat_message("user", avatar=config.user_avatar):
                st.markdown(prompt)

            with st.chat_message("assistant", avatar=config.assistant_avatar):
                response = st.write_stream(
                    response_generator(
                        prompt,
                        session_id=st.session_state.session_id,
                    )
                )
            assistant = {"role": "assistant", "content": response.strip()}
            st.session_state.messages.append(assistant)
            with SessionLocal() as db:
                upsert_chat_history(
                    st.session_state.session_id, [assistant], db, existing_update=True
                )

    elif st.session_state.get("authentication_status") is False:
        st.error("Username/password is incorrect")
    elif st.session_state.get("authentication_status") is None:
        st.warning("Please enter your username and password")
