import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sdk_chatbot/sdk_chatbot.dart';

void main() {
  group('ChatConfig Tests', () {
    test('ChatConfig initializes with default values', () {
      final config = ChatConfig(apiEndpoint: 'https://example.com/chat');
      
      expect(config.apiEndpoint, 'https://example.com/chat');
      expect(config.primaryColor, Colors.blue);
      expect(config.textColor, Colors.black);
      expect(config.backgroundColor, Colors.white);
      expect(config.buttonText, 'Chat with us');
      expect(config.buttonIcon, Icons.chat_bubble_outline);
      expect(config.initialGreeting, 'Hello! How can I help you today?');
      expect(config.apiKey, isNull);
    });
    
    test('ChatConfig accepts custom values', () {
      final config = ChatConfig(
        apiEndpoint: 'https://example.com/api',
        primaryColor: Colors.red,
        secondaryColor: Colors.amber,
        textColor: Colors.white,
        backgroundColor: Colors.black,
        buttonText: 'Support',
        buttonIcon: Icons.support_agent,
        initialGreeting: 'Welcome!',
        apiKey: 'test-key',
      );
      
      expect(config.apiEndpoint, 'https://example.com/api');
      expect(config.primaryColor, Colors.red);
      expect(config.secondaryColor, Colors.amber);
      expect(config.textColor, Colors.white);
      expect(config.backgroundColor, Colors.black);
      expect(config.buttonText, 'Support');
      expect(config.buttonIcon, Icons.support_agent);
      expect(config.initialGreeting, 'Welcome!');
      expect(config.apiKey, 'test-key');
    });
  });
  
  group('ChatController Tests', () {
    test('ChatController initializes with greeting message', () {
      final config = ChatConfig(apiEndpoint: 'https://example.com/chat');
      final controller = ChatController(config: config);
      
      expect(controller.messages.length, 1);
      expect(controller.messages.first.text, config.initialGreeting);
      expect(controller.messages.first.isUser, false);
    });
    
    test('ChatController clear chat keeps greeting message', () {
      final config = ChatConfig(apiEndpoint: 'https://example.com/chat');
      final controller = ChatController(config: config);
      
      // Add a test message
      controller.messages.add(
        ChatMessage(
          text: 'Test message',
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      
      expect(controller.messages.length, 2);
      
      // Clear chat
      controller.clearChat();
      
      // Should only have greeting message
      expect(controller.messages.length, 1);
      expect(controller.messages.first.text, config.initialGreeting);
    });
  });
  
  // Widget tests would go here for testing the actual UI components
  // They would require a testWidgets function and MaterialApp wrapper
}
