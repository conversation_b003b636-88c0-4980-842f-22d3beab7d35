# SDK Chatbot

A customizable chatbot widget for Flutter applications that adds a floating chat button to your app for customer support.

## Features

- Floating action button chat widget
- Customizable colors, text, and icons
- Configurable API endpoints for integration with your own backend
- Easy to integrate with any Flutter application
- Responsive and animated UI
- Complete chat interface with message history

## Getting Started

To use this plugin, add `sdk_chatbot` as a dependency in your `pubspec.yaml` file:

```yaml
dependencies:
  sdk_chatbot: ^0.0.1  # Use the latest version
```

## Usage

Import the package:

```dart
import 'package:sdk_chatbot/sdk_chatbot.dart';
```

Add the ChatButton widget to your app:

```dart
// Create a chat configuration
final chatConfig = ChatConfig(
  apiEndpoint: 'https://your-api.example.com/chat',
  primaryColor: Colors.blue,
  buttonText: 'Support',
  initialGreeting: 'Hello! How can I help you today?',
);

// Add the button to your scaffold
Scaffold(
  // Your scaffold content...
  floatingActionButton: ChatButton(
    config: chatConfig,
    onChatOpen: () {
      print('Chat opened');
    },
    onChatClose: () {
      print('Chat closed');
    },
  ),
);
```

## Configuration Options

You can customize the chat button and interface using `ChatConfig`:

```dart
ChatConfig(
  // Required
  apiEndpoint: 'https://your-api.example.com/chat',
  
  // Optional customizations
  primaryColor: Colors.blue,
  secondaryColor: Colors.lightBlue,
  textColor: Colors.black,
  backgroundColor: Colors.white,
  buttonText: 'Chat with us',
  buttonIcon: Icons.chat_bubble_outline,
  initialGreeting: 'Hello! How can I help you today?',
  apiKey: 'your-api-key',  // If your API requires authentication
);
```

## ChatButton Properties

```dart
ChatButton(
  config: chatConfig,  // Required
  alignment: Alignment.bottomRight,  // Position on screen
  padding: EdgeInsets.all(16.0),  // Padding from screen edges
  size: 60.0,  // Button size
  showLabel: false,  // Whether to show text label on button
  shape: BoxShape.circle,  // Button shape
  onChatOpen: () {},  // Called when chat opens
  onChatClose: () {},  // Called when chat closes
);
```

## API Integration

The SDK connects to your backend API specified in the `apiEndpoint` property. 

Your API should accept POST requests with JSON body:
```json
{
  "message": "User's message text"
}
```

And should respond with:
```json
{
  "response": "Bot's response message"
}
```

## Example

Check out the [example](example) folder for a complete implementation example.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
