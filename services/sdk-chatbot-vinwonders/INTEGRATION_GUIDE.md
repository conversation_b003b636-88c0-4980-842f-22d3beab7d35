# SDK Chatbot Integration Guide

This guide will help you integrate the SDK Chatbot into your Flutter application.

## Prerequisites

- Flutter 3.0.0 or higher
- Dart 3.0.0 or higher
- An API endpoint for your chatbot service

## Installation

1. Add the SDK Chatbot to your `pubspec.yaml` file:

```yaml
dependencies:
  sdk_chatbot: ^0.0.1  # Use the latest version
```

2. Run the following command to install the package:

```bash
flutter pub get
```

3. Import the package in your Dart code:

```dart
import 'package:sdk_chatbot/sdk_chatbot.dart';
```

## Basic Integration

### Step 1: Create a ChatConfig

First, create a configuration object for your chatbot:

```dart
final chatConfig = ChatConfig(
  apiEndpoint: 'https://your-api-endpoint.com/chat',
  primaryColor: Colors.blue,  // Customize to match your brand
  initialGreeting: 'Hello! How can I help you today?',
);
```

### Step 2: Add the ChatButton to your Scaffold

```dart
Scaffold(
  appBar: AppBar(title: Text('My App')),
  body: YourAppContent(),
  floatingActionButton: ChatButton(
    config: chatConfig,
  ),
);
```

That's it! You now have a basic chat button that will display a chat interface when tapped.

## Advanced Configuration

### Custom Styling

You can customize the appearance of your chat button and interface:

```dart
final chatConfig = ChatConfig(
  apiEndpoint: 'https://your-api-endpoint.com/chat',
  primaryColor: Colors.purple,
  secondaryColor: Colors.purpleAccent,
  textColor: Colors.white,
  backgroundColor: Colors.grey[100],
  buttonText: 'Get Help',
  buttonIcon: Icons.support_agent,
  initialGreeting: 'Welcome to our support chat! How can we assist you today?',
);

ChatButton(
  config: chatConfig,
  alignment: Alignment.bottomLeft,  // Change position
  padding: EdgeInsets.all(24.0),    // Change padding
  size: 70.0,                       // Make button larger
  showLabel: true,                  // Show text on button
  shape: BoxShape.rectangle,        // Use rectangular button
);
```

### API Authentication

If your chatbot API requires authentication:

```dart
final chatConfig = ChatConfig(
  apiEndpoint: 'https://your-api-endpoint.com/chat',
  apiKey: 'your-secret-api-key',  // Will be added to API requests
);
```

### Event Callbacks

You can listen for chat events:

```dart
ChatButton(
  config: chatConfig,
  onChatOpen: () {
    // Analytics tracking
    analyticsService.trackEvent('chat_opened');
  },
  onChatClose: () {
    // Maybe show a feedback prompt
    showFeedbackDialog();
  },
);
```

## Backend API Requirements

Your backend API should be set up to:

1. Accept POST requests to the endpoint you specified in `apiEndpoint`
2. Expect JSON request body in the format:
   ```json
   {
     "message": "User's message text"
   }
   ```
3. Return JSON response in the format:
   ```json
   {
     "response": "Bot's response message"
   }
   ```

If you're using API authentication, the SDK will include your API key in the request headers:
```
Authorization: Bearer your-api-key
```

## Troubleshooting

### Common Issues

1. **Chat button doesn't appear**
   - Ensure your Scaffold doesn't have another widget in the floatingActionButton position
   - Check if the button is hidden behind another UI element

2. **API communication errors**
   - Verify your API endpoint is correct and accessible
   - Check that your API returns JSON in the expected format
   - Make sure you have internet permissions in your app manifests

3. **Styling issues**
   - If text colors aren't visible, ensure your color combinations have sufficient contrast
   - For position issues, try different alignment values

## Support

If you encounter any issues with the SDK Chatbot, please open an issue on our GitHub repository.

## License

The SDK Chatbot is licensed under the MIT License.
