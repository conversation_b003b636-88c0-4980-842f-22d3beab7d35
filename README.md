# VinIT AI

# Introduction

This repo is the home for all of the AI code produced by the VinIT AI .

The project includes a collection of modules and libraries for data collection, processing, and analysis, as well as models for predictive analytics, restapi service.

# Package Index

- [Smart City](services/smart-city): Detect abnormal objects and analyze video content from the camera.

# Requirements

- [Python3](https://docs.python.org/3/whatsnew/3.11.html)
- [Poetry](https://python-poetry.org/) ^2.1


Internally we already tested in Windows 11, Ubuntu, Macos. Other OS may work but are officially unsupported. (Unofficially, if building on Linux you should install python-dev and build-essential in addition to the above).

# General installation and working guide

[Setup Guidelines](services/smart-city/README.md)

# Principles

There are four principles in VinIT AI monorepo:

- **Scopes**: The folders act as scopes to make sure code artifacts are only visible when they should be. This allows to extract common tasks (e.g. building a C# solution) quickly and maintainers can easier reason about where the error lies.
- **The One Version Rule (Atomic Commits)**: The principle guarantee that you can commit **_atomically_** to both of related projects simultaneously. There is no view of the repository where Project A is at Commit #1 but Project B is at Commit #2.
- **Big pictures**: With everything in one place there is no need to copy code between repositories or to look for infrastructure as code files and documentation.
- **Good practice**: A monorepo requires teams to work with each other. By merging code only with a MR, teams review each other’s code which breaks silos and improves code quality.

# Large Change rule

If you own a piece of code that a lot of people depend on, it can be very difficult to upgrade that piece of code, because any change you make will break somebody. You can’t fork your codebase, move everybody who depends on you incrementally to the new version, and then delete the old version. Instead, when you make a breaking change you have to either:

1. Commit to every project that depends on you, all at once.
2. Do a backward compatibility where you create a new function with no callers, commit that, then move your callers to use the new function over lots of commits, then delete the old function.