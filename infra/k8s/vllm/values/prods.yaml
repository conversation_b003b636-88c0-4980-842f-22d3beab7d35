# -- Default values for llmstack helm chart - PRODUCTION
# -- Declare variables to be passed into your templates.

servingEngineSpec:
  enableEngine: true
  labels:
    environment: "production"
    release: "stable"
  modelSpec:
  - name: "llama2-70b"
    repository: "lmcache/vllm-openai"
    tag: "latest"
    modelURL: "Qwen/Qwen2.5-VL-3B-Instruct-AWQ"
    replicaCount: 2  # Production needs higher availability
    requestCPU: 24
    requestMemory: "128Gi"
    requestGPU: 4
    pvcStorage: "100Gi"
    pvcAccessMode:
      - ReadWriteOnce
    pvcMatchLabels:
      model: "Qwen2.5-VL-3B-Instruct-AWQ"
    vllmConfig:
      enableChunkedPrefill: true
      enablePrefixCaching: true
      maxModelLen: 4096  # Or higher, depending on the model
      dtype: "half"
      extraArgs: ["--gpu-memory-utilization", "0.9", "--limit-mm-per-prompt", "image=5,video=5"]
    lmcacheConfig:
      enabled: true
      cpuOffloadingBufferSize: "60"

  containerPort: 8000
  servicePort: 80

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  startupProbe:
    initialDelaySeconds: 30
    periodSeconds: 10
    failureThreshold: 30
    httpGet:
      path: /health
      port: 8000

  livenessProbe:
    initialDelaySeconds: 30
    failureThreshold: 3
    periodSeconds: 10
    httpGet:
      path: /health
      port: 8000

  maxUnavailablePodDisruptionBudget: "1"

  tolerations:
    - key: "nvidia.com/gpu.present"
      operator: "Exists"
      effect: "NoSchedule"

  runtimeClassName: "nvidia"

  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    runAsGroup: 1001
    fsGroup: 1001

  containerSecurityContext:
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true

routerSpec:
  repository: "lmcache/lmstack-router"
  tag: "latest" # Consider using a specific, tested tag
  imagePullPolicy: "Always"
  enableRouter: true
  replicaCount: 3 # production router needs higher availability
  containerPort: 8000
  servicePort: 80
  serviceDiscovery: "k8s"
  routingLogic: "roundrobin"
  extraArgs: []
  engineScrapeInterval: 15
  requestStatsWindow: 60

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  resources:
    requests:
      cpu: "8"
      memory: "32G"
    limits:
      cpu: "16"
      memory: "64G"

  labels:
    environment: "production-router"
    release: "stable"

  ingress:
    enabled: true # Enable Ingress for production
    className: "nginx" # Or your ingress controller class
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-prod" # Use a prod cert issuer
      nginx.ingress.kubernetes.io/proxy-body-size: "0" # Allow large payloads
      nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
      nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
      nginx.ingress.kubernetes.io/rewrite-target: /$1
    hosts:
      - host: vllm.example.com # Replace with your domain
        paths:
          - path: /
            pathType: Prefix

    tls:
      - secretName: vllm-example-com-tls # Replace with your TLS secret
        hosts:
          - vllm.example.com

  nodeSelectorTerms:
    - matchExpressions:
        - key: cloud.provider.com/instance-type
          operator: In
          values:
            - "g4dn.xlarge" # Replace with your preferred instance type

