# -- Default values for llmstack helm chart - STAGING
# -- Declare variables to be passed into your templates.

servingEngineSpec:
  enableEngine: true
  labels:
    environment: "staging"
    release: "canary"
  modelSpec:
  - name: "Qwen2.5-VL-3b"
    repository: "lmcache/vllm-openai"
    tag: "latest"
    modelURL: Qwen/Qwen2.5-VL-3B-Instruct-AWQ
    replicaCount: 1 # Staging can be scaled down
    requestCPU: 6
    requestMemory: "32Gi"
    requestGPU: 1
    pvcStorage: "50Gi"
    pvcAccessMode:
      - ReadWriteOnce
    pvcMatchLabels:
      model: "Qwen2.5-VL-3b"
    vllmConfig:
      enableChunkedPrefill: false
      enablePrefixCaching: false
      maxModelLen: 2048
      dtype: "half"
      extraArgs: ["--gpu-memory-utilization", "0.9", "--limit-mm-per-prompt", "image=5,video=5"]
    lmcacheConfig:
      enabled: true
      cpuOffloadingBufferSize: "30"

  containerPort: 8000
  servicePort: 80

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  startupProbe:
    initialDelaySeconds: 15
    periodSeconds: 10
    failureThreshold: 60
    httpGet:
      path: /health
      port: 8000

  livenessProbe:
    initialDelaySeconds: 15
    failureThreshold: 3
    periodSeconds: 10
    httpGet:
      path: /health
      port: 8000

  maxUnavailablePodDisruptionBudget: "1"

  tolerations:
    - key: "nvidia.com/gpu.present"
      operator: "Exists"
      effect: "NoSchedule"

  runtimeClassName: "nvidia"

  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    runAsGroup: 1001
    fsGroup: 1001

  containerSecurityContext:
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true

routerSpec:
  repository: "lmcache/lmstack-router"
  tag: "latest" # Can be a different tag from production, e.g., "dev"
  imagePullPolicy: "Always"
  enableRouter: true
  replicaCount: 1 # scaled down
  containerPort: 8000
  servicePort: 80
  serviceDiscovery: "k8s"
  routingLogic: "roundrobin"
  extraArgs: []
  engineScrapeInterval: 15
  requestStatsWindow: 60

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  resources:
    requests:
      cpu: "4"
      memory: "16G"
    limits:
      cpu: "8"
      memory: "32G"

  labels:
    environment: "staging-router"
    release: "canary"

  ingress:
    enabled: true # Enable Ingress for staging
    className: "nginx" # Or your ingress controller class
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-staging" # Use a staging cert issuer
      nginx.ingress.kubernetes.io/proxy-body-size: "0"
      nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
      nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
      nginx.ingress.kubernetes.io/rewrite-target: /$1
    hosts:
      - host: vllm-staging.example.com # Replace with your staging domain
        paths:
          - path: /
            pathType: Prefix

    tls:
      - secretName: vllm-staging-example-com-tls # Replace with your TLS secret
        hosts:
          - vllm-staging.example.com

  nodeSelectorTerms:
    - matchExpressions:
        - key: cloud.provider.com/instance-type
          operator: In
          values:
            - "g4dn.xlarge" # or other suitable instance type