{{- if .Values.routerSpec.enableRouter -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ .Release.Name }}-deployment-access-binding
  namespace: {{ .Release.Namespace }}
subjects:
  - kind: ServiceAccount
    name: {{ .Release.Name }}-router-service-account
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: {{ .Release.Name }}-pod-reader
  apiGroup: rbac.authorization.k8s.io
{{- end }}
