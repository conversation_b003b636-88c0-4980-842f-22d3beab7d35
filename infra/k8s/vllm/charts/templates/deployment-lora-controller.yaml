{{- if and .Values.loraController .Values.loraController.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lora-controller
  namespace: {{ .Release.Namespace }}
  labels:
    app: lora-controller
    {{- include "chart.engineLabels" . | nindent 4 }}
spec:
  replicas: {{ .Values.loraController.replicaCount | default 1 }}
  selector:
    matchLabels:
      app: lora-controller
      {{- include "chart.engineLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: lora-controller
        {{- include "chart.engineLabels" . | nindent 8 }}
      {{- with .Values.loraController.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: lora-controller
      {{- with .Values.loraController.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: controller
          image: "{{ .Values.loraController.image.repository }}:{{ .Values.loraController.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.loraController.image.pullPolicy }}
          env:
            - name: ADAPTER_DOWNLOAD_PATH
              value: "/models"
            {{- with .Values.loraController.extraEnv }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          volumeMounts:
            - name: shared-model-storage
              mountPath: /models
            {{- with .Values.loraController.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          resources:
            {{- toYaml .Values.loraController.resources | nindent 12 }}
          {{- with .Values.loraController.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: shared-model-storage
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-shared-storage-claim
        {{- with .Values.loraController.extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.loraController.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.loraController.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.loraController.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.loraController.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
