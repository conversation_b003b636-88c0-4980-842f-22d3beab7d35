{{- if and .Values.loraController .Values.loraController.enabled -}}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: loraadapters.production-stack.vllm.ai
spec:
  group: production-stack.vllm.ai
  names:
    kind: LoraAdapter
    listKind: LoraAdapterList
    plural: loraadapters
    singular: loraadapter
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: LoraAdapter is the Schema for the loraadapters API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: LoraAdapterSpec defines the desired state of LoraAdapter
            properties:
              adapterSource:
                description: AdapterSource defines where to get the LoRA adapter from
                properties:
                  adapterName:
                    description: AdapterName is the name of the adapter to apply
                    type: string
                  adapterPath:
                    description: 'AdapterPath is the path to the LoRA adapter weights. For local sources: required, specifies the path to the adapter For remote sources: optional, will be updated by the controller with the download path'
                    type: string
                  credentialsSecretRef:
                    description: CredentialsSecretRef references a secret containing storage credentials
                    properties:
                      name:
                        description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  maxAdapters:
                    description: MaxAdapters is the maximum number of adapters to load
                    format: int32
                    type: integer
                  pattern:
                    description: Pattern is a regex pattern to filter adapters (for s3/cos)
                    type: string
                  repository:
                    description: Repository is the repository where the adapter is stored
                    type: string
                  type:
                    description: Type is the type of adapter source (e.g., "local", "s3", "http")
                    type: string
                required:
                - adapterName
                - repository
                - type
                type: object
              baseModel:
                description: BaseModel is the name of the base model this adapter is for
                type: string
              deploymentConfig:
                description: DeploymentConfig defines how the adapter should be deployed
                properties:
                  algorithm:
                    default: default
                    description: Algorithm specifies which placement algorithm to use
                    enum:
                    - default
                    - ordered
                    - equalized
                    type: string
                  replicas:
                    description: Replicas is the number of replicas that should load this adapter
                    format: int32
                    minimum: 0
                    type: integer
                required:
                - algorithm
                type: object
            required:
            - adapterSource
            - baseModel
            type: object
          status:
            description: LoraAdapterStatus defines the observed state of LoraAdapter
            properties:
              conditions:
                description: Conditions represent the latest available observations of the adapter's state
                items:
                  description: "Condition contains details for one aspect of the current state of this API Resource."
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition transitioned from one status to another.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating details about the transition.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation that the condition was set based upon.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating the reason for the condition's last transition.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              loadedAdapters:
                description: LoadedAdapters tracks the loading status of adapters and their pod assignments
                items:
                  description: LoadedAdapter represents an adapter that has been loaded into a pod
                  properties:
                    loadTime:
                      description: LoadTime is when the adapter was loaded
                      format: date-time
                      type: string
                    name:
                      description: Name is the name of the adapter
                      type: string
                    path:
                      description: Path is the path where the adapter is loaded
                      type: string
                    podAssignments:
                      description: PodAssignments represents the pods this adapter has been assigned to
                      items:
                        description: PodAssignment represents a pod that has been assigned to load this adapter
                        properties:
                          pod:
                            description: Pod represents the pod information
                            properties:
                              apiVersion:
                                description: API version of the referent.
                                type: string
                              fieldPath:
                                description: If referring to a piece of an object instead of an entire object
                                type: string
                              kind:
                                description: Kind of the referent.
                                type: string
                              name:
                                description: Name of the referent.
                                type: string
                              namespace:
                                description: Namespace of the referent.
                                type: string
                              resourceVersion:
                                description: Specific resourceVersion to which this reference is made
                                type: string
                              uid:
                                description: UID of the referent
                                type: string
                            type: object
                            x-kubernetes-map-type: atomic
                          status:
                            description: Status represents the current status of the assignment
                            type: string
                        required:
                        - pod
                        - status
                        type: object
                      type: array
                    status:
                      description: Status represents the current status of the loaded adapter
                      type: string
                  required:
                  - name
                  - path
                  - podAssignments
                  - status
                  type: object
                type: array
              message:
                description: Message provides additional information about the current phase
                type: string
              phase:
                description: Phase represents the current phase of the adapter deployment
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}
