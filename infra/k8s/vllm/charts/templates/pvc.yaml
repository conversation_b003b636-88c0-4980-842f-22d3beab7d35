
{{- if .Values.servingEngineSpec.enableEngine -}}
{{- range $modelSpec := .Values.servingEngineSpec.modelSpec }}
{{- with $ -}}
{{- if and (hasKey $modelSpec "pvcStorage") (not (empty $modelSpec.pvcStorage)) }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: "{{ .Release.Name }}-{{$modelSpec.name}}-storage-claim"
  namespace: {{ .Release.Namespace }}
spec:
  accessModes:
    {{- if $modelSpec.pvcAccessMode }}
    {{- toYaml $modelSpec.pvcAccessMode | nindent 4 }}
    {{- else }}
    - ReadWriteOnce
    {{- end }}
  resources:
    requests:
      storage: {{ $modelSpec.pvcStorage | default "20Gi" }}  # Default to 20Gi if not set
  {{- if hasKey $modelSpec "storageClass" }}
  storageClassName: "{{ $modelSpec.storageClass }}"
  {{- end }}
  {{- if not (empty $modelSpec.pvcMatchLabels) }}
  selector:
    matchLabels:
      {{- toYaml $modelSpec.pvcMatchLabels | nindent 8 }}
  {{- end }}
{{- end }}
{{- end }}
---
{{- end }}
{{- end }}
