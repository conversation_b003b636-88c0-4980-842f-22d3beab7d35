{{- if .Values.servingEngineSpec.enableEngine -}}
{{- range $modelSpec := .Values.servingEngineSpec.modelSpec }}
{{- with $ -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Release.Name }}-{{$modelSpec.name}}-deployment-vllm"
  namespace: {{ .Release.Namespace }}
  labels:
    model: {{ $modelSpec.name }}
  {{- include "chart.engineLabels" . | nindent 4 }}
spec:
  replicas: {{ $modelSpec.replicaCount }}
  {{- include "chart.engineStrategy" . | nindent 2 }}
  selector:
    matchLabels:
    {{- include "chart.engineLabels" . | nindent 6 }}
  progressDeadlineSeconds: 1200
  template:
    metadata:
      labels:
        model: {{ $modelSpec.name }}
      {{- include "chart.engineLabels" . | nindent 8 }}
    spec:
      {{- if hasKey $modelSpec "initContainer" }}
      {{- $container := $modelSpec.initContainer }}
      initContainers:
        - name: {{ $container.name }}
          image: {{ $container.image }}
          {{- if $container.command }}
          command: {{ toYaml $container.command | nindent 12 }}
          {{- end }}
          {{- if $container.args }}
          args: {{ toYaml $container.args | nindent 12 }}
          {{- end }}
          {{- if $container.env }}
          env: {{ toYaml $container.env | nindent 12 }}
          {{- end }}
          {{- if $container.resources }}
          resources: {{ toYaml $container.resources | nindent 12 }}
          {{- end }}
          {{- if hasKey $container "mountPvcStorage" }}
          volumeMounts:
          {{- if and $container.volumeMounts.pvcStorage (hasKey $modelSpec "pvcStorage") }}
            - name: {{ .Release.Name }}-storage
              mountPath: /data
          {{- end }}
          {{- end }}
      {{- end }}
      {{- if .Values.servingEngineSpec.securityContext }}
      securityContext:
        {{- toYaml .Values.servingEngineSpec.securityContext | nindent 8 }}
      {{- end }}
      containers:
        - name: "vllm"
          image: "{{ required "Required value 'modelSpec.repository' must be defined !" $modelSpec.repository }}:{{ required "Required value 'modelSpec.tag' must be defined !" $modelSpec.tag }}"

          command:
          - "vllm"
          - "serve"
          - {{ $modelSpec.modelURL | quote }}
          - "--host"
          - "0.0.0.0"
          - "--port"
          - {{ include "chart.container-port" . | quote }}
          {{- if $modelSpec.enableLoRA }}
          - "--enable-lora"
          {{- end }}
          {{- if $modelSpec.enableTool }}
          - "--enable-auto-tool-choice"
          {{- end }}
          {{- if $modelSpec.toolCallParser }}
          - "--tool-call-parser"
          - {{ $modelSpec.toolCallParser | quote }}
          {{- end }}
          {{- with $modelSpec.vllmConfig }}
          {{-   if hasKey . "enableChunkedPrefill" }}
          - "--enable-chunked-prefill"
          - {{ .enableChunkedPrefill | quote }}
          {{-   end }}
          {{-   if .enablePrefixCaching }}
          - "--enable-prefix-caching"
          {{-   end }}
          {{-   if hasKey . "maxModelLen" }}
          - "--max-model-len"
          - {{ .maxModelLen | quote }}
          {{-   end }}
          {{-   if hasKey . "dtype" }}
          - "--dtype"
          - {{ .dtype | quote }}
          {{-   end }}
          {{-   if hasKey . "tensorParallelSize" }}
          - "--tensor-parallel-size"
          - {{ .tensorParallelSize | quote }}
          {{-   end }}
          {{-   if .extraArgs }}
          {{-     range .extraArgs }}
          - {{ . | quote }}
          {{-     end }}
          {{-   end }}
          {{- end }}
          {{- if $modelSpec.lmcacheConfig }}
          {{-   if $modelSpec.lmcacheConfig.enabled }}
          - "--kv-transfer-config"
          - '{"kv_connector":"LMCacheConnector","kv_role":"kv_both"}'
          {{-   end }}
          {{- end }}
          {{- if $modelSpec.chatTemplate }}
          - "--chat-template"
          - {{ $modelSpec.chatTemplate | quote }}
          {{- end }}
          {{- if .Values.servingEngineSpec.containerSecurityContext }}
          securityContext:
            {{- toYaml .Values.servingEngineSpec.containerSecurityContext | nindent 12 }}
          {{- end }}
          imagePullPolicy: IfNotPresent
          env:
          - name: HF_HOME
            {{- if hasKey $modelSpec "pvcStorage" }}
            value: /data
            {{- else }}
            value: /tmp
            {{- end }}
          {{- with $modelSpec.vllmConfig}}
          {{- if hasKey . "v1" }}
          - name: VLLM_USE_V1
            value: {{ default 0 $modelSpec.vllmConfig.v1 | quote }}
          {{- end}}
          {{- end}}
          {{- if $modelSpec.hf_token }}
          - name: HF_TOKEN
            {{- if kindIs "string" $modelSpec.hf_token }}
            valueFrom:
              secretKeyRef:
                name: {{ .Release.Name }}-secrets
                key: hf_token_{{ $modelSpec.name }}
            {{- else }}
            valueFrom:
              secretKeyRef:
                name: {{ $modelSpec.hf_token.secretName }}
                key: {{ $modelSpec.hf_token.secretKey }}
            {{- end }}
          {{- end }}
          {{- $vllmApiKey := $.Values.servingEngineSpec.vllmApiKey }}
          {{- if $vllmApiKey }}
          - name: VLLM_API_KEY
            {{- if kindIs "string" $vllmApiKey }}
            valueFrom:
              secretKeyRef:
                name: {{ .Release.Name }}-secrets
                key: vllmApiKey
            {{- else }}
            valueFrom:
              secretKeyRef:
                name: {{ $vllmApiKey.secretName }}
                key: {{ $vllmApiKey.secretKey }}
            {{- end }}
          {{- end }}
          {{- with $modelSpec.env }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- if $modelSpec.lmcacheConfig }}
          {{-   if $modelSpec.lmcacheConfig.enabled }}
          - name: LMCACHE_USE_EXPERIMENTAL
            value: "True"
          - name: VLLM_RPC_TIMEOUT
            value: "1000000"
          {{-   end }}
          {{-   if $modelSpec.lmcacheConfig.cpuOffloadingBufferSize }}
          - name: LMCACHE_LOCAL_CPU
            value: "True"
          - name: LMCACHE_MAX_LOCAL_CPU_SIZE
            value: "{{ $modelSpec.lmcacheConfig.cpuOffloadingBufferSize }}"
          {{-   end }}
          {{-   if $modelSpec.lmcacheConfig.diskOffloadingBufferSize }}
          - name: LMCACHE_LOCAL_DISK
            value: "True"
          - name: LMCACHE_MAX_LOCAL_DISK_SIZE
            value: "{{ $modelSpec.lmcacheConfig.diskOffloadingBufferSize }}"
          {{-   end }}
          {{-   if .Values.cacheserverSpec }}
          - name: LMCACHE_REMOTE_URL
            value: "{{ include "cacheserver.formatRemoteUrl" (dict "service_name" (print .Release.Name "-cache-server-service") "port" .Values.cacheserverSpec.servicePort) }}"
          - name: LMCACHE_REMOTE_SERDE
            value: "{{ .Values.cacheserverSpec.serde }}"
          {{-   end }}
          {{- end }}
          {{- if .Values.servingEngineSpec.configs }}
          envFrom:
            - configMapRef:
                name: "{{ .Release.Name }}-configs"
          {{- end }}
          ports:
            - name: {{ include "chart.container-port-name" . }}
              containerPort: {{ include "chart.container-port" . }}
          {{- include "chart.probes" . | indent 10 }}
          resources: {{- include "chart.resources" $modelSpec | nindent 12 }}
          {{- if or (hasKey $modelSpec "pvcStorage") (and $modelSpec.vllmConfig (hasKey $modelSpec.vllmConfig "tensorParallelSize")) (hasKey $modelSpec "chatTemplate") (hasKey $modelSpec "extraVolumeMounts") }}
          volumeMounts:
          {{- end }}
          {{- if hasKey $modelSpec "pvcStorage" }}
          - name: {{ .Release.Name }}-storage
            mountPath: /data
          {{- end }}
          {{- with $modelSpec.vllmConfig }}
          {{- if hasKey $modelSpec.vllmConfig "tensorParallelSize"}}
          - name: shm
            mountPath: /dev/shm
          {{- end}}
          {{- end}}
          {{- if $modelSpec.chatTemplate }}
          - name: vllm-templates
            mountPath: /templates
          {{- end }}
          {{- if hasKey $modelSpec "extraVolumeMounts" }}
          {{- toYaml $modelSpec.extraVolumeMounts | nindent 10 }}
          {{- end }}
      {{- if $modelSpec.imagePullSecret }}
      imagePullSecrets:
        - name: {{ $modelSpec.imagePullSecret }}
      {{- end }}
      {{- if or (hasKey $modelSpec "pvcStorage") (and $modelSpec.vllmConfig (hasKey $modelSpec.vllmConfig "tensorParallelSize")) (hasKey $modelSpec "chatTemplate") (hasKey $modelSpec "extraVolumes") }}
      volumes:
      {{- end}}
        {{- if hasKey $modelSpec "pvcStorage" }}
        - name: {{ .Release.Name }}-storage
          persistentVolumeClaim:
            claimName: "{{ .Release.Name }}-{{$modelSpec.name}}-storage-claim"
        {{- end }}
        {{- with $modelSpec.vllmConfig }}
        {{- if hasKey $modelSpec.vllmConfig "tensorParallelSize"}}
        - name: shm
          emptyDir:
            medium: Memory
            sizeLimit: {{ default "20Gi" $modelSpec.shmSize }}
        {{- end}}
        {{- end}}
        {{- if $modelSpec.chatTemplate}}
        {{- if hasKey $modelSpec "chatTemplateConfigMap" }}
        - name: {{ .Release.Name }}-chat-templates
          configMap:
            name: "{{ .Release.Name }}-{{$modelSpec.name}}-chat-templates"
        {{- else }}
        - name: vllm-templates
          persistentVolumeClaim:
            claimName: vllm-templates-pvc
        {{- end }}
        {{- end}}
        {{- if hasKey $modelSpec "extraVolumes" }}
        {{- toYaml $modelSpec.extraVolumes | nindent 8 }}
        {{- end}}
      {{- if .Values.servingEngineSpec.tolerations }}
      {{-   with .Values.servingEngineSpec.tolerations }}
      tolerations:
        {{-   toYaml . | nindent 8 }}
      {{-   end }}
      {{- end }}

      {{- if .Values.servingEngineSpec.runtimeClassName }}
      runtimeClassName: {{ .Values.servingEngineSpec.runtimeClassName }}
      {{- end }}
      {{- if .Values.servingEngineSpec.schedulerName }}
      schedulerName: {{ .Values.servingEngineSpec.schedulerName }}
      {{- end }}
      {{- if $modelSpec.nodeSelectorTerms}}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- with $modelSpec.nodeSelectorTerms }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
      {{- end }}
{{- if and $modelSpec.chatTemplate (hasKey $modelSpec "chatTemplateConfigMap") }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ .Release.Name }}-{{$modelSpec.name}}-chat-templates"
  namespace: "{{ .Release.Namespace }}"
data:
  {{ $modelSpec.chatTemplate }}: |-
    {{ $modelSpec.chatTemplateConfigMap }}
{{- end }}
{{- end }}
---
{{- end }}
{{- end }}
