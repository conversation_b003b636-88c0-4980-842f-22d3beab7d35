{{- if and .Values.loraController .Values.loraController.enabled -}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: lora-controller
  namespace: {{ .Release.Namespace }}
  labels:
    app: lora-controller
    {{- include "chart.engineLabels" . | nindent 4 }}
  {{- if .Values.loraController.serviceAccount }}
  {{- with .Values.loraController.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Release.Name }}-lora-controller
  labels:
    app: lora-controller
    {{- include "chart.engineLabels" . | nindent 4 }}
rules:
# Access to core resources
- apiGroups: [""]
  resources: ["pods", "services", "secrets", "configmaps", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
# Needed to update pod status when adapters are loaded/unloaded
- apiGroups: [""]
  resources: ["pods/status"]
  verbs: ["get", "update", "patch"]
# Can create events to notify about adapter status
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
# Access to watch deployments that use LoRA
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
# Full control over LoraAdapter resources
- apiGroups: ["production-stack.vllm.ai"]
  resources: ["loraadapters"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
# Ability to update LoraAdapter status
- apiGroups: ["production-stack.vllm.ai"]
  resources: ["loraadapters/status"]
  verbs: ["get", "update", "patch"]
# Needed to know about existing CRDs
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["get", "list", "watch"]
---
# Provides role binding within a specific namespace
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: lora-controller
  namespace: {{ .Release.Namespace }}
  labels:
    app: lora-controller
    {{- include "chart.engineLabels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: lora-controller
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ .Release.Name }}-lora-controller
  apiGroup: rbac.authorization.k8s.io
---
# Provides cluster-wide permissions to watch all namespaces if watchNamespaces isn't limited
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Release.Name }}-lora-controller
  labels:
    app: lora-controller
    {{- include "chart.engineLabels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: lora-controller
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ .Release.Name }}-lora-controller
  apiGroup: rbac.authorization.k8s.io
{{- end }}
