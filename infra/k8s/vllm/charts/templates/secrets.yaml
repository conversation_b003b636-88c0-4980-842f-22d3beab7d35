{{- if .Values.servingEngineSpec.enableEngine -}}
apiVersion: v1
kind: Secret
metadata:
  name: "{{ .Release.Name }}-secrets"
  namespace: {{ .Release.Namespace }}
type: Opaque
data:
  {{- $vllmApiKey := $.Values.servingEngineSpec.vllmApiKey }}
  {{- if and $vllmApiKey (kindIs "string" $vllmApiKey) }}
  vllmApiKey: {{ $vllmApiKey | b64enc | quote }}
  {{- end }}

  {{- range $modelSpec := .Values.servingEngineSpec.modelSpec }}
  {{- with $ -}}
  {{-   if and $modelSpec.hf_token (kindIs "string" $modelSpec.hf_token) }}
  hf_token_{{ $modelSpec.name }}: {{ $modelSpec.hf_token | b64enc | quote }}
  {{-   end }}
  {{- end }}
  {{- end }}
{{- end }}
