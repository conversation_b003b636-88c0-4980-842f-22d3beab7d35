server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html;
    index index.html index.htm index.nginx-debian.html;
    server_name dev-ai.vinhomes.vn;

    # These headers are global, but often placed per-location if needed
    # It's fine here for simplicity if all proxies need them
    proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header   X-Real-IP $remote_addr;
    proxy_set_header   Host $host; # Use $host, or $http_host if you need original Host header.
                                   # $host:$server_port is usually not needed unless backend needs <PERSON>inx's listening port.
    proxy_set_header   X-Forwarded-Proto $scheme; # Important for HTTPS later

    send_timeout       10m;
    proxy_buffer_size 512k;
    proxy_buffers 4 1024k;
    proxy_busy_buffers_size 1024k;
    proxy_redirect off;

    # CORS headers
    location / {
        try_files $uri $uri/ =404;
        
        if ($request_method = 'OPTIONS') {
            # Whitelist the origin. For security, be specific.
            # Using $http_origin dynamically allows the origin that made the request.
            # If you need to restrict to specific origins, use a map or multiple 'if' conditions.

            add_header 'Access-Control-Allow-Origin' "$http_origin" always; # Reflect the request's origin

            # Allowed methods (must include what was in Access-Control-Request-Method)
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;

            # Allowed headers (must include what was in Access-Control-Request-Headers)
            # You can be more specific or use '*' if you trust all requested headers.
            # Reflecting $http_access_control_request_headers is often a good dynamic approach.
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            # Or, be explicit if you know them:
            # add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;

            # Allow cookies and authorization headers
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Max age for preflight request to be cached by browser (in seconds)
            add_header 'Access-Control-Max-Age' 1728000 always; # 20 days

            # Tell Nginx to basically ignore the body for OPTIONS and return 204
            add_header 'Content-Type' 'text/plain; charset=utf-8'; # Minimal content type
            add_header 'Content-Length' 0;
            return 204;
        }
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }

    # For chabot app if it's also a standard HTTP proxy
    location /chatbot/ {
        # Remove uwsgi_params if chabot is not a uWSGI app
        # include uwsgi_params;
        proxy_pass    http://127.0.0.1:8000/; # Note trailing slash if you want to strip /chabot/
                                              # Or keep as is if chabot expects /chabot/
         # --- Streaming Specific Configuration ---
        proxy_buffering off;                 # Crucial: Disable response buffering from backend
        proxy_read_timeout 300s;             # Keep connection open for 5 mins of inactivity from backend.
                                             # Adjust as needed (e.g., 900s, 1h).
        proxy_http_version 1.1;              # Recommended for keep-alive and streaming
        proxy_set_header Connection "";      # Clear Connection header from client for Nginx to manage its own
        proxy_set_header Cache-Control "no-cache";


        # Preflight OPTIONS handling
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always; # Reflect the request's origin

            # Allowed methods (must include what was in Access-Control-Request-Method)
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;

            # Allowed headers (must include what was in Access-Control-Request-Headers)
            # You can be more specific or use '*' if you trust all requested headers.
            # Reflecting $http_access_control_request_headers is often a good dynamic approach.
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            # Or, be explicit if you know them:
            # add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;

            # Allow cookies and authorization headers
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Max age for preflight request to be cached by browser (in seconds)
            add_header 'Access-Control-Max-Age' 1728000 always; # 20 days

            # Tell Nginx to basically ignore the body for OPTIONS and return 204
            add_header 'Content-Type' 'text/plain; charset=utf-8'; # Minimal content type
            add_header 'Content-Length' 0;
            return 204;
        }
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }

    location /hrai/ {
        # REMOVE uwsgi_params, it's for uWSGI protocol, not standard HTTP proxying
        # include uwsgi_params;

        # Add a trailing slash to proxy_pass URL to strip the /hrai/ prefix
        proxy_pass    http://127.0.0.1:7000/;

        # These headers are already set globally, but if you moved them
        # from global, you'd need them here:
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        # Preflight OPTIONS handling
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always; # Reflect the request's origin

            # Allowed methods (must include what was in Access-Control-Request-Method)
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;

            # Allowed headers (must include what was in Access-Control-Request-Headers)
            # You can be more specific or use '*' if you trust all requested headers.
            # Reflecting $http_access_control_request_headers is often a good dynamic approach.
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            # Or, be explicit if you know them:
            # add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;

            # Allow cookies and authorization headers
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Max age for preflight request to be cached by browser (in seconds)
            add_header 'Access-Control-Max-Age' 1728000 always; # 20 days

            # Tell Nginx to basically ignore the body for OPTIONS and return 204
            add_header 'Content-Type' 'text/plain; charset=utf-8'; # Minimal content type
            add_header 'Content-Length' 0;
            return 204;
        }
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }

    location /chatbot-demo/ {
        proxy_pass http://127.0.0.1:8501/;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;

        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        # WebSocket support (important for Streamlit)
        proxy_read_timeout 86400;

        # CORS headers (optional, similar to others)
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
    }

    add_header 'Access-Control-Allow-Origin' "$http_origin" always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;

    
    }
    location /chatbot-vinwonder/ {
        # Remove uwsgi_params if chabot is not a uWSGI app
        # include uwsgi_params;
        proxy_pass    http://127.0.0.1:9000/; # Note trailing slash if you want to strip /chabot/
                                              # Or keep as is if chabot expects /chabot/
         # --- Streaming Specific Configuration ---
        proxy_buffering off;                 # Crucial: Disable response buffering from backend
        proxy_read_timeout 300s;             # Keep connection open for 5 mins of inactivity from backend.
                                             # Adjust as needed (e.g., 900s, 1h).
        proxy_http_version 1.1;              # Recommended for keep-alive and streaming
        proxy_set_header Connection "";      # Clear Connection header from client for Nginx to manage its own
        proxy_set_header Cache-Control "no-cache";


        # Preflight OPTIONS handling
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always; # Reflect the request's origin

            # Allowed methods (must include what was in Access-Control-Request-Method)
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;

            # Allowed headers (must include what was in Access-Control-Request-Headers)
            # You can be more specific or use '*' if you trust all requested headers.
            # Reflecting $http_access_control_request_headers is often a good dynamic approach.
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            # Or, be explicit if you know them:
            # add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;

            # Allow cookies and authorization headers
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Max age for preflight request to be cached by browser (in seconds)
            add_header 'Access-Control-Max-Age' 1728000 always; # 20 days

            # Tell Nginx to basically ignore the body for OPTIONS and return 204
            add_header 'Content-Type' 'text/plain; charset=utf-8'; # Minimal content type
            add_header 'Content-Length' 0;
            return 204;
        }
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }

    location /chatbot-vinperl-internal/ {
        proxy_pass http://127.0.0.1:8502/;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;

        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        # WebSocket support (important for Streamlit)
        proxy_read_timeout 86400;

        # CORS headers (optional, similar to others)
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    location /chatbot-vinwonder-cs/ {
        proxy_pass http://127.0.0.1:8503/;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;

        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        # WebSocket support (important for Streamlit)
        proxy_read_timeout 86400;

        # CORS headers (optional, similar to others)
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    location /call-center-ai/ {
        proxy_pass http://127.0.0.1:8600/;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;

        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        # WebSocket support (important for Streamlit)
        proxy_read_timeout 86400;
        client_max_body_size 100m;

        # CORS headers (optional, similar to others)
        if ($request_method = OPTIONS) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' "$http_access_control_request_headers" always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }