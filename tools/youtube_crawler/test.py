import yt_dlp
import os
import re

def get_video_urls_from_channel(channel_url):
    ydl_opts = {
        'extract_flat': True,
        'quiet': True,
        'force_generic_extractor': True,
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(channel_url, download=False)
        entries = info.get('entries', [])
        video_urls = [entry['url'] for entry in entries if 'url' in entry]
        print(f"\n📺 Found {len(video_urls)} videos in channel.")
        return video_urls
    
    

channel_url = "https://www.youtube.com/@SaigonT%E1%BA%BFu/videos"
video_urls = get_video_urls_from_channel(channel_url)

print("\n📺 Video URLs: ", video_urls)