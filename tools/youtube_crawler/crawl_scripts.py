import os
import yt_dlp
from slugify import slugify


class YOUTubeCrawler:
    def __init__(
            self,
            audio_path: str = '../data/',
    ) -> None:
        self.audio_path: str = audio_path


    def get_video_urls_from_channel(self, channel_url: str):
        ydl_opts = {
            'extract_flat': True,
            'quiet': True,
            'force_generic_extractor': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(channel_url, download=False)
            entries = info.get('entries', [])
            video_urls = [entry['url'] for entry in entries if 'url' in entry]
            print(f"\n📺 Found {len(video_urls)} videos in channel.")
            return video_urls
        


    def download_audio_and_subs(self, url: str):
        # Step 1: Get video info to create folder
        with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'video')
            alias = slugify(title)
            print(f"Video title: {title}\nSaving to folder: {alias}")

        # Step 2: Create folder
        # os.makedirs(alias, exist_ok=True)

        # Step 3: Define download options
        ydl_opts = {
            'format': 'bestaudio/best',
            'outtmpl': os.path.join(self.audio_path, alias, 'audio.%(ext)s'),
            'writesubtitles': True,
            'subtitleslangs': ['vi'],
            'subtitlesformat': 'srt',
            'writeautomaticsub': True,  # Try auto-sub if manual is unavailable
            'postprocessors': [
                {
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',  # Or 'm4a'
                    'preferredquality': '192',
                }
            ]
        }

        # Step 4: Download audio + subtitles
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])
            print("✅ Download complete.")


youtube_crawler = YOUTubeCrawler()

# Example usage
yt_url = "https://www.youtube.com/watch?v=H8Y5k_6UpDg&t=171s&ab_channel=SaigonTếu"
yt_url = "https://www.youtube.com/watch?v=ELkyvYLkgpM"
yt_url = "https://www.youtube.com/watch?v=Y2-QJsbv1WQ&list=RDY2-QJsbv1WQ&start_radio=1&ab_channel=LemonzRemix"
srt = youtube_crawler.download_audio_and_subs(yt_url)

print(f"Subtitle saved to: {srt}")