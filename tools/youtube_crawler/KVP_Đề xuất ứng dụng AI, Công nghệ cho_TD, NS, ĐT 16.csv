ĐỀ XUẤT ỨNG DỤNG AI;;ĐẦU MỐI HR;NGUYỄN THỊ DUYÊN, ĐINH THỊ MINH, NGUYỄN THỊ KHÁNH;;;VINIT PHẢN HỒI;;;
;;ĐẦU MỐI VINIT;NGÔ MINH MẪN, HOÀNG THỊ MỸ HẠNH;;;;;;
STT;Hạ<PERSON> mục;<PERSON><PERSON> tả chi tiết;<PERSON><PERSON> phận;Tên ứng dụng đã tìm hiểu;<PERSON><PERSON> do đề xuất;<PERSON><PERSON><PERSON>ng;;Thời gian triển khai;<PERSON><PERSON><PERSON> định
I;Ứng dụng trong tuyển dụng - thủ tục nhân sự;;;;;;Khối phụ trách;;
1;Giải pháp nền tảng Tuyển dụng;VinIT xem xét và chốt PA tiếp tục cải tiến trên Base hoặc phát triển ứng dụng IloveVGR;Tuyển dụng;;;;PTPM (anh Lợi);;
2;Workflow - Tuyển dụng;;Tuyển dụng;;;;PTPM (anh Lợi);;
2,1;Tự động trong công tác tiếp nhận yêu cầu tuyển dụng (YCTD) của BP chuyên môn;"-  Tự động kiểm tra mức độ hợp lệ, đủ thông tin trong Phiếu YCTD của Bộ phận chuyên môn gửi trên hệ thống (Chức danh, SL tuyển Không vượt định biên, thời gian cần nhân sự...).
- Tự động trả về BP chuyên môn nếu Phiếu YCTD chưa hợp lệ, đủ thông tin.
- Tự động nhắc CVTD phản hồi YCTD theo thời hạn";Tuyển dụng;;Tối ưu thời gian và nhân lực;Khả thi;AI ( anh Ân) - GĐ2;;
2,2;Tự động trong Công tác đăng tin tuyển dụng;"- Tự động soạn thảo tin đăng tuyển dụng (đọc dữ liệu từ tên vị trí + Mô tả công việc)
- Tự động post tin lên các trang chính/trang liên kết (website công ty vinhomes.vn; Careers.vinhomes.vn, tuyendung.vingroup.net, FB, LinkedIn, các web tuyển dụng..)";Tuyển dụng;"Success factors
Zoho Recruit
Claude AI 
Generative AI (GenAI)";Tối ưu thời gian và nhân lực;"Khả thi,
- Ngoại trừ việc tự động post lên các trang ngoài tập đoàn (sẽ vướng bot detection)";AI ( anh Ân) - GĐ2;;
2,3;Tự động tổng hợp hồ sơ ứng viên (CV);"- Tự động tổng hợp CV từ các nguồn khác nhau (website tuyển dụng công ty, các website việc làm, email công ty, CV do CVTD upload) về chung 1 hệ thống database.
";Tuyển dụng;;;Công nghệ;PTPM (anh Lợi);;
2,4;Tự động sàng lọc hồ sơ ứng viên (CV);"- Tự động đọc CV ứng viên, nhận diện các thông tin trong CV ứng viên phù hợp với tiêu chí tuyển dụng (ở mục 2.2)
- Tự động chấm điểm mức độ (%) phù hợp
- Tự động mapping các thông tin của UV từ CV vào database: scan các loại thông tin cá nhân (Họ tên, giới tính, độ tuổi, quê quán,...), trình độ học vấn (trường, ngành, chứng chỉ....), kinh nghiệm liên quan (nơi làm việc, chức danh, nhiệm vụ,...), kỹ năng/ năng lực (....)";Tuyển dụng;"Success factors
Zoho Recruit
Claude AI 
Generative AI (GenAI)";Tối ưu thời gian và nhân lực;"Khả thi, 
Cần tập trung nguồn về 1 email để AI tự đọc mail để lấy CV và các thông tin liên quan";AI ( anh Ân) - GĐ1;30/06/2025;
2,5;Tự động trong Công tác phỏng vấn;"- Lên lịch phỏng vấn: Tự động gửi thư mời phỏng vấn
- Tự động hóa phỏng vấn: Tận dụng AI tạo sinh và AI để (1) tự động hóa từ mô tả công việc để tạo dữ liệu câu hỏi phỏng vấn có liên quan để đánh giá mức độ phù hợp; (2) Hỏi từ ngân hàng câu hỏi.
- AI thực hiện các bài Test năng lực, IQ, EQ...theo yêu cầu (nếu có) của CBLĐ
- AI đánh giá phỏng vấn: phân tích giọng nói, tốc độ trả lời, mức độ tự tin, nội dung câu trả lời để đánh giá kỹ năng của ứng viên và mức độ phỏng vấn
- Tự động lưu lại các video (hoặc chuyển thể voice sang văn bản), ghi âm phỏng vấn để CBLĐ xem xét thêm nếu cần
-  AI phản hồi kết quả phỏng vấn, thông báo bước tiếp theo trong quy trình tuyển dụng (nếu có)";Tuyển dụng;"Zoho Recruit
Duomian AI";Tối ưu thời gian và nhân lực;"Khả thi
- Cần thống nhất tool video call, ví dụ: ms teams
- Ghi chú: tác vụ phân tích video sẽ tốn thời gian, không thể realtime, nên sẽ thực hiện lưu trữ video rồi tổng hợp /phân tích sau (độ trễ dưới 24h)";AI ( anh Ân) - GĐ2;;
2,6;Tự động trong Công tác nhập dữ liệu và hoàn thiện hồ sơ CBNV mới;"- Tự động scan thông tin cá nhân của UV được phê duyệt tuyển dụng (Dữ liệu PD tuyển dụng: chức danh, cấp bậc,lương, địa điểm làm việc, mã ca,...sẽ do CVTD cập nhật)
- Tự động nhắc nhở UV hoàn thiện các thông tin còn thiếu theo Danh mục hồ sơ CBNV mới.
- Tích hợp dữ liệu vào hệ thống SAP.";Tuyển dụng;"Success factors
Zoho Recruit";Tối ưu thời gian và nhân lực;"Chưa rõ nguồn dữ liệu để AI có thể access và phân tích.
Ví dụ form nhập thông tin như CCCD, gia đình,... thì đã có form sẵn. Có thể chỉ cần rule-based để parse thông tin nhập liệu không cần AI
- Còn nếu đọc email, phân tích hình ảnh + doc đính kèm thì rất tốn nguồn lực để triển khai, có thể cân nhắc thực hiện trong các phrase sau";AI ( anh Ân) - GĐ2;;
2,7;"Tự động gửi ""Thư mời làm việc"" và checklist onboarding";"- Tự động gửi ""Thư mời làm việc""  cho CBNV mới theo template được cấu hình trên hệ thống.
 (1) Thông tin đầu mối đón tiếp, thời gian/địa điểm..."" 
 (2) Checklist hướng dẫn onboarding (Nộp hồ sơ, sổ tay nhân viên mới…)
- Tự động nhắc nhở UV ký HĐ thử việc/tập nghề theo 
- Tự động gửi thông báo cho PTD-NS về các TH chưa ký HĐ hoặc từ chối nhận việc để CVNS xóa mã NV trên hệ thống SAP
- Tự động tạo survey đánh giá mức độ hài lòng của UV về trải nghiệm quy trình tuyển dụng";Tuyển dụng;ILVG;Tối ưu thời gian và nhân lực;Khả thi;AI ( anh Ân) - GĐ2;;
2,8;"Tự động gửi ""Thông báo nhân sự mới"" tới BP chuyên môn và các BP liên quan trong công tác chuẩn bị tiếp đón CBNV mới";"- Tự động gửi ""Thông báo CBNV mới"" tới BP chuyên môn và các BP liên quan (PNS, PHC, Lễ tân tòa nhà...)
- Tự động nhắc BP chuyên môn về công tác chuẩn bị trang thiết bị, khu vực làm việc cho CBNV mới";Tuyển dụng;Zoho Recruit;Tối ưu thời gian và nhân lực;Khả thi;AI ( anh Ân) - GĐ2;;
2,9;Tự động xuất dữ liệu báo cáo tuyển dụng;"- Tự động xuất báo cáo dữ liệu UV apply, UV phỏng vấn, UV đạt, UV onboard
- Tự động phân tích hiệu quả tuyển dụng qua số liệu UV từ các kênh tuyển dụng, chi phí tuyển dụng";Tuyển dụng;"Success factors
Zoho Recruit
";Khả năng tổng hợp, phân tích dữ liệu chính xác và nhanh chóng;"Khả thi,
- Có lưu ý cho ý sau:
""- Tự động phân tích hiệu quả tuyển dụng qua số liệu UV từ các kênh tuyển dụng, chi phí tuyển dụng"" -> cần chi tiết hóa metric";AI ( anh Ân) - GĐ2;;
3;Tự động phỏng vấn nghỉ việc (exit interview);Phân tích nội dung phỏng vấn exit interview;Nhân sự;Retorio, HireVue;Tối ưu thời gian và nhân lực;"Chưa hiểu yêu cầu
- Đầu vào cho AI phân tích là dạng gì: Text, hay video, hay audio";AI ( anh Ân) - GĐ2;;
II;Trả lời, cung cấp thông tin - Chatbot;;;;;;;;
1;Chatbox -  Tuyển dụng (cho Ứng viên);Trả lời câu hỏi, cung cấp thông tin  về quy trình tuyển dụng, tiêu chí, mô tả công việc, văn hóa, môi trường làm việc, chính sách phúc lợi (theo quy định do NS cung cấp không theo đường link tới QĐ Nội bộ chung)…website tuyển dụng;Tuyển dụng;"Gemini, Copilot
Perplexity AI";Tối ưu thời gian và nhân lực;"Khả thi
Cần cung cấp các tài liệu liên quan để bot trả lời, có thể ở dạng: pdf, doc, trang wiki";AI ( anh Ân) - GĐ1;"- AI: 15/06/2025
- Web: Chưa có";"Chưa chốt được dùng website nào
Không ảnh hưởng đến thời gian tư vấn và giảm nhân sự"
2;Chatbox - Tuyển dụng - Nhân sự  (cho CBNV);Trả lời các câu hỏi về chính sách, quy định liên quan tới nhân sự, hướng dẫn các thủ tục nhân sự cho CBNV trong quá trình làm việc.;Nhân sự;MeBeBot, Talla;Tối ưu thời gian và nhân lực;"Khả thi
Cần cung cấp các tài liệu liên quan để bot trả lời, có thể ở dạng: pdf, doc, trang wiki";AI ( anh Ân) - GĐ1;"- AI: 15/06/2025
- ILVGR: 
Web: 01/07/2025
App: 01/07/2025";15/06/2025 bên Nhân sự test và dùng thử
3;Chatbot - Học tập;"Trả lời dựa trên bộ Q&A có sẵn hoặc dựa trên tài liệu cung cấp
- Trả lời câu hỏi kiến thức liên quan đến công việc dựa trên các quy trình làm việc/tài liệu đào tạo được cung cấp
- Trả lời câu hỏi liên quan đến công tác đào tạo (ví dụ: sử dụng hệ thống Elearning; tìm kiếm tài liệu; các chỉ tiêu giờ học, giờ dạy...)
- Tư vấn khóa học nên tham gia: Cần tích hợp được với Thư viện/khóa học có sẵn trên Elearning Vinhomes.";Đào tạo;Copilot;Tối ưu thời gian và nhân lực;"Khả thi
Cần cung cấp các tài liệu liên quan để bot trả lời, có thể ở dạng: pdf, doc, trang wiki";AI ( anh Ân) - GĐ1;;Cần xác nhận với nhà thầu LMS Trí Nam
4;Chatbox -  Hành chính, Mua sắm;"- Trả lời các câu hỏi về thủ tục Hành chính (VD: gửi xe, lấy face ID, thanh toán chi phí, CPN, taxi, đặt khách sạn,...)
- Trả lời câu hỏi, cung cấp thông tin về quy trình mua sắm, phần mềm mua sắm (VD: tìm mã hàng phù hợp với yêu cầu của BP, thao tác trên phần mềm mua sắm, các trường hợp ngoại lệ cần xử lý như nào,…)";Hành chính;Copilot;Tối ưu thời gian và nhân lực;"Khả thi
- Cần cung cấp các tài liệu liên quan để bot trả lời, có thể ở dạng: pdf, doc, trong wiki
- Cần cụ thể tính ưu tiên của các chức năng
- Cần tạo kịch bản, để biết cần export API gì cho AI agent sử dụng";AI ( anh Ân) - GĐ1;Q4;"Chưa rõ yêu cầu
Thống nhất với team mua sắm Q4"
III ;Thiết kế tài liệu (slide, video, soạn thảo văn bản);;;;;;;;
1;Thiết kế tài liệu đào tạo;Thiết kế slide từ một nội dung text;Đào tạo;"Kling AI
Elai
Gamma
Napkin
Chat GPT plus";Tối ưu thời gian, nâng cao chất lượng ;;;;
2;;Thiết kế lại từ 1 slide chưa đẹp (vd hình ảnh sử dụng dung lượng thấp, thiếu biểu đồ…);;;;;;;
3;;Xây dựng/vẽ một lộ trình học tập dựa trên bảng excel cung cấp;;;;;;;
4;;"Thiết kế các ấn phẩm truyền thông dựa ý tưởng, mô tả
Ví dụ: Cần thiết kế ấn phẩm truyền thông có text Gen Happy, có hình ảnh khu đô thị Vinhomes, có ánh mặt trời rực rỡ, gia đình 4 người cười vui, hạnh phúc";;;;;;;
5;Xây dựng video;AI dựng thành video, có hình hoạt họa mô phỏng, đọc lời thoại dựa trên dữ liệu đầu vào là có quay sẵn màn hình các thao tác, có text theo từng màn hình ;;;;;;;
6;;AI dựng thành video, mô phỏng theo nội dung mô tả dựa trên các SOP mô tả cụ thể các bước cần thực hiện;;;;;;;
7;;AI chuyển thành video hoạt hình, mô phỏng lại hình animation, có lời thoại dựa trên video quay các thao tác thực tế cho con người thực hiện, có text;;;;;;;
8;;AI dựng video có lời thoại, hình ảnh từ một slide đào tạo;;;;;;;
9;Soạn thảo văn bản;Soạn thảo văn bản theo cấu trúc quy định;Hành chính;"Kling AI
Elai";Tối ưu thời gian và nhân lực;;;;
IV;Thiết kế báo cáo theo mẫu sẵn và báo cáo theo AI;;;;;;;;
1;Thiết kế báo cáo ;- BC cố định như sheet Quy hoạch BC;TD-NS-ĐT;;Tối ưu thời gian và nhân lực;;PTPM (anh Lợi);;
2;Tạo báo cáo động;- Báo cáo động theo yêu cầu ;TD-NS-ĐT;;Tối ưu thời gian và nhân lực;Cần trao đổi thêm làm rõ để bài;AI ( anh Ân) - GĐ1;;
3;Nhận biết vấn đề báo cáo và đưa giải pháp giải quyết;"- Nhận biết và đưa ra các vấn đề bất cập dựa trên số liệu, dữ liệu.
- Đưa ra các giải pháp giải quyết các vấn đề bất cập.";TD-NS-ĐT;;Tối ưu thời gian và nhân lực;Cần trao đổi thêm làm rõ để bài;AI ( anh Ân) - GĐ1;;
